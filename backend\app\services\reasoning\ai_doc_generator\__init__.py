"""
AI智能文档生成器
基于人工智能的文档生成系统，能够自动分析项目特征、智能规划文档章节结构，并根据规划逐章节生成个性化内容
"""

# 导入核心数据模型
from .models import (
    DocumentGenerationConfig,
    ProjectProfile,
    ContentRequirement,
    DocumentPlan,
    GenerationTask,
    QualityMetrics,
    GenerationResult,
    DocumentOutput,
    SystemStatus
)

# 导入核心组件
from .core import AIOrchestrator
from .config import AdaptiveConfig
from .ai_engines.agents import PlanningAgent
from .sections.base import IntelligentSection
from .sections.standard import SmartIntroduction
from .handlers import IntelligentDocHandler

__version__ = "0.1.0"

__all__ = [
    # 版本信息
    "__version__",

    # 核心数据模型
    "DocumentGenerationConfig",
    "ProjectProfile",
    "ContentRequirement",
    "DocumentPlan",
    "GenerationTask",
    "QualityMetrics",
    "GenerationResult",
    "DocumentOutput",
    "SystemStatus",

    # 核心组件
    "AIOrchestrator",
    "AdaptiveConfig",
    "PlanningAgent",
    "IntelligentSection",
    "SmartIntroduction",
    "IntelligentDocHandler"
]
