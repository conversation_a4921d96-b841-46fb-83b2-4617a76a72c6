# 智能文档生成系统

一个基于AI的智能文档生成系统，提供从项目分析到文档生成的完整解决方案。

## 主要功能

### 🎯 智能文档规划
- 基于项目特征自动分析和规划文档结构
- 支持多种规划策略：自适应、模板化、领域特定、用户定义
- 智能识别项目类型和目标受众
- 自动优化章节组织和依赖关系

### 📝 多策略内容生成
- **结构化生成**：适用于API文档、配置说明等正式文档
- **叙述性生成**：适用于介绍、教程等流畅易读的内容
- **技术性生成**：适用于架构说明、开发指南等专业文档
- **创意性生成**：适用于营销材料、产品亮点等吸引人的内容

### 🔧 智能内容优化
- 自动质量评估和改进建议
- 风格统一和语言优化
- 内容一致性检查和修正
- 多轮迭代优化直至达到质量标准

### 📄 多格式输出
- 支持Markdown、HTML、PDF等多种输出格式
- 智能格式转换和样式适配
- 可定制的模板和样式
- 批量导出和发布功能

### 🔄 工作流管理
- 完整的流水线管理和监控
- 支持串行、并行和混合执行模式
- 错误处理和重试机制
- 实时状态跟踪和性能监控

## 系统架构

```
智能文档生成系统
├── 核心组件 (core/)
│   ├── DocumentPlanner - 文档规划器
│   ├── ContentGenerator - 内容生成器
│   └── ContentOptimizer - 内容优化器
├── 生成器 (generators/)
│   ├── StructuredGenerator - 结构化生成器
│   ├── NarrativeGenerator - 叙述性生成器
│   ├── TechnicalGenerator - 技术性生成器
│   └── CreativeGenerator - 创意性生成器
├── 工作流 (workflows/)
│   ├── PlanningWorkflow - 规划工作流
│   ├── GenerationWorkflow - 生成工作流
│   ├── RefinementWorkflow - 优化工作流
│   └── PipelineManager - 流水线管理器
├── 智能体 (agents/)
│   ├── PlannerAgent - 规划智能体
│   ├── WriterAgent - 写作智能体
│   ├── ReviewerAgent - 审阅智能体
│   └── CoordinatorAgent - 协调智能体
├── 数据模型 (models/)
│   ├── planning_models - 规划相关模型
│   ├── generation_models - 生成相关模型
│   └── workflow_models - 工作流相关模型
├── 配置管理 (config/)
│   ├── GeneratorConfig - 生成器配置
│   ├── TemplateConfig - 模板配置
│   └── WorkflowConfig - 工作流配置
├── 模板库 (templates/)
│   ├── planning_templates - 规划模板
│   ├── generation_templates - 生成模板
│   ├── refinement_templates - 优化模板
│   └── evaluation_templates - 评估模板
└── 工具集 (utils/)
    ├── TextProcessor - 文本处理工具
    ├── FormatConverter - 格式转换工具
    ├── ContentAnalyzer - 内容分析工具
    └── ValidationUtils - 验证工具
```

## 快速开始

### 基本使用

```python
from intelligent_doc_generator import IntelligentDocGenerator

# 创建生成器实例
generator = IntelligentDocGenerator()

# 生成完整文档
result = await generator.generate_document(
    project_path="/path/to/your/project",
    project_name="My Awesome Project",
    config={
        "strategy": "adaptive",
        "quality_level": "high",
        "output_format": "markdown",
        "target_audience": "developers"
    }
)

# 获取生成的文档
document = result["document"]
metadata = result["metadata"]
statistics = result["statistics"]
```

### 分步骤使用

```python
# 1. 仅规划文档结构
plan = await generator.plan_document(
    project_path="/path/to/project",
    project_name="My Project",
    planning_config={
        "strategy": "adaptive",
        "max_sections": 15,
        "target_audience": "developers"
    }
)

# 2. 基于规划生成内容
content_results = await generator.generate_content(
    document_plan=plan,
    generation_config={
        "parallel_generation": True,
        "quality_threshold": 0.8
    }
)

# 3. 优化生成的内容
optimized_results = await generator.optimize_content(
    generation_results=content_results,
    optimization_config={
        "target_quality": 0.9,
        "enable_style_refinement": True
    }
)
```

### 内容分析和格式转换

```python
# 分析内容质量
analysis = generator.analyze_content(content)
print(f"质量分数: {analysis['quality']['overall_score']}")
print(f"建议: {analysis['quality']['recommendations']}")

# 格式转换
html_content = generator.convert_format(
    content=markdown_content,
    source_format="markdown",
    target_format="html",
    options={
        "include_toc": True,
        "include_metadata": True
    }
)
```

## 配置选项

### 规划配置

```python
planning_config = {
    "strategy": "adaptive",  # adaptive, template_based, domain_specific, user_defined
    "project_type": "web_application",
    "target_audience": "developers",
    "max_sections": 20,
    "preferred_length": 5000,
    "documentation_style": "technical",
    "language": "zh-CN",
    "custom_sections": ["部署指南", "故障排除"],
    "excluded_sections": ["法律声明"],
    "required_sections": ["快速开始", "API参考"]
}
```

### 生成配置

```python
generation_config = {
    "parallel_generation": True,
    "max_concurrent_tasks": 5,
    "quality_threshold": 0.7,
    "enable_validation": True,
    "auto_retry_failed": True,
    "writing_style": "professional",
    "tone": "friendly",
    "complexity_level": "medium"
}
```

### 优化配置

```python
optimization_config = {
    "enable_quality_improvement": True,
    "enable_style_refinement": True,
    "enable_consistency_check": True,
    "target_quality": 0.8,
    "target_style": "professional",
    "max_iterations": 3
}
```

## 高级功能

### 自定义生成器

```python
from intelligent_doc_generator.generators import BaseGenerator

class CustomGenerator(BaseGenerator):
    def __init__(self):
        super().__init__()
        self.name = "CustomGenerator"
    
    async def _process(self, input_data):
        # 实现自定义生成逻辑
        pass
```

### 自定义工作流

```python
from intelligent_doc_generator.workflows import WorkflowConfig, StageConfig

# 创建自定义工作流
custom_stages = [
    StageConfig(
        name="custom_analysis",
        timeout=120,
        parameters={"analysis_type": "deep"}
    ),
    StageConfig(
        name="custom_generation",
        dependencies=["custom_analysis"],
        timeout=300
    )
]

workflow_config = WorkflowConfig()
custom_pipeline = workflow_config.create_custom_pipeline(
    name="custom_pipeline",
    description="自定义文档生成流水线",
    stages=custom_stages
)
```

### 模板定制

```python
from intelligent_doc_generator.config import TemplateConfig

template_config = TemplateConfig()

# 获取现有模板
template = template_config.get_template_config("structured_generation")

# 验证模板参数
validation_result = template_config.validate_template_parameters(
    "structured_generation",
    {"section_type": "api_reference", "project_name": "My API"}
)
```

## 监控和调试

### 系统状态监控

```python
# 获取系统状态
status = generator.get_system_status()
print(f"系统版本: {status['version']}")
print(f"组件状态: {status['components']}")

# 获取生成统计
stats = generator.get_generation_statistics()
print(f"成功率: {stats['success_rate']:.2%}")
print(f"平均处理时间: {stats['average_processing_time']:.2f}秒")
```

### 工作流状态跟踪

```python
# 获取流水线状态
pipeline_status = generator.pipeline_manager.get_pipeline_status()
print(f"当前阶段: {pipeline_status['current_stage']}")
print(f"执行状态: {pipeline_status['status']}")

# 获取详细步骤信息
for step in pipeline_status['steps']:
    print(f"步骤: {step['name']}, 状态: {step['status']}")
```

## 最佳实践

### 1. 配置优化
- 根据项目类型选择合适的规划策略
- 设置合理的质量阈值和超时时间
- 启用并行生成以提高效率

### 2. 质量控制
- 使用多轮优化提升内容质量
- 定期分析生成内容并调整配置
- 建立质量评估标准和流程

### 3. 性能优化
- 合理设置并发任务数量
- 启用缓存机制减少重复计算
- 监控系统资源使用情况

### 4. 错误处理
- 配置适当的重试策略
- 实现优雅的降级机制
- 建立完善的日志和监控

## 故障排除

### 常见问题

**Q: 生成的文档质量不理想怎么办？**
A: 可以尝试以下方法：
- 调整质量阈值和优化配置
- 使用更适合的生成策略
- 增加优化迭代次数
- 检查输入数据的质量

**Q: 生成速度太慢怎么优化？**
A: 可以考虑：
- 启用并行生成
- 增加并发任务数量
- 启用缓存机制
- 优化模板和配置

**Q: 如何自定义文档结构？**
A: 可以通过以下方式：
- 使用用户定义的规划策略
- 指定自定义章节列表
- 创建自定义模板
- 实现自定义生成器

## 版本信息

- **当前版本**: 1.0.0
- **Python要求**: >= 3.8
- **依赖框架**: LangChain, Pydantic, AsyncIO

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎贡献代码和建议！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至 <EMAIL>
- 查看文档和示例
