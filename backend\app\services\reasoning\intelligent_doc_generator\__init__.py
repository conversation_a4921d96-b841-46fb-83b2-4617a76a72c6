"""
智能文档生成器

AI驱动的智能文档生成系统，支持：
- 自适应文档结构规划
- 上下文感知内容生成  
- 多样化内容风格
- 质量控制与评估
- 增量内容更新
"""

from .intelligent_doc_generator import IntelligentDocumentGenerator
from .core.doc_planner import DocumentPlanner
from .core.content_generator import ContentGenerator
from .agents.coordinator_agent import CoordinatorAgent

# 导入核心组件
from .core import (
    DocumentPlanner, ContentGenerator, TemplateManager, 
    ContextAnalyzer, QualityController,
    ProjectFeatures, QualityReport
)

# 导入数据模型
from .models import (
    IntelligentDocument, DynamicSection, DocumentVersion,
    DocumentPlan, SectionPlan, PlanningContext,
    GenerationTask, GenerationContext, GenerationResult,
    WorkflowStep, WorkflowState, WorkflowExecution
)

__version__ = "1.0.0"
__author__ = "AI Documentation Team"

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    
    # 核心组件
    "DocumentPlanner",
    "ContentGenerator", 
    "TemplateManager",
    "ContextAnalyzer",
    "QualityController",
    
    # 数据结构
    "ProjectFeatures",
    "QualityReport",
    
    # 文档模型
    "IntelligentDocument",
    "DynamicSection", 
    "DocumentVersion",
    
    # 规划模型
    "DocumentPlan",
    "SectionPlan",
    "PlanningContext",
    
    # 生成模型
    "GenerationTask",
    "GenerationContext", 
    "GenerationResult",
    
    # 工作流模型
    "WorkflowStep",
    "WorkflowState",
    "WorkflowExecution",
    
    # 主文档生成器
    "IntelligentDocumentGenerator",
    "CoordinatorAgent"
]
