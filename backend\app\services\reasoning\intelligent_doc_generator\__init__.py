"""
智能文档生成系统

一个基于AI的智能文档生成系统，提供从项目分析到文档生成的完整解决方案。

主要功能：
- 智能文档规划：基于项目特征自动规划文档结构
- 多策略内容生成：支持结构化、叙述性、技术性、创意性等多种生成策略
- 智能内容优化：自动优化内容质量、风格和一致性
- 多格式输出：支持Markdown、HTML、PDF等多种输出格式
- 工作流管理：提供完整的流水线管理和监控

核心组件：
- DocumentPlanner: 文档规划器
- ContentGenerator: 内容生成器
- ContentOptimizer: 内容优化器
- PipelineManager: 流水线管理器
- IntelligentDocGenerator: 主要接口类

使用示例：
    from intelligent_doc_generator import IntelligentDocGenerator

    generator = IntelligentDocGenerator()
    result = await generator.generate_document(
        project_path="/path/to/project",
        project_name="My Project",
        config={
            "strategy": "adaptive",
            "quality_level": "high",
            "output_format": "markdown"
        }
    )
"""

import logging
from typing import Dict, Any, Optional

# 核心组件导入
from .core.doc_planner import DocumentPlanner
from .core.content_generator import ContentGenerator
from .core.content_optimizer import ContentOptimizer

# 生成器导入
from .generators import (
    BaseGenerator,
    StructuredGenerator,
    NarrativeGenerator,
    TechnicalGenerator,
    CreativeGenerator
)

# 工作流导入
from .workflows import (
    PlanningWorkflow,
    GenerationWorkflow,
    RefinementWorkflow,
    PipelineManager
)

# 智能体导入
from .agents import (
    PlannerAgent,
    WriterAgent,
    ReviewerAgent,
    CoordinatorAgent
)

# 模型导入
from .models.planning_models import DocumentPlan, SectionPlan, PlanningContext
from .models.generation_models import GenerationRequest, GenerationResult
from .models.workflow_models import WorkflowStep, WorkflowResult

# 配置导入
from .config import GeneratorConfig, TemplateConfig, WorkflowConfig

# 工具导入
from .utils import TextProcessor, FormatConverter, ContentAnalyzer, ValidationUtils

# 主要接口类导入
from .intelligent_doc_generator import IntelligentDocGenerator

# 版本信息
__version__ = "1.0.0"
__author__ = "Intelligent Doc Generator Team"
__email__ = "<EMAIL>"

# 设置日志
logger = logging.getLogger(__name__)


# 导出主要类和函数
__all__ = [
    # 主要接口
    "IntelligentDocGenerator",

    # 核心组件
    "DocumentPlanner",
    "ContentGenerator",
    "ContentOptimizer",

    # 生成器
    "BaseGenerator",
    "StructuredGenerator",
    "NarrativeGenerator",
    "TechnicalGenerator",
    "CreativeGenerator",

    # 工作流
    "PlanningWorkflow",
    "GenerationWorkflow",
    "RefinementWorkflow",
    "PipelineManager",

    # 智能体
    "PlannerAgent",
    "WriterAgent",
    "ReviewerAgent",
    "CoordinatorAgent",

    # 模型
    "DocumentPlan",
    "SectionPlan",
    "PlanningContext",
    "GenerationRequest",
    "GenerationResult",
    "WorkflowStep",
    "WorkflowResult",

    # 配置
    "GeneratorConfig",
    "TemplateConfig",
    "WorkflowConfig",

    # 工具
    "TextProcessor",
    "FormatConverter",
    "ContentAnalyzer",
    "ValidationUtils",

    # 版本信息
    "__version__",
    "__author__",
    "__email__"
]
