"""
协调智能体

负责协调整个智能文档生成过程的AI智能体，包括：
- 流程协调
- 任务分配
- 质量监控
- 结果整合
"""

import uuid
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple

from ...ai_agent_core import BaseAgent, AgentInput, AgentOutput, AgentManager
from .planner_agent import PlannerAgent
from .writer_agent import WriterAgent
from .reviewer_agent import ReviewerAgent
from ..workflows.pipeline_manager import PipelineManager

logger = logging.getLogger(__name__)


@AgentManager.register("DocumentCoordinatorAgent")
class CoordinatorAgent(BaseAgent):
    """文档生成协调智能体"""
    
    def __init__(self):
        """初始化协调智能体"""
        super().__init__()
        self.name = "DocumentCoordinatorAgent"
        
        # 初始化子智能体
        self.planner_agent = PlannerAgent()
        self.writer_agent = WriterAgent()
        self.reviewer_agent = ReviewerAgent()
        
        # 初始化流水线管理器
        self.pipeline_manager = PipelineManager()
        
        # 协调配置
        self.coordination_config = {
            "max_retry_attempts": 3,
            "quality_threshold": 0.8,
            "parallel_processing": True,
            "auto_review": True,
            "auto_refinement": True
        }
        
    async def _initialize(self) -> None:
        """初始化协调智能体特定资源"""
        logger.info("初始化文档生成协调智能体特定资源")
        
        # 初始化子智能体
        await self.planner_agent.initialize()
        await self.writer_agent.initialize()
        await self.reviewer_agent.initialize()
        
    async def _create_chain(self) -> None:
        """创建协调处理链"""
        try:
            # 协调智能体不需要单独的AI链，主要负责流程协调
            logger.info("文档生成协调处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建文档生成协调处理链失败: {str(e)}")
            raise
    
    async def _process(self, input_data: AgentInput) -> Tuple[Any, Dict[str, Any]]:
        """处理协调请求"""
        try:
            # 解析协调参数
            coordination_params = self._parse_coordination_parameters(input_data.parameters)
            
            # 验证协调参数
            validation_result = await self._validate_coordination_parameters(coordination_params)
            if not validation_result["is_valid"]:
                return None, {
                    "error": f"协调参数验证失败: {validation_result['errors']}",
                    "validation_result": validation_result
                }
            
            # 执行协调流程
            coordination_result = await self._execute_coordination_workflow(coordination_params)
            
            # 生成协调报告
            coordination_report = await self._generate_coordination_report(coordination_result, coordination_params)
            
            return coordination_result, {
                "coordination_id": coordination_result.get("coordination_id"),
                "workflow_type": coordination_params.get("workflow_type"),
                "total_sections": coordination_result.get("total_sections", 0),
                "success_rate": coordination_result.get("success_rate", 0),
                "overall_quality": coordination_result.get("overall_quality", 0),
                "processing_time": (datetime.now(timezone.utc) - input_data.timestamp).total_seconds()
            }
            
        except Exception as e:
            logger.error(f"协调处理失败: {str(e)}")
            return None, {"error": str(e)}
    
    def _parse_coordination_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """解析协调参数"""
        return {
            "workflow_type": parameters.get("workflow_type", "full_pipeline"),
            "project_path": parameters.get("project_path", ""),
            "project_name": parameters.get("project_name", ""),
            "coordination_config": parameters.get("coordination_config", {}),
            "planning_config": parameters.get("planning_config", {}),
            "generation_config": parameters.get("generation_config", {}),
            "review_config": parameters.get("review_config", {}),
            "quality_requirements": parameters.get("quality_requirements", {}),
            "performance_requirements": parameters.get("performance_requirements", {}),
            "custom_workflow": parameters.get("custom_workflow", [])
        }
    
    async def _validate_coordination_parameters(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """验证协调参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证工作流类型
        valid_workflow_types = ["full_pipeline", "planning_only", "generation_only", "review_only", "custom"]
        if coordination_params.get("workflow_type") not in valid_workflow_types:
            validation_result["errors"].append(f"无效的工作流类型: {coordination_params.get('workflow_type')}")
            validation_result["is_valid"] = False
        
        # 验证项目信息
        if coordination_params.get("workflow_type") in ["full_pipeline", "planning_only"]:
            if not coordination_params.get("project_name"):
                validation_result["errors"].append("项目名称不能为空")
                validation_result["is_valid"] = False
        
        # 验证配置完整性
        required_configs = {
            "full_pipeline": ["planning_config", "generation_config"],
            "planning_only": ["planning_config"],
            "generation_only": ["generation_config"],
            "review_only": ["review_config"]
        }
        
        workflow_type = coordination_params.get("workflow_type")
        if workflow_type in required_configs:
            for config_name in required_configs[workflow_type]:
                if not coordination_params.get(config_name):
                    validation_result["warnings"].append(f"缺少{config_name}配置")
        
        return validation_result
    
    async def _execute_coordination_workflow(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行协调工作流"""
        workflow_type = coordination_params.get("workflow_type")
        
        if workflow_type == "full_pipeline":
            return await self._execute_full_pipeline(coordination_params)
        elif workflow_type == "planning_only":
            return await self._execute_planning_only(coordination_params)
        elif workflow_type == "generation_only":
            return await self._execute_generation_only(coordination_params)
        elif workflow_type == "review_only":
            return await self._execute_review_only(coordination_params)
        elif workflow_type == "custom":
            return await self._execute_custom_workflow(coordination_params)
        else:
            raise ValueError(f"不支持的工作流类型: {workflow_type}")
    
    async def _execute_full_pipeline(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行完整流水线"""
        try:
            logger.info("执行完整文档生成流水线")
            
            # 准备流水线配置
            pipeline_config = {
                "planning": coordination_params.get("planning_config", {}),
                "generation": coordination_params.get("generation_config", {}),
                "refinement": coordination_params.get("review_config", {}),
                "enable_refinement": coordination_params.get("coordination_config", {}).get("auto_review", True)
            }
            
            # 执行流水线
            pipeline_result = await self.pipeline_manager.execute_pipeline(
                coordination_params["project_path"],
                coordination_params["project_name"],
                pipeline_config
            )
            
            # 处理流水线结果
            coordination_result = {
                "coordination_id": str(uuid.uuid4()),
                "workflow_type": "full_pipeline",
                "pipeline_result": pipeline_result,
                "pipeline_status": self.pipeline_manager.get_pipeline_status(),
                "total_sections": len(pipeline_result.get("document", {})),
                "success_rate": 1.0,  # 流水线成功完成
                "overall_quality": pipeline_result.get("statistics", {}).get("average_quality", 0),
                "execution_time": pipeline_result.get("pipeline_info", {}).get("execution_time", 0),
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"完整流水线执行失败: {str(e)}")
            raise
    
    async def _execute_planning_only(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行仅规划工作流"""
        try:
            logger.info("执行仅规划工作流")
            
            # 准备规划输入
            planning_input = AgentInput(
                request_id=str(uuid.uuid4()),
                parameters=coordination_params.get("planning_config", {}),
                context={},
                timestamp=datetime.now(timezone.utc)
            )
            
            # 执行规划
            planning_output = await self.planner_agent.process(planning_input)
            
            coordination_result = {
                "coordination_id": str(uuid.uuid4()),
                "workflow_type": "planning_only",
                "planning_result": planning_output.response.result,
                "planning_metadata": planning_output.response.metadata,
                "total_sections": len(planning_output.response.result.get("sections", [])) if planning_output.response.result else 0,
                "success_rate": 1.0 if not planning_output.response.has_error else 0.0,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"仅规划工作流执行失败: {str(e)}")
            raise
    
    async def _execute_generation_only(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行仅生成工作流"""
        try:
            logger.info("执行仅生成工作流")
            
            generation_config = coordination_params.get("generation_config", {})
            sections_to_generate = generation_config.get("sections", [])
            
            generation_results = {}
            
            # 并发或串行生成内容
            if self.coordination_config.get("parallel_processing", True):
                tasks = []
                for section_config in sections_to_generate:
                    task = self._generate_single_section(section_config)
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, result in enumerate(results):
                    section_id = sections_to_generate[i].get("section_id", f"section_{i}")
                    if not isinstance(result, Exception):
                        generation_results[section_id] = result
            else:
                for section_config in sections_to_generate:
                    section_id = section_config.get("section_id", str(uuid.uuid4()))
                    try:
                        result = await self._generate_single_section(section_config)
                        generation_results[section_id] = result
                    except Exception as e:
                        logger.error(f"章节 {section_id} 生成失败: {str(e)}")
            
            coordination_result = {
                "coordination_id": str(uuid.uuid4()),
                "workflow_type": "generation_only",
                "generation_results": generation_results,
                "total_sections": len(sections_to_generate),
                "generated_sections": len(generation_results),
                "success_rate": len(generation_results) / len(sections_to_generate) if sections_to_generate else 0,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"仅生成工作流执行失败: {str(e)}")
            raise
    
    async def _execute_review_only(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行仅审阅工作流"""
        try:
            logger.info("执行仅审阅工作流")
            
            review_config = coordination_params.get("review_config", {})
            content_to_review = review_config.get("content", {})
            
            # 使用审阅智能体审阅多个章节
            review_results = await self.reviewer_agent.review_multiple_sections(
                content_to_review, review_config
            )
            
            # 计算整体质量
            total_score = 0
            valid_reviews = 0
            
            for section_id, review_data in review_results.items():
                if review_data.get("result") and "overall_score" in review_data["result"]:
                    total_score += review_data["result"]["overall_score"]
                    valid_reviews += 1
            
            overall_quality = total_score / valid_reviews if valid_reviews > 0 else 0
            
            coordination_result = {
                "coordination_id": str(uuid.uuid4()),
                "workflow_type": "review_only",
                "review_results": review_results,
                "total_sections": len(content_to_review),
                "reviewed_sections": len(review_results),
                "overall_quality": overall_quality,
                "success_rate": len(review_results) / len(content_to_review) if content_to_review else 0,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"仅审阅工作流执行失败: {str(e)}")
            raise
    
    async def _execute_custom_workflow(self, coordination_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行自定义工作流"""
        try:
            logger.info("执行自定义工作流")
            
            custom_workflow = coordination_params.get("custom_workflow", [])
            workflow_results = []
            
            for step in custom_workflow:
                step_result = await self._execute_custom_step(step)
                workflow_results.append(step_result)
            
            coordination_result = {
                "coordination_id": str(uuid.uuid4()),
                "workflow_type": "custom",
                "workflow_results": workflow_results,
                "total_steps": len(custom_workflow),
                "completed_steps": len(workflow_results),
                "success_rate": len(workflow_results) / len(custom_workflow) if custom_workflow else 0,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            return coordination_result
            
        except Exception as e:
            logger.error(f"自定义工作流执行失败: {str(e)}")
            raise

    async def _generate_single_section(self, section_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成单个章节"""
        # 准备写作输入
        writing_input = AgentInput(
            request_id=str(uuid.uuid4()),
            parameters=section_config,
            context={},
            timestamp=datetime.now(timezone.utc)
        )

        # 执行写作
        writing_output = await self.writer_agent.process(writing_input)

        return writing_output.response.result

    async def _execute_custom_step(self, step_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行自定义步骤"""
        step_type = step_config.get("type", "unknown")

        if step_type == "planning":
            return await self._execute_planning_step(step_config)
        elif step_type == "generation":
            return await self._execute_generation_step(step_config)
        elif step_type == "review":
            return await self._execute_review_step(step_config)
        else:
            logger.warning(f"未知的自定义步骤类型: {step_type}")
            return {"type": step_type, "status": "skipped", "reason": "unknown_type"}

    async def _execute_planning_step(self, step_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行规划步骤"""
        planning_input = AgentInput(
            request_id=str(uuid.uuid4()),
            parameters=step_config.get("parameters", {}),
            context={},
            timestamp=datetime.now(timezone.utc)
        )

        planning_output = await self.planner_agent.process(planning_input)

        return {
            "type": "planning",
            "status": "completed" if not planning_output.response.has_error else "failed",
            "result": planning_output.response.result,
            "metadata": planning_output.response.metadata
        }

    async def _execute_generation_step(self, step_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行生成步骤"""
        generation_input = AgentInput(
            request_id=str(uuid.uuid4()),
            parameters=step_config.get("parameters", {}),
            context={},
            timestamp=datetime.now(timezone.utc)
        )

        generation_output = await self.writer_agent.process(generation_input)

        return {
            "type": "generation",
            "status": "completed" if not generation_output.response.has_error else "failed",
            "result": generation_output.response.result,
            "metadata": generation_output.response.metadata
        }

    async def _execute_review_step(self, step_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行审阅步骤"""
        review_input = AgentInput(
            request_id=str(uuid.uuid4()),
            parameters=step_config.get("parameters", {}),
            context={},
            timestamp=datetime.now(timezone.utc)
        )

        review_output = await self.reviewer_agent.process(review_input)

        return {
            "type": "review",
            "status": "completed" if not review_output.response.has_error else "failed",
            "result": review_output.response.result,
            "metadata": review_output.response.metadata
        }

    async def _generate_coordination_report(
        self,
        coordination_result: Dict[str, Any],
        coordination_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成协调报告"""
        report = {
            "coordination_summary": {
                "coordination_id": coordination_result.get("coordination_id"),
                "workflow_type": coordination_result.get("workflow_type"),
                "execution_status": "completed",
                "total_processing_time": coordination_result.get("execution_time", 0),
                "success_rate": coordination_result.get("success_rate", 0),
                "overall_quality": coordination_result.get("overall_quality", 0)
            },
            "performance_metrics": {
                "sections_processed": coordination_result.get("total_sections", 0),
                "successful_sections": coordination_result.get("generated_sections", 0),
                "failed_sections": coordination_result.get("total_sections", 0) - coordination_result.get("generated_sections", 0),
                "average_processing_time_per_section": 0
            },
            "quality_metrics": {
                "overall_quality_score": coordination_result.get("overall_quality", 0),
                "quality_threshold_met": coordination_result.get("overall_quality", 0) >= self.coordination_config.get("quality_threshold", 0.8),
                "sections_above_threshold": 0,
                "sections_below_threshold": 0
            },
            "recommendations": [],
            "next_steps": []
        }

        # 计算平均处理时间
        if coordination_result.get("total_sections", 0) > 0 and coordination_result.get("execution_time", 0) > 0:
            report["performance_metrics"]["average_processing_time_per_section"] = (
                coordination_result["execution_time"] / coordination_result["total_sections"]
            )

        # 生成建议
        if coordination_result.get("success_rate", 0) < 1.0:
            report["recommendations"].append("部分章节生成失败，建议检查配置和输入数据")

        if coordination_result.get("overall_quality", 0) < self.coordination_config.get("quality_threshold", 0.8):
            report["recommendations"].append("整体质量未达标，建议启用自动审阅和优化")

        # 生成后续步骤
        if coordination_params.get("workflow_type") == "planning_only":
            report["next_steps"].append("执行内容生成阶段")
        elif coordination_params.get("workflow_type") == "generation_only":
            report["next_steps"].append("执行内容审阅和优化")

        return report

    def get_coordination_status(self) -> Dict[str, Any]:
        """获取协调状态"""
        return {
            "coordinator_id": self.name,
            "sub_agents_status": {
                "planner": self.planner_agent.initialized,
                "writer": self.writer_agent.initialized,
                "reviewer": self.reviewer_agent.initialized
            },
            "pipeline_status": self.pipeline_manager.get_pipeline_status(),
            "coordination_config": self.coordination_config
        }
