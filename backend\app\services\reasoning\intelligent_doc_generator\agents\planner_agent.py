"""
规划智能体

负责智能文档规划的AI智能体，包括：
- 项目分析
- 需求理解
- 结构规划
- 方案评估
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple

from ...ai_agent_core import BaseAgent, AgentInput, AgentOutput, AgentManager
from ..models.planning_models import DocumentPlan, PlanningContext, PlanningStrategy
from ..templates.planning_templates import PlanningTemplates, PlanningTemplateType

logger = logging.getLogger(__name__)


@AgentManager.register("DocumentPlannerAgent")
class PlannerAgent(BaseAgent):
    """文档规划智能体"""
    
    def __init__(self):
        """初始化规划智能体"""
        super().__init__()
        self.name = "DocumentPlannerAgent"
        self.planning_templates = PlanningTemplates()
        
    async def _initialize(self) -> None:
        """初始化规划智能体特定资源"""
        logger.info("初始化文档规划智能体特定资源")
        
    async def _create_chain(self) -> None:
        """创建规划处理链"""
        try:
            # 使用自适应规划模板作为默认模板
            planning_template = self.planning_templates.get_adaptive_planning_template()
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import JsonOutputParser
            
            prompt = PromptTemplate.from_template(planning_template)
            self.chain = prompt | self.llm | JsonOutputParser()
            
            logger.info("文档规划处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建文档规划处理链失败: {str(e)}")
            raise
    
    async def _process(self, input_data: AgentInput) -> Tuple[Any, Dict[str, Any]]:
        """处理规划请求"""
        try:
            # 解析规划参数
            planning_params = self._parse_planning_parameters(input_data.parameters)
            
            # 验证规划参数
            validation_result = await self._validate_planning_parameters(planning_params)
            if not validation_result["is_valid"]:
                return None, {
                    "error": f"规划参数验证失败: {validation_result['errors']}",
                    "validation_result": validation_result
                }
            
            # 执行智能规划
            planning_result = await self._execute_intelligent_planning(planning_params)
            
            # 评估规划质量
            evaluation_result = await self._evaluate_planning_quality(planning_result, planning_params)
            
            return planning_result, {
                "planning_id": planning_result.get("plan_id"),
                "strategy": planning_params.get("strategy", "adaptive"),
                "sections_count": len(planning_result.get("sections", [])),
                "evaluation": evaluation_result,
                "processing_time": (datetime.now(timezone.utc) - input_data.timestamp).total_seconds()
            }
            
        except Exception as e:
            logger.error(f"规划处理失败: {str(e)}")
            return None, {"error": str(e)}
    
    def _parse_planning_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """解析规划参数"""
        return {
            "project_name": parameters.get("project_name", "未知项目"),
            "project_type": parameters.get("project_type", "software"),
            "primary_language": parameters.get("primary_language", "Python"),
            "target_audience": parameters.get("target_audience", "developers"),
            "strategy": parameters.get("strategy", "adaptive"),
            "max_sections": parameters.get("max_sections", 20),
            "preferred_length": parameters.get("preferred_length", 5000),
            "time_constraints": parameters.get("time_constraints"),
            "structure_analysis": parameters.get("structure_analysis", {}),
            "dependency_analysis": parameters.get("dependency_analysis", {}),
            "module_analysis": parameters.get("module_analysis", {}),
            "custom_requirements": parameters.get("custom_requirements", []),
            "excluded_sections": parameters.get("excluded_sections", []),
            "required_sections": parameters.get("required_sections", [])
        }
    
    async def _validate_planning_parameters(self, planning_params: Dict[str, Any]) -> Dict[str, Any]:
        """验证规划参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证必需参数
        required_params = ["project_name", "project_type"]
        for param in required_params:
            if not planning_params.get(param):
                validation_result["errors"].append(f"缺少必需参数: {param}")
                validation_result["is_valid"] = False
        
        # 验证数值参数
        if planning_params.get("max_sections", 0) <= 0:
            validation_result["warnings"].append("最大章节数应大于0")
        
        if planning_params.get("preferred_length", 0) <= 0:
            validation_result["warnings"].append("首选长度应大于0")
        
        # 验证策略
        valid_strategies = ["adaptive", "template_based", "domain_specific", "user_defined"]
        if planning_params.get("strategy") not in valid_strategies:
            validation_result["warnings"].append(f"未知的规划策略: {planning_params.get('strategy')}")
        
        return validation_result
    
    async def _execute_intelligent_planning(self, planning_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能规划"""
        try:
            # 选择规划模板
            template_type = self._select_planning_template(planning_params)
            template = self.planning_templates.get_template(template_type)
            
            # 准备规划输入
            planning_input = self._prepare_planning_input(planning_params)
            
            # 调用AI进行规划
            planning_result = await self.chain.ainvoke(planning_input)
            
            # 后处理规划结果
            processed_result = await self._post_process_planning_result(planning_result, planning_params)
            
            return processed_result
            
        except Exception as e:
            logger.error(f"智能规划执行失败: {str(e)}")
            raise
    
    def _select_planning_template(self, planning_params: Dict[str, Any]) -> PlanningTemplateType:
        """选择规划模板"""
        strategy = planning_params.get("strategy", "adaptive")
        
        template_mapping = {
            "adaptive": PlanningTemplateType.ADAPTIVE,
            "template_based": PlanningTemplateType.TEMPLATE_BASED,
            "domain_specific": PlanningTemplateType.DOMAIN_SPECIFIC,
            "user_defined": PlanningTemplateType.ADAPTIVE  # 默认使用自适应
        }
        
        return template_mapping.get(strategy, PlanningTemplateType.ADAPTIVE)
    
    def _prepare_planning_input(self, planning_params: Dict[str, Any]) -> Dict[str, Any]:
        """准备规划输入"""
        return {
            "project_name": planning_params["project_name"],
            "project_type": planning_params["project_type"],
            "primary_language": planning_params["primary_language"],
            "target_audience": planning_params["target_audience"],
            "max_sections": planning_params["max_sections"],
            "preferred_length": planning_params["preferred_length"],
            "time_constraints": planning_params.get("time_constraints"),
            "structure_analysis": planning_params.get("structure_analysis", {}),
            "dependency_analysis": planning_params.get("dependency_analysis", {}),
            "module_analysis": planning_params.get("module_analysis", {})
        }
    
    async def _post_process_planning_result(
        self,
        planning_result: Dict[str, Any],
        planning_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """后处理规划结果"""
        # 确保结果包含必要字段
        if "sections" not in planning_result:
            planning_result["sections"] = []
        
        # 为每个章节添加唯一ID
        for i, section in enumerate(planning_result["sections"]):
            if "section_id" not in section:
                section["section_id"] = f"section_{i+1}"
        
        # 添加规划元数据
        planning_result["plan_id"] = str(uuid.uuid4())
        planning_result["created_at"] = datetime.now(timezone.utc).isoformat()
        planning_result["strategy"] = planning_params.get("strategy", "adaptive")
        planning_result["project_info"] = {
            "name": planning_params["project_name"],
            "type": planning_params["project_type"],
            "language": planning_params["primary_language"]
        }
        
        return planning_result
    
    async def _evaluate_planning_quality(
        self,
        planning_result: Dict[str, Any],
        planning_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估规划质量"""
        evaluation = {
            "overall_score": 0.0,
            "completeness_score": 0.0,
            "relevance_score": 0.0,
            "feasibility_score": 0.0,
            "issues": [],
            "recommendations": []
        }
        
        sections = planning_result.get("sections", [])
        
        # 完整性评估
        if len(sections) >= 3:
            evaluation["completeness_score"] = 0.8
        elif len(sections) >= 1:
            evaluation["completeness_score"] = 0.6
        else:
            evaluation["completeness_score"] = 0.2
            evaluation["issues"].append("章节数量过少")
        
        # 相关性评估
        evaluation["relevance_score"] = 0.8  # 默认相关性
        
        # 可行性评估
        total_length = sum(section.get("estimated_length", 500) for section in sections)
        preferred_length = planning_params.get("preferred_length", 5000)
        
        if abs(total_length - preferred_length) / preferred_length <= 0.2:
            evaluation["feasibility_score"] = 0.9
        elif abs(total_length - preferred_length) / preferred_length <= 0.5:
            evaluation["feasibility_score"] = 0.7
        else:
            evaluation["feasibility_score"] = 0.5
            evaluation["issues"].append("预估长度与期望差距较大")
        
        # 计算总分
        evaluation["overall_score"] = (
            evaluation["completeness_score"] * 0.4 +
            evaluation["relevance_score"] * 0.3 +
            evaluation["feasibility_score"] * 0.3
        )
        
        # 生成建议
        if evaluation["overall_score"] < 0.7:
            evaluation["recommendations"].append("建议优化规划方案")
        
        return evaluation
