"""
审阅智能体

负责智能文档审阅和质量控制的AI智能体，包括：
- 内容审阅
- 质量评估
- 错误检测
- 改进建议
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple

from ...ai_agent_core import BaseAgent, AgentInput, AgentOutput, AgentManager
from ..models.generation_models import GenerationResult
from ..templates.evaluation_templates import EvaluationTemplates, EvaluationTemplateType

logger = logging.getLogger(__name__)


@AgentManager.register("DocumentReviewerAgent")
class ReviewerAgent(BaseAgent):
    """文档审阅智能体"""
    
    def __init__(self):
        """初始化审阅智能体"""
        super().__init__()
        self.name = "DocumentReviewerAgent"
        self.evaluation_templates = EvaluationTemplates()
        self.review_criteria = {
            "accuracy": "准确性检查",
            "completeness": "完整性检查",
            "clarity": "清晰度检查",
            "consistency": "一致性检查",
            "usability": "可用性检查"
        }
        
    async def _initialize(self) -> None:
        """初始化审阅智能体特定资源"""
        logger.info("初始化文档审阅智能体特定资源")
        
    async def _create_chain(self) -> None:
        """创建审阅处理链"""
        try:
            # 使用质量评估模板作为默认模板
            review_template = self.evaluation_templates.get_quality_assessment_template()
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import JsonOutputParser
            
            prompt = PromptTemplate.from_template(review_template)
            self.chain = prompt | self.llm | JsonOutputParser()
            
            logger.info("文档审阅处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建文档审阅处理链失败: {str(e)}")
            raise
    
    async def _process(self, input_data: AgentInput) -> Tuple[Any, Dict[str, Any]]:
        """处理审阅请求"""
        try:
            # 解析审阅参数
            review_params = self._parse_review_parameters(input_data.parameters)
            
            # 验证审阅参数
            validation_result = await self._validate_review_parameters(review_params)
            if not validation_result["is_valid"]:
                return None, {
                    "error": f"审阅参数验证失败: {validation_result['errors']}",
                    "validation_result": validation_result
                }
            
            # 执行智能审阅
            review_result = await self._execute_intelligent_review(review_params)
            
            # 生成改进建议
            improvement_suggestions = await self._generate_improvement_suggestions(review_result, review_params)
            
            return review_result, {
                "review_id": review_result.get("review_id"),
                "overall_score": review_result.get("overall_score", 0),
                "review_type": review_params.get("review_type"),
                "issues_found": len(review_result.get("issues", [])),
                "suggestions_count": len(improvement_suggestions),
                "processing_time": (datetime.now(timezone.utc) - input_data.timestamp).total_seconds()
            }
            
        except Exception as e:
            logger.error(f"审阅处理失败: {str(e)}")
            return None, {"error": str(e)}
    
    def _parse_review_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """解析审阅参数"""
        return {
            "content_to_review": parameters.get("content_to_review", ""),
            "review_type": parameters.get("review_type", "quality_assessment"),
            "document_type": parameters.get("document_type", "technical"),
            "target_audience": parameters.get("target_audience", "developers"),
            "review_criteria": parameters.get("review_criteria", list(self.review_criteria.keys())),
            "quality_standards": parameters.get("quality_standards", {}),
            "context_information": parameters.get("context_information", {}),
            "previous_reviews": parameters.get("previous_reviews", []),
            "specific_focus": parameters.get("specific_focus", []),
            "severity_threshold": parameters.get("severity_threshold", "medium"),
            "detailed_analysis": parameters.get("detailed_analysis", True)
        }
    
    async def _validate_review_parameters(self, review_params: Dict[str, Any]) -> Dict[str, Any]:
        """验证审阅参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证待审阅内容
        content = review_params.get("content_to_review", "")
        if not content or not content.strip():
            validation_result["errors"].append("待审阅内容不能为空")
            validation_result["is_valid"] = False
        elif len(content.strip()) < 50:
            validation_result["warnings"].append("待审阅内容过短，可能影响审阅质量")
        
        # 验证审阅类型
        valid_review_types = ["quality_assessment", "content_review", "technical_accuracy", "user_experience"]
        if review_params.get("review_type") not in valid_review_types:
            validation_result["warnings"].append(f"未知的审阅类型: {review_params.get('review_type')}")
        
        # 验证审阅标准
        review_criteria = review_params.get("review_criteria", [])
        if not review_criteria:
            validation_result["warnings"].append("未指定审阅标准，将使用默认标准")
        
        return validation_result
    
    async def _execute_intelligent_review(self, review_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能审阅"""
        try:
            # 选择评估模板
            template_type = self._select_evaluation_template(review_params)
            
            # 准备审阅输入
            review_input = self._prepare_review_input(review_params)
            
            # 调用AI进行审阅
            review_result = await self.chain.ainvoke(review_input)
            
            # 后处理审阅结果
            processed_result = await self._post_process_review_result(review_result, review_params)
            
            return processed_result
            
        except Exception as e:
            logger.error(f"智能审阅执行失败: {str(e)}")
            raise
    
    def _select_evaluation_template(self, review_params: Dict[str, Any]) -> EvaluationTemplateType:
        """选择评估模板"""
        review_type = review_params.get("review_type", "quality_assessment")
        
        template_mapping = {
            "quality_assessment": EvaluationTemplateType.QUALITY_ASSESSMENT,
            "content_review": EvaluationTemplateType.CONTENT_REVIEW,
            "technical_accuracy": EvaluationTemplateType.TECHNICAL_ACCURACY,
            "user_experience": EvaluationTemplateType.USER_EXPERIENCE,
            "completeness_check": EvaluationTemplateType.COMPLETENESS_CHECK
        }
        
        return template_mapping.get(review_type, EvaluationTemplateType.QUALITY_ASSESSMENT)
    
    def _prepare_review_input(self, review_params: Dict[str, Any]) -> Dict[str, Any]:
        """准备审阅输入"""
        context_info = review_params.get("context_information", {})
        
        return {
            "content_to_evaluate": review_params["content_to_review"],
            "document_type": review_params["document_type"],
            "target_audience": review_params["target_audience"],
            "evaluation_standards": review_params.get("quality_standards", {}),
            "review_criteria": review_params["review_criteria"],
            "context_information": context_info,
            "specific_focus": review_params.get("specific_focus", []),
            "severity_threshold": review_params["severity_threshold"],
            "detailed_evaluation_criteria": self._get_detailed_criteria(review_params)
        }
    
    def _get_detailed_criteria(self, review_params: Dict[str, Any]) -> Dict[str, str]:
        """获取详细的评估标准"""
        criteria = {}
        for criterion in review_params.get("review_criteria", []):
            if criterion in self.review_criteria:
                criteria[criterion] = self.review_criteria[criterion]
        return criteria
    
    async def _post_process_review_result(
        self,
        review_result: Dict[str, Any],
        review_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """后处理审阅结果"""
        # 确保结果包含必要字段
        if "overall_score" not in review_result:
            review_result["overall_score"] = 0.0
        
        if "issues" not in review_result:
            review_result["issues"] = []
        
        if "recommendations" not in review_result:
            review_result["recommendations"] = []
        
        # 添加审阅元数据
        review_result["review_id"] = str(uuid.uuid4())
        review_result["reviewed_at"] = datetime.now(timezone.utc).isoformat()
        review_result["review_type"] = review_params["review_type"]
        review_result["reviewer"] = self.name
        
        # 分类问题严重程度
        review_result["issues_by_severity"] = self._categorize_issues_by_severity(review_result.get("issues", []))
        
        # 计算质量等级
        review_result["quality_level"] = self._determine_quality_level(review_result["overall_score"])
        
        return review_result
    
    def _categorize_issues_by_severity(self, issues: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按严重程度分类问题"""
        categorized = {
            "critical": [],
            "high": [],
            "medium": [],
            "low": []
        }
        
        for issue in issues:
            severity = issue.get("severity", "medium").lower()
            if severity in categorized:
                categorized[severity].append(issue)
            else:
                categorized["medium"].append(issue)
        
        return categorized
    
    def _determine_quality_level(self, overall_score: float) -> str:
        """确定质量等级"""
        if overall_score >= 0.9:
            return "优秀"
        elif overall_score >= 0.8:
            return "良好"
        elif overall_score >= 0.7:
            return "一般"
        elif overall_score >= 0.6:
            return "需改进"
        else:
            return "不合格"
    
    async def _generate_improvement_suggestions(
        self,
        review_result: Dict[str, Any],
        review_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成改进建议"""
        suggestions = []
        
        # 基于问题生成建议
        issues = review_result.get("issues", [])
        for issue in issues:
            suggestion = {
                "id": str(uuid.uuid4()),
                "category": issue.get("category", "general"),
                "priority": self._map_severity_to_priority(issue.get("severity", "medium")),
                "description": f"针对{issue.get('description', '问题')}的改进建议",
                "action": self._generate_action_for_issue(issue),
                "expected_impact": "提升内容质量"
            }
            suggestions.append(suggestion)
        
        # 基于分数生成通用建议
        overall_score = review_result.get("overall_score", 0)
        if overall_score < 0.8:
            suggestions.append({
                "id": str(uuid.uuid4()),
                "category": "general",
                "priority": "high",
                "description": "整体质量需要提升",
                "action": "建议重新审视内容结构和表达方式",
                "expected_impact": "显著提升文档质量"
            })
        
        return suggestions
    
    def _map_severity_to_priority(self, severity: str) -> str:
        """将严重程度映射为优先级"""
        mapping = {
            "critical": "urgent",
            "high": "high",
            "medium": "medium",
            "low": "low"
        }
        return mapping.get(severity.lower(), "medium")
    
    def _generate_action_for_issue(self, issue: Dict[str, Any]) -> str:
        """为问题生成具体行动建议"""
        issue_type = issue.get("type", "general")
        
        action_templates = {
            "accuracy": "核实相关信息的准确性",
            "completeness": "补充缺失的重要信息",
            "clarity": "简化表达，提高可读性",
            "consistency": "统一术语和格式",
            "structure": "重新组织内容结构",
            "grammar": "修正语法和拼写错误"
        }
        
        return action_templates.get(issue_type, "根据具体问题进行相应改进")
    
    async def review_multiple_sections(
        self,
        sections: Dict[str, str],
        review_config: Dict[str, Any]
    ) -> Dict[str, Dict[str, Any]]:
        """审阅多个章节"""
        review_results = {}
        
        for section_id, content in sections.items():
            section_params = review_config.copy()
            section_params["content_to_review"] = content
            
            # 准备输入数据
            input_data = AgentInput(
                request_id=f"review_{section_id}",
                parameters=section_params,
                context={},
                timestamp=datetime.now(timezone.utc)
            )
            
            # 处理审阅
            result, metadata = await self._process(input_data)
            review_results[section_id] = {
                "result": result,
                "metadata": metadata
            }
        
        return review_results
