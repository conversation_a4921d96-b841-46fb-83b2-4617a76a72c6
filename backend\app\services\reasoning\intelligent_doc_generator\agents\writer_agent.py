"""
写作智能体

负责智能文档内容生成的AI智能体，包括：
- 内容创作
- 风格控制
- 质量保证
- 多样化生成
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple

from ...ai_agent_core import BaseAgent, AgentInput, AgentOutput, AgentManager
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy
from ..templates.generation_templates import GenerationTemplates, GenerationTemplateType

logger = logging.getLogger(__name__)


@AgentManager.register("DocumentWriterAgent")
class WriterAgent(BaseAgent):
    """文档写作智能体"""
    
    def __init__(self):
        """初始化写作智能体"""
        super().__init__()
        self.name = "DocumentWriterAgent"
        self.generation_templates = GenerationTemplates()
        self.writing_styles = {
            "technical": "技术性写作，准确专业",
            "narrative": "叙述性写作，流畅易读",
            "creative": "创意性写作，生动有趣",
            "formal": "正式写作，严谨规范"
        }
        
    async def _initialize(self) -> None:
        """初始化写作智能体特定资源"""
        logger.info("初始化文档写作智能体特定资源")
        
    async def _create_chain(self) -> None:
        """创建写作处理链"""
        try:
            # 使用结构化生成模板作为默认模板
            writing_template = self.generation_templates.get_structured_generation_template()
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            prompt = PromptTemplate.from_template(writing_template)
            self.chain = prompt | self.llm | StrOutputParser()
            
            logger.info("文档写作处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建文档写作处理链失败: {str(e)}")
            raise
    
    async def _process(self, input_data: AgentInput) -> Tuple[Any, Dict[str, Any]]:
        """处理写作请求"""
        try:
            # 解析写作参数
            writing_params = self._parse_writing_parameters(input_data.parameters)
            
            # 验证写作参数
            validation_result = await self._validate_writing_parameters(writing_params)
            if not validation_result["is_valid"]:
                return None, {
                    "error": f"写作参数验证失败: {validation_result['errors']}",
                    "validation_result": validation_result
                }
            
            # 执行智能写作
            writing_result = await self._execute_intelligent_writing(writing_params)
            
            # 评估内容质量
            quality_result = await self._evaluate_content_quality(writing_result, writing_params)
            
            return writing_result, {
                "generation_id": writing_result.get("generation_id"),
                "content_type": writing_params.get("content_type"),
                "writing_style": writing_params.get("writing_style"),
                "word_count": len(writing_result.get("content", "").split()),
                "quality_score": quality_result.get("quality_score", 0),
                "processing_time": (datetime.now(timezone.utc) - input_data.timestamp).total_seconds()
            }
            
        except Exception as e:
            logger.error(f"写作处理失败: {str(e)}")
            return None, {"error": str(e)}
    
    def _parse_writing_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """解析写作参数"""
        return {
            "content_type": parameters.get("content_type", "text"),
            "section_title": parameters.get("section_title", ""),
            "section_description": parameters.get("section_description", ""),
            "writing_style": parameters.get("writing_style", "technical"),
            "target_length": parameters.get("target_length", 500),
            "complexity_level": parameters.get("complexity_level", "medium"),
            "target_audience": parameters.get("target_audience", "developers"),
            "project_context": parameters.get("project_context", {}),
            "content_guidelines": parameters.get("content_guidelines", []),
            "key_points": parameters.get("key_points", []),
            "reference_materials": parameters.get("reference_materials", []),
            "tone": parameters.get("tone", "professional"),
            "format_requirements": parameters.get("format_requirements", {}),
            "quality_requirements": parameters.get("quality_requirements", {})
        }
    
    async def _validate_writing_parameters(self, writing_params: Dict[str, Any]) -> Dict[str, Any]:
        """验证写作参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证内容类型
        valid_content_types = ["text", "introduction", "overview", "tutorial", "api_reference", "installation"]
        if writing_params.get("content_type") not in valid_content_types:
            validation_result["warnings"].append(f"未知的内容类型: {writing_params.get('content_type')}")
        
        # 验证写作风格
        if writing_params.get("writing_style") not in self.writing_styles:
            validation_result["warnings"].append(f"未知的写作风格: {writing_params.get('writing_style')}")
        
        # 验证目标长度
        target_length = writing_params.get("target_length", 0)
        if target_length <= 0:
            validation_result["warnings"].append("目标长度应大于0")
        elif target_length > 5000:
            validation_result["warnings"].append("目标长度过长，可能影响质量")
        
        # 验证复杂度级别
        valid_complexity = ["low", "medium", "high"]
        if writing_params.get("complexity_level") not in valid_complexity:
            validation_result["warnings"].append(f"未知的复杂度级别: {writing_params.get('complexity_level')}")
        
        return validation_result
    
    async def _execute_intelligent_writing(self, writing_params: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能写作"""
        try:
            # 选择生成模板
            template_type = self._select_generation_template(writing_params)
            
            # 准备写作输入
            writing_input = self._prepare_writing_input(writing_params)
            
            # 调用AI进行写作
            generated_content = await self.chain.ainvoke(writing_input)
            
            # 后处理写作结果
            processed_result = await self._post_process_writing_result(generated_content, writing_params)
            
            return processed_result
            
        except Exception as e:
            logger.error(f"智能写作执行失败: {str(e)}")
            raise
    
    def _select_generation_template(self, writing_params: Dict[str, Any]) -> GenerationTemplateType:
        """选择生成模板"""
        writing_style = writing_params.get("writing_style", "technical")
        content_type = writing_params.get("content_type", "text")
        
        # 根据写作风格和内容类型选择模板
        if writing_style == "narrative" or content_type in ["introduction", "overview"]:
            return GenerationTemplateType.NARRATIVE
        elif writing_style == "technical" or content_type in ["api_reference", "installation"]:
            return GenerationTemplateType.TECHNICAL
        elif writing_style == "creative":
            return GenerationTemplateType.CREATIVE
        else:
            return GenerationTemplateType.STRUCTURED
    
    def _prepare_writing_input(self, writing_params: Dict[str, Any]) -> Dict[str, Any]:
        """准备写作输入"""
        project_context = writing_params.get("project_context", {})
        
        return {
            "section_type": writing_params["content_type"],
            "section_title": writing_params["section_title"],
            "target_length": writing_params["target_length"],
            "project_name": project_context.get("project_name", "未知项目"),
            "project_type": project_context.get("project_type", "软件项目"),
            "primary_language": project_context.get("primary_language", "Python"),
            "tech_stack": project_context.get("tech_stack", []),
            "context_data": project_context,
            "content_strategy": writing_params["writing_style"],
            "target_audience": writing_params["target_audience"],
            "complexity_level": writing_params["complexity_level"],
            "tone": writing_params["tone"],
            "content_guidelines": writing_params.get("content_guidelines", []),
            "key_points": writing_params.get("key_points", []),
            "special_requirements": writing_params.get("format_requirements", {})
        }
    
    async def _post_process_writing_result(
        self,
        generated_content: str,
        writing_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """后处理写作结果"""
        # 清理和格式化内容
        cleaned_content = self._clean_generated_content(generated_content)
        
        # 应用格式要求
        formatted_content = await self._apply_format_requirements(cleaned_content, writing_params)
        
        # 创建结果对象
        writing_result = {
            "generation_id": str(uuid.uuid4()),
            "content": formatted_content,
            "content_type": writing_params["content_type"],
            "writing_style": writing_params["writing_style"],
            "word_count": len(formatted_content.split()),
            "character_count": len(formatted_content),
            "target_length": writing_params["target_length"],
            "complexity_level": writing_params["complexity_level"],
            "created_at": datetime.now(timezone.utc).isoformat(),
            "metadata": {
                "target_audience": writing_params["target_audience"],
                "tone": writing_params["tone"],
                "guidelines_applied": len(writing_params.get("content_guidelines", [])),
                "key_points_covered": len(writing_params.get("key_points", []))
            }
        }
        
        return writing_result
    
    def _clean_generated_content(self, content: str) -> str:
        """清理生成的内容"""
        # 移除多余的空行
        lines = content.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            stripped = line.strip()
            if stripped:
                cleaned_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                cleaned_lines.append('')
                prev_empty = True
        
        return '\n'.join(cleaned_lines).strip()
    
    async def _apply_format_requirements(
        self,
        content: str,
        writing_params: Dict[str, Any]
    ) -> str:
        """应用格式要求"""
        format_requirements = writing_params.get("format_requirements", {})
        
        # 应用标题格式
        if format_requirements.get("title_format") == "markdown":
            content = self._ensure_markdown_titles(content)
        
        # 应用列表格式
        if format_requirements.get("list_format") == "numbered":
            content = self._convert_to_numbered_lists(content)
        
        # 应用代码块格式
        if format_requirements.get("code_format") == "highlighted":
            content = self._highlight_code_blocks(content)
        
        return content
    
    def _ensure_markdown_titles(self, content: str) -> str:
        """确保Markdown标题格式"""
        lines = content.split('\n')
        formatted_lines = []
        
        for line in lines:
            stripped = line.strip()
            # 简单的标题检测和格式化
            if stripped and not stripped.startswith('#') and self._looks_like_title(stripped):
                formatted_lines.append(f"## {stripped}")
            else:
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def _looks_like_title(self, text: str) -> bool:
        """判断文本是否看起来像标题"""
        return (
            len(text) < 80 and
            not text.endswith('.') and
            not text.startswith('-') and
            not text.startswith('*') and
            text[0].isupper()
        )
    
    def _convert_to_numbered_lists(self, content: str) -> str:
        """转换为编号列表"""
        # 简化实现
        return content
    
    def _highlight_code_blocks(self, content: str) -> str:
        """高亮代码块"""
        # 简化实现
        return content
    
    async def _evaluate_content_quality(
        self,
        writing_result: Dict[str, Any],
        writing_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估内容质量"""
        content = writing_result.get("content", "")
        target_length = writing_params.get("target_length", 500)
        
        quality_evaluation = {
            "quality_score": 0.0,
            "length_score": 0.0,
            "structure_score": 0.0,
            "completeness_score": 0.0,
            "issues": [],
            "strengths": []
        }
        
        # 长度评估
        word_count = len(content.split())
        length_ratio = word_count / target_length if target_length > 0 else 0
        
        if 0.8 <= length_ratio <= 1.2:
            quality_evaluation["length_score"] = 0.9
        elif 0.6 <= length_ratio <= 1.5:
            quality_evaluation["length_score"] = 0.7
        else:
            quality_evaluation["length_score"] = 0.5
            quality_evaluation["issues"].append("内容长度与目标差距较大")
        
        # 结构评估
        if self._has_good_structure(content):
            quality_evaluation["structure_score"] = 0.8
            quality_evaluation["strengths"].append("结构清晰")
        else:
            quality_evaluation["structure_score"] = 0.6
            quality_evaluation["issues"].append("结构需要改进")
        
        # 完整性评估
        key_points = writing_params.get("key_points", [])
        if key_points:
            covered_points = sum(1 for point in key_points if point.lower() in content.lower())
            quality_evaluation["completeness_score"] = covered_points / len(key_points)
        else:
            quality_evaluation["completeness_score"] = 0.8  # 默认完整性
        
        # 计算总分
        quality_evaluation["quality_score"] = (
            quality_evaluation["length_score"] * 0.3 +
            quality_evaluation["structure_score"] * 0.4 +
            quality_evaluation["completeness_score"] * 0.3
        )
        
        return quality_evaluation
    
    def _has_good_structure(self, content: str) -> bool:
        """检查内容结构"""
        lines = content.strip().split('\n')
        
        # 检查是否有标题
        has_titles = any(line.strip().startswith('#') for line in lines)
        
        # 检查段落数量
        paragraphs = [line for line in lines if line.strip()]
        has_multiple_paragraphs = len(paragraphs) > 3
        
        return has_titles or has_multiple_paragraphs
