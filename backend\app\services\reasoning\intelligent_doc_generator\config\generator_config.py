"""
生成器配置

管理智能文档生成器的各种配置参数
"""

import os
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class GeneratorType(str, Enum):
    """生成器类型"""
    STRUCTURED = "structured"
    NARRATIVE = "narrative"
    TECHNICAL = "technical"
    CREATIVE = "creative"


class QualityLevel(str, Enum):
    """质量级别"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ModelConfig:
    """AI模型配置"""
    model_name: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 2000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 30
    retry_attempts: int = 3


@dataclass
class GenerationConfig:
    """内容生成配置"""
    default_length: int = 500
    min_length: int = 100
    max_length: int = 2000
    quality_threshold: float = 0.7
    enable_optimization: bool = True
    enable_validation: bool = True
    parallel_generation: bool = True
    max_concurrent_tasks: int = 5


@dataclass
class LanguageConfig:
    """语言配置"""
    primary_language: str = "zh-CN"
    supported_languages: List[str] = field(default_factory=lambda: ["zh-CN", "en-US"])
    fallback_language: str = "en-US"
    enable_translation: bool = False


@dataclass
class OutputConfig:
    """输出配置"""
    default_format: str = "markdown"
    supported_formats: List[str] = field(default_factory=lambda: ["markdown", "html", "plain_text", "json"])
    include_metadata: bool = True
    include_statistics: bool = True
    enable_export: bool = True
    export_formats: List[str] = field(default_factory=lambda: ["pdf", "docx", "html"])


class GeneratorConfig:
    """生成器配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置管理器"""
        self.config_file = config_file
        self._load_default_config()
        
        if config_file and os.path.exists(config_file):
            self._load_config_from_file(config_file)
    
    def _load_default_config(self):
        """加载默认配置"""
        # AI模型配置
        self.model_config = ModelConfig()
        
        # 生成配置
        self.generation_config = GenerationConfig()
        
        # 语言配置
        self.language_config = LanguageConfig()
        
        # 输出配置
        self.output_config = OutputConfig()
        
        # 生成器特定配置
        self.generator_configs = {
            GeneratorType.STRUCTURED: {
                "template_style": "formal",
                "include_examples": True,
                "section_numbering": True,
                "table_of_contents": True
            },
            GeneratorType.NARRATIVE: {
                "writing_style": "engaging",
                "storytelling_elements": True,
                "personal_tone": False,
                "flow_optimization": True
            },
            GeneratorType.TECHNICAL: {
                "technical_depth": "medium",
                "include_code_examples": True,
                "api_documentation": True,
                "troubleshooting_section": True
            },
            GeneratorType.CREATIVE: {
                "creativity_level": "medium",
                "use_metaphors": True,
                "emotional_appeal": True,
                "visual_elements": True
            }
        }
        
        # 质量控制配置
        self.quality_config = {
            QualityLevel.HIGH: {
                "min_quality_score": 0.8,
                "enable_multi_pass": True,
                "enable_peer_review": True,
                "max_iterations": 3
            },
            QualityLevel.MEDIUM: {
                "min_quality_score": 0.7,
                "enable_multi_pass": True,
                "enable_peer_review": False,
                "max_iterations": 2
            },
            QualityLevel.LOW: {
                "min_quality_score": 0.6,
                "enable_multi_pass": False,
                "enable_peer_review": False,
                "max_iterations": 1
            }
        }
        
        # 性能配置
        self.performance_config = {
            "cache_enabled": True,
            "cache_ttl": 3600,  # 1小时
            "batch_size": 10,
            "memory_limit": "1GB",
            "cpu_limit": 4
        }
        
        # 安全配置
        self.security_config = {
            "content_filtering": True,
            "sensitive_data_detection": True,
            "output_sanitization": True,
            "audit_logging": True
        }
    
    def _load_config_from_file(self, config_file: str):
        """从文件加载配置"""
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            self._update_config_from_dict(config_data)
            logger.info(f"配置已从文件加载: {config_file}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        # 更新模型配置
        if "model_config" in config_data:
            model_data = config_data["model_config"]
            for key, value in model_data.items():
                if hasattr(self.model_config, key):
                    setattr(self.model_config, key, value)
        
        # 更新生成配置
        if "generation_config" in config_data:
            gen_data = config_data["generation_config"]
            for key, value in gen_data.items():
                if hasattr(self.generation_config, key):
                    setattr(self.generation_config, key, value)
        
        # 更新语言配置
        if "language_config" in config_data:
            lang_data = config_data["language_config"]
            for key, value in lang_data.items():
                if hasattr(self.language_config, key):
                    setattr(self.language_config, key, value)
        
        # 更新输出配置
        if "output_config" in config_data:
            output_data = config_data["output_config"]
            for key, value in output_data.items():
                if hasattr(self.output_config, key):
                    setattr(self.output_config, key, value)
        
        # 更新其他配置
        for config_name in ["generator_configs", "quality_config", "performance_config", "security_config"]:
            if config_name in config_data:
                getattr(self, config_name).update(config_data[config_name])
    
    def get_generator_config(self, generator_type: GeneratorType) -> Dict[str, Any]:
        """获取特定生成器的配置"""
        return self.generator_configs.get(generator_type, {})
    
    def get_quality_config(self, quality_level: QualityLevel) -> Dict[str, Any]:
        """获取特定质量级别的配置"""
        return self.quality_config.get(quality_level, self.quality_config[QualityLevel.MEDIUM])
    
    def update_generator_config(self, generator_type: GeneratorType, config: Dict[str, Any]):
        """更新生成器配置"""
        if generator_type in self.generator_configs:
            self.generator_configs[generator_type].update(config)
        else:
            self.generator_configs[generator_type] = config
    
    def update_model_config(self, **kwargs):
        """更新模型配置"""
        for key, value in kwargs.items():
            if hasattr(self.model_config, key):
                setattr(self.model_config, key, value)
    
    def update_generation_config(self, **kwargs):
        """更新生成配置"""
        for key, value in kwargs.items():
            if hasattr(self.generation_config, key):
                setattr(self.generation_config, key, value)
    
    def save_config(self, config_file: Optional[str] = None):
        """保存配置到文件"""
        config_file = config_file or self.config_file
        if not config_file:
            logger.warning("未指定配置文件路径")
            return
        
        try:
            config_data = self.to_dict()
            
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置已保存到文件: {config_file}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "model_config": {
                "model_name": self.model_config.model_name,
                "temperature": self.model_config.temperature,
                "max_tokens": self.model_config.max_tokens,
                "top_p": self.model_config.top_p,
                "frequency_penalty": self.model_config.frequency_penalty,
                "presence_penalty": self.model_config.presence_penalty,
                "timeout": self.model_config.timeout,
                "retry_attempts": self.model_config.retry_attempts
            },
            "generation_config": {
                "default_length": self.generation_config.default_length,
                "min_length": self.generation_config.min_length,
                "max_length": self.generation_config.max_length,
                "quality_threshold": self.generation_config.quality_threshold,
                "enable_optimization": self.generation_config.enable_optimization,
                "enable_validation": self.generation_config.enable_validation,
                "parallel_generation": self.generation_config.parallel_generation,
                "max_concurrent_tasks": self.generation_config.max_concurrent_tasks
            },
            "language_config": {
                "primary_language": self.language_config.primary_language,
                "supported_languages": self.language_config.supported_languages,
                "fallback_language": self.language_config.fallback_language,
                "enable_translation": self.language_config.enable_translation
            },
            "output_config": {
                "default_format": self.output_config.default_format,
                "supported_formats": self.output_config.supported_formats,
                "include_metadata": self.output_config.include_metadata,
                "include_statistics": self.output_config.include_statistics,
                "enable_export": self.output_config.enable_export,
                "export_formats": self.output_config.export_formats
            },
            "generator_configs": self.generator_configs,
            "quality_config": self.quality_config,
            "performance_config": self.performance_config,
            "security_config": self.security_config
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证模型配置
        if self.model_config.temperature < 0 or self.model_config.temperature > 2:
            validation_result["errors"].append("temperature应在0-2之间")
            validation_result["is_valid"] = False
        
        if self.model_config.max_tokens <= 0:
            validation_result["errors"].append("max_tokens应大于0")
            validation_result["is_valid"] = False
        
        # 验证生成配置
        if self.generation_config.min_length >= self.generation_config.max_length:
            validation_result["errors"].append("min_length应小于max_length")
            validation_result["is_valid"] = False
        
        if self.generation_config.quality_threshold < 0 or self.generation_config.quality_threshold > 1:
            validation_result["errors"].append("quality_threshold应在0-1之间")
            validation_result["is_valid"] = False
        
        # 验证语言配置
        if self.language_config.primary_language not in self.language_config.supported_languages:
            validation_result["warnings"].append("主要语言不在支持的语言列表中")
        
        return validation_result
    
    def get_environment_config(self) -> Dict[str, Any]:
        """获取环境相关配置"""
        return {
            "debug_mode": os.getenv("DEBUG", "false").lower() == "true",
            "log_level": os.getenv("LOG_LEVEL", "INFO"),
            "api_timeout": int(os.getenv("API_TIMEOUT", "30")),
            "max_workers": int(os.getenv("MAX_WORKERS", "4")),
            "cache_dir": os.getenv("CACHE_DIR", "/tmp/doc_generator_cache")
        }
