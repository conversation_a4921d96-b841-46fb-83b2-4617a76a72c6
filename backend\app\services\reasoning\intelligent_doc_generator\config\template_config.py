"""
模板配置

管理AI提示词模板的配置和管理
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class TemplateCategory(str, Enum):
    """模板类别"""
    PLANNING = "planning"
    GENERATION = "generation"
    REFINEMENT = "refinement"
    EVALUATION = "evaluation"


class TemplateFormat(str, Enum):
    """模板格式"""
    JINJA2 = "jinja2"
    F_STRING = "f_string"
    PLAIN_TEXT = "plain_text"


@dataclass
class TemplateMetadata:
    """模板元数据"""
    name: str
    description: str
    category: TemplateCategory
    format: TemplateFormat
    version: str = "1.0.0"
    author: str = "system"
    created_at: str = ""
    updated_at: str = ""
    tags: List[str] = field(default_factory=list)
    parameters: List[str] = field(default_factory=list)
    examples: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class TemplateValidation:
    """模板验证配置"""
    required_parameters: List[str] = field(default_factory=list)
    optional_parameters: List[str] = field(default_factory=list)
    parameter_types: Dict[str, str] = field(default_factory=dict)
    validation_rules: List[str] = field(default_factory=list)


class TemplateConfig:
    """模板配置管理器"""
    
    def __init__(self, template_dir: Optional[str] = None):
        """初始化模板配置管理器"""
        self.template_dir = template_dir or self._get_default_template_dir()
        self.templates: Dict[str, Dict[str, Any]] = {}
        self.template_metadata: Dict[str, TemplateMetadata] = {}
        self.template_validations: Dict[str, TemplateValidation] = {}
        
        self._load_default_templates()
        self._load_custom_templates()
    
    def _get_default_template_dir(self) -> str:
        """获取默认模板目录"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(os.path.dirname(current_dir), "templates")
    
    def _load_default_templates(self):
        """加载默认模板配置"""
        # 规划模板配置
        self._register_planning_templates()
        
        # 生成模板配置
        self._register_generation_templates()
        
        # 优化模板配置
        self._register_refinement_templates()
        
        # 评估模板配置
        self._register_evaluation_templates()
    
    def _register_planning_templates(self):
        """注册规划模板"""
        planning_templates = {
            "adaptive_planning": {
                "metadata": TemplateMetadata(
                    name="adaptive_planning",
                    description="自适应文档规划模板",
                    category=TemplateCategory.PLANNING,
                    format=TemplateFormat.JINJA2,
                    tags=["planning", "adaptive", "intelligent"],
                    parameters=["project_name", "project_type", "target_audience", "structure_analysis"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["project_name", "project_type"],
                    optional_parameters=["target_audience", "structure_analysis", "time_constraints"],
                    parameter_types={
                        "project_name": "string",
                        "project_type": "string",
                        "target_audience": "string",
                        "max_sections": "integer"
                    }
                ),
                "config": {
                    "max_output_length": 2000,
                    "temperature": 0.7,
                    "enable_reasoning": True,
                    "output_format": "json"
                }
            },
            "domain_specific_planning": {
                "metadata": TemplateMetadata(
                    name="domain_specific_planning",
                    description="领域特定文档规划模板",
                    category=TemplateCategory.PLANNING,
                    format=TemplateFormat.JINJA2,
                    tags=["planning", "domain", "specialized"],
                    parameters=["domain", "project_subtype", "industry_standards"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["domain", "project_name"],
                    optional_parameters=["project_subtype", "industry_standards", "best_practices"],
                    parameter_types={
                        "domain": "string",
                        "project_name": "string",
                        "industry_standards": "list"
                    }
                ),
                "config": {
                    "max_output_length": 2500,
                    "temperature": 0.6,
                    "enable_reasoning": True,
                    "output_format": "json"
                }
            }
        }
        
        for template_name, template_config in planning_templates.items():
            self._register_template(template_name, template_config)
    
    def _register_generation_templates(self):
        """注册生成模板"""
        generation_templates = {
            "structured_generation": {
                "metadata": TemplateMetadata(
                    name="structured_generation",
                    description="结构化内容生成模板",
                    category=TemplateCategory.GENERATION,
                    format=TemplateFormat.JINJA2,
                    tags=["generation", "structured", "formal"],
                    parameters=["section_type", "content_strategy", "structure_requirements"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["section_type", "project_name"],
                    optional_parameters=["content_strategy", "structure_requirements", "target_length"],
                    parameter_types={
                        "section_type": "string",
                        "project_name": "string",
                        "target_length": "integer"
                    }
                ),
                "config": {
                    "max_output_length": 3000,
                    "temperature": 0.7,
                    "enable_examples": True,
                    "output_format": "markdown"
                }
            },
            "narrative_generation": {
                "metadata": TemplateMetadata(
                    name="narrative_generation",
                    description="叙述性内容生成模板",
                    category=TemplateCategory.GENERATION,
                    format=TemplateFormat.JINJA2,
                    tags=["generation", "narrative", "storytelling"],
                    parameters=["narrative_style", "tone_style", "target_audience"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["section_type", "narrative_style"],
                    optional_parameters=["tone_style", "target_audience", "style_guidance"],
                    parameter_types={
                        "section_type": "string",
                        "narrative_style": "string",
                        "tone_style": "string"
                    }
                ),
                "config": {
                    "max_output_length": 2500,
                    "temperature": 0.8,
                    "enable_creativity": True,
                    "output_format": "markdown"
                }
            }
        }
        
        for template_name, template_config in generation_templates.items():
            self._register_template(template_name, template_config)
    
    def _register_refinement_templates(self):
        """注册优化模板"""
        refinement_templates = {
            "quality_improvement": {
                "metadata": TemplateMetadata(
                    name="quality_improvement",
                    description="质量改进模板",
                    category=TemplateCategory.REFINEMENT,
                    format=TemplateFormat.JINJA2,
                    tags=["refinement", "quality", "improvement"],
                    parameters=["original_content", "quality_issues", "target_quality_score"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["original_content", "current_quality_score"],
                    optional_parameters=["quality_issues", "target_quality_score", "improvement_guidelines"],
                    parameter_types={
                        "original_content": "string",
                        "current_quality_score": "float",
                        "target_quality_score": "float"
                    }
                ),
                "config": {
                    "max_output_length": 4000,
                    "temperature": 0.6,
                    "enable_analysis": True,
                    "output_format": "markdown"
                }
            },
            "style_refinement": {
                "metadata": TemplateMetadata(
                    name="style_refinement",
                    description="风格精炼模板",
                    category=TemplateCategory.REFINEMENT,
                    format=TemplateFormat.JINJA2,
                    tags=["refinement", "style", "language"],
                    parameters=["original_content", "target_style", "style_requirements"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["original_content", "target_style"],
                    optional_parameters=["style_requirements", "audience_considerations", "brand_voice"],
                    parameter_types={
                        "original_content": "string",
                        "target_style": "string",
                        "style_requirements": "dict"
                    }
                ),
                "config": {
                    "max_output_length": 3500,
                    "temperature": 0.7,
                    "enable_style_analysis": True,
                    "output_format": "markdown"
                }
            }
        }
        
        for template_name, template_config in refinement_templates.items():
            self._register_template(template_name, template_config)
    
    def _register_evaluation_templates(self):
        """注册评估模板"""
        evaluation_templates = {
            "quality_assessment": {
                "metadata": TemplateMetadata(
                    name="quality_assessment",
                    description="质量评估模板",
                    category=TemplateCategory.EVALUATION,
                    format=TemplateFormat.JINJA2,
                    tags=["evaluation", "quality", "assessment"],
                    parameters=["content_to_evaluate", "evaluation_standards", "target_audience"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["content_to_evaluate", "document_type"],
                    optional_parameters=["evaluation_standards", "target_audience", "quality_requirements"],
                    parameter_types={
                        "content_to_evaluate": "string",
                        "document_type": "string",
                        "target_audience": "string"
                    }
                ),
                "config": {
                    "max_output_length": 2000,
                    "temperature": 0.5,
                    "enable_scoring": True,
                    "output_format": "json"
                }
            },
            "content_review": {
                "metadata": TemplateMetadata(
                    name="content_review",
                    description="内容审查模板",
                    category=TemplateCategory.EVALUATION,
                    format=TemplateFormat.JINJA2,
                    tags=["evaluation", "review", "audit"],
                    parameters=["content_to_review", "review_criteria", "compliance_requirements"]
                ),
                "validation": TemplateValidation(
                    required_parameters=["content_to_review", "review_type"],
                    optional_parameters=["review_criteria", "compliance_requirements", "industry_standards"],
                    parameter_types={
                        "content_to_review": "string",
                        "review_type": "string",
                        "review_criteria": "list"
                    }
                ),
                "config": {
                    "max_output_length": 2500,
                    "temperature": 0.4,
                    "enable_detailed_analysis": True,
                    "output_format": "json"
                }
            }
        }
        
        for template_name, template_config in evaluation_templates.items():
            self._register_template(template_name, template_config)
    
    def _register_template(self, template_name: str, template_config: Dict[str, Any]):
        """注册模板"""
        self.templates[template_name] = template_config
        self.template_metadata[template_name] = template_config["metadata"]
        self.template_validations[template_name] = template_config["validation"]
    
    def _load_custom_templates(self):
        """加载自定义模板"""
        if not os.path.exists(self.template_dir):
            return
        
        try:
            custom_config_file = os.path.join(self.template_dir, "custom_templates.json")
            if os.path.exists(custom_config_file):
                with open(custom_config_file, 'r', encoding='utf-8') as f:
                    custom_templates = json.load(f)
                
                for template_name, template_config in custom_templates.items():
                    self._register_custom_template(template_name, template_config)
                
                logger.info(f"加载了 {len(custom_templates)} 个自定义模板")
        
        except Exception as e:
            logger.error(f"加载自定义模板失败: {str(e)}")
    
    def _register_custom_template(self, template_name: str, template_config: Dict[str, Any]):
        """注册自定义模板"""
        # 创建元数据对象
        metadata_data = template_config.get("metadata", {})
        metadata = TemplateMetadata(
            name=template_name,
            description=metadata_data.get("description", ""),
            category=TemplateCategory(metadata_data.get("category", "generation")),
            format=TemplateFormat(metadata_data.get("format", "jinja2")),
            version=metadata_data.get("version", "1.0.0"),
            author=metadata_data.get("author", "custom"),
            tags=metadata_data.get("tags", []),
            parameters=metadata_data.get("parameters", [])
        )
        
        # 创建验证对象
        validation_data = template_config.get("validation", {})
        validation = TemplateValidation(
            required_parameters=validation_data.get("required_parameters", []),
            optional_parameters=validation_data.get("optional_parameters", []),
            parameter_types=validation_data.get("parameter_types", {}),
            validation_rules=validation_data.get("validation_rules", [])
        )
        
        # 注册模板
        self.templates[template_name] = template_config
        self.template_metadata[template_name] = metadata
        self.template_validations[template_name] = validation
    
    def get_template_config(self, template_name: str) -> Optional[Dict[str, Any]]:
        """获取模板配置"""
        return self.templates.get(template_name)
    
    def get_template_metadata(self, template_name: str) -> Optional[TemplateMetadata]:
        """获取模板元数据"""
        return self.template_metadata.get(template_name)
    
    def get_template_validation(self, template_name: str) -> Optional[TemplateValidation]:
        """获取模板验证配置"""
        return self.template_validations.get(template_name)
    
    def get_templates_by_category(self, category: TemplateCategory) -> Dict[str, Dict[str, Any]]:
        """按类别获取模板"""
        return {
            name: config for name, config in self.templates.items()
            if self.template_metadata[name].category == category
        }
    
    def get_templates_by_tags(self, tags: List[str]) -> Dict[str, Dict[str, Any]]:
        """按标签获取模板"""
        return {
            name: config for name, config in self.templates.items()
            if any(tag in self.template_metadata[name].tags for tag in tags)
        }
    
    def validate_template_parameters(self, template_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证模板参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        validation_config = self.get_template_validation(template_name)
        if not validation_config:
            validation_result["warnings"].append(f"模板 {template_name} 没有验证配置")
            return validation_result
        
        # 检查必需参数
        for param in validation_config.required_parameters:
            if param not in parameters:
                validation_result["errors"].append(f"缺少必需参数: {param}")
                validation_result["is_valid"] = False
        
        # 检查参数类型
        for param, expected_type in validation_config.parameter_types.items():
            if param in parameters:
                actual_value = parameters[param]
                if not self._check_parameter_type(actual_value, expected_type):
                    validation_result["errors"].append(f"参数 {param} 类型错误，期望 {expected_type}")
                    validation_result["is_valid"] = False
        
        return validation_result
    
    def _check_parameter_type(self, value: Any, expected_type: str) -> bool:
        """检查参数类型"""
        type_mapping = {
            "string": str,
            "integer": int,
            "float": float,
            "boolean": bool,
            "list": list,
            "dict": dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True  # 未知类型默认通过
    
    def list_all_templates(self) -> List[str]:
        """列出所有模板名称"""
        return list(self.templates.keys())
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        category_counts = {}
        for metadata in self.template_metadata.values():
            category = metadata.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            "total_templates": len(self.templates),
            "category_distribution": category_counts,
            "format_distribution": {
                format_type.value: sum(1 for m in self.template_metadata.values() if m.format == format_type)
                for format_type in TemplateFormat
            }
        }
