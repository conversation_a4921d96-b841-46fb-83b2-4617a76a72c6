"""
工作流配置

管理智能文档生成工作流的配置参数
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class WorkflowType(str, Enum):
    """工作流类型"""
    FULL_PIPELINE = "full_pipeline"
    PLANNING_ONLY = "planning_only"
    GENERATION_ONLY = "generation_only"
    REFINEMENT_ONLY = "refinement_only"
    CUSTOM = "custom"


class ExecutionMode(str, Enum):
    """执行模式"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    HYBRID = "hybrid"


class ErrorHandling(str, Enum):
    """错误处理策略"""
    FAIL_FAST = "fail_fast"
    CONTINUE_ON_ERROR = "continue_on_error"
    RETRY_ON_ERROR = "retry_on_error"


@dataclass
class StageConfig:
    """阶段配置"""
    name: str
    enabled: bool = True
    timeout: int = 300  # 5分钟
    retry_attempts: int = 3
    retry_delay: int = 5  # 秒
    dependencies: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PipelineConfig:
    """流水线配置"""
    name: str
    description: str
    workflow_type: WorkflowType
    execution_mode: ExecutionMode
    error_handling: ErrorHandling
    max_execution_time: int = 1800  # 30分钟
    enable_monitoring: bool = True
    enable_caching: bool = True
    stages: List[StageConfig] = field(default_factory=list)


class WorkflowConfig:
    """工作流配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化工作流配置管理器"""
        self.config_file = config_file
        self.pipeline_configs: Dict[str, PipelineConfig] = {}
        self.default_configs: Dict[str, Dict[str, Any]] = {}
        
        self._load_default_configs()
        
        if config_file and os.path.exists(config_file):
            self._load_config_from_file(config_file)
    
    def _load_default_configs(self):
        """加载默认配置"""
        # 完整流水线配置
        self._create_full_pipeline_config()
        
        # 仅规划配置
        self._create_planning_only_config()
        
        # 仅生成配置
        self._create_generation_only_config()
        
        # 仅优化配置
        self._create_refinement_only_config()
        
        # 默认参数配置
        self._load_default_parameters()
    
    def _create_full_pipeline_config(self):
        """创建完整流水线配置"""
        stages = [
            StageConfig(
                name="initialization",
                timeout=60,
                parameters={
                    "validate_inputs": True,
                    "prepare_environment": True,
                    "initialize_monitoring": True
                }
            ),
            StageConfig(
                name="planning",
                timeout=300,
                dependencies=["initialization"],
                parameters={
                    "strategy": "adaptive",
                    "enable_evaluation": True,
                    "max_sections": 20,
                    "quality_threshold": 0.7
                }
            ),
            StageConfig(
                name="generation",
                timeout=600,
                dependencies=["planning"],
                parameters={
                    "parallel_generation": True,
                    "max_concurrent_tasks": 5,
                    "quality_threshold": 0.7,
                    "enable_validation": True
                }
            ),
            StageConfig(
                name="refinement",
                timeout=400,
                dependencies=["generation"],
                parameters={
                    "enable_quality_improvement": True,
                    "enable_style_refinement": True,
                    "enable_consistency_check": True,
                    "target_quality": 0.8
                }
            ),
            StageConfig(
                name="finalization",
                timeout=120,
                dependencies=["refinement"],
                parameters={
                    "generate_metadata": True,
                    "generate_statistics": True,
                    "export_formats": ["markdown", "html"]
                }
            )
        ]
        
        config = PipelineConfig(
            name="full_pipeline",
            description="完整的智能文档生成流水线",
            workflow_type=WorkflowType.FULL_PIPELINE,
            execution_mode=ExecutionMode.SEQUENTIAL,
            error_handling=ErrorHandling.RETRY_ON_ERROR,
            max_execution_time=1800,
            stages=stages
        )
        
        self.pipeline_configs["full_pipeline"] = config
    
    def _create_planning_only_config(self):
        """创建仅规划配置"""
        stages = [
            StageConfig(
                name="context_analysis",
                timeout=120,
                parameters={
                    "analyze_project_structure": True,
                    "extract_requirements": True,
                    "identify_audience": True
                }
            ),
            StageConfig(
                name="structure_planning",
                timeout=180,
                dependencies=["context_analysis"],
                parameters={
                    "strategy": "adaptive",
                    "max_sections": 15,
                    "enable_optimization": True
                }
            ),
            StageConfig(
                name="plan_evaluation",
                timeout=60,
                dependencies=["structure_planning"],
                parameters={
                    "evaluation_criteria": ["completeness", "relevance", "feasibility"],
                    "quality_threshold": 0.7
                }
            )
        ]
        
        config = PipelineConfig(
            name="planning_only",
            description="仅执行文档规划阶段",
            workflow_type=WorkflowType.PLANNING_ONLY,
            execution_mode=ExecutionMode.SEQUENTIAL,
            error_handling=ErrorHandling.FAIL_FAST,
            max_execution_time=600,
            stages=stages
        )
        
        self.pipeline_configs["planning_only"] = config
    
    def _create_generation_only_config(self):
        """创建仅生成配置"""
        stages = [
            StageConfig(
                name="generator_selection",
                timeout=30,
                parameters={
                    "auto_select_generators": True,
                    "fallback_generator": "structured"
                }
            ),
            StageConfig(
                name="content_generation",
                timeout=600,
                dependencies=["generator_selection"],
                parameters={
                    "parallel_generation": True,
                    "max_concurrent_tasks": 8,
                    "enable_quality_check": True,
                    "auto_retry_failed": True
                }
            ),
            StageConfig(
                name="content_integration",
                timeout=120,
                dependencies=["content_generation"],
                parameters={
                    "merge_strategy": "sequential",
                    "add_transitions": True,
                    "validate_consistency": True
                }
            )
        ]
        
        config = PipelineConfig(
            name="generation_only",
            description="仅执行内容生成阶段",
            workflow_type=WorkflowType.GENERATION_ONLY,
            execution_mode=ExecutionMode.HYBRID,
            error_handling=ErrorHandling.CONTINUE_ON_ERROR,
            max_execution_time=900,
            stages=stages
        )
        
        self.pipeline_configs["generation_only"] = config
    
    def _create_refinement_only_config(self):
        """创建仅优化配置"""
        stages = [
            StageConfig(
                name="quality_analysis",
                timeout=120,
                parameters={
                    "analyze_readability": True,
                    "analyze_completeness": True,
                    "analyze_coherence": True,
                    "analyze_accuracy": True
                }
            ),
            StageConfig(
                name="content_optimization",
                timeout=300,
                dependencies=["quality_analysis"],
                parameters={
                    "improve_quality": True,
                    "optimize_structure": True,
                    "enhance_clarity": True
                }
            ),
            StageConfig(
                name="style_refinement",
                timeout=200,
                dependencies=["content_optimization"],
                parameters={
                    "target_style": "professional",
                    "maintain_consistency": True,
                    "improve_flow": True
                }
            ),
            StageConfig(
                name="final_review",
                timeout=100,
                dependencies=["style_refinement"],
                parameters={
                    "comprehensive_check": True,
                    "generate_report": True,
                    "quality_threshold": 0.85
                }
            )
        ]
        
        config = PipelineConfig(
            name="refinement_only",
            description="仅执行内容优化阶段",
            workflow_type=WorkflowType.REFINEMENT_ONLY,
            execution_mode=ExecutionMode.SEQUENTIAL,
            error_handling=ErrorHandling.RETRY_ON_ERROR,
            max_execution_time=800,
            stages=stages
        )
        
        self.pipeline_configs["refinement_only"] = config
    
    def _load_default_parameters(self):
        """加载默认参数配置"""
        self.default_configs = {
            "planning": {
                "strategy": "adaptive",
                "max_sections": 20,
                "preferred_length": 5000,
                "quality_threshold": 0.7,
                "enable_evaluation": True,
                "enable_optimization": True
            },
            "generation": {
                "parallel_generation": True,
                "max_concurrent_tasks": 5,
                "quality_threshold": 0.7,
                "enable_validation": True,
                "auto_retry_failed": True,
                "retry_attempts": 2
            },
            "refinement": {
                "enable_quality_improvement": True,
                "enable_style_refinement": True,
                "enable_consistency_check": True,
                "target_quality": 0.8,
                "max_iterations": 3
            },
            "monitoring": {
                "enable_logging": True,
                "log_level": "INFO",
                "enable_metrics": True,
                "enable_alerts": False,
                "performance_tracking": True
            },
            "caching": {
                "enable_cache": True,
                "cache_ttl": 3600,
                "cache_size_limit": "100MB",
                "cache_strategy": "lru"
            }
        }
    
    def _load_config_from_file(self, config_file: str):
        """从文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载流水线配置
            if "pipelines" in config_data:
                for pipeline_name, pipeline_data in config_data["pipelines"].items():
                    self._load_pipeline_config(pipeline_name, pipeline_data)
            
            # 加载默认参数
            if "default_configs" in config_data:
                self.default_configs.update(config_data["default_configs"])
            
            logger.info(f"工作流配置已从文件加载: {config_file}")
            
        except Exception as e:
            logger.error(f"加载工作流配置文件失败: {str(e)}")
    
    def _load_pipeline_config(self, pipeline_name: str, pipeline_data: Dict[str, Any]):
        """加载流水线配置"""
        try:
            # 加载阶段配置
            stages = []
            for stage_data in pipeline_data.get("stages", []):
                stage = StageConfig(
                    name=stage_data["name"],
                    enabled=stage_data.get("enabled", True),
                    timeout=stage_data.get("timeout", 300),
                    retry_attempts=stage_data.get("retry_attempts", 3),
                    retry_delay=stage_data.get("retry_delay", 5),
                    dependencies=stage_data.get("dependencies", []),
                    parameters=stage_data.get("parameters", {})
                )
                stages.append(stage)
            
            # 创建流水线配置
            config = PipelineConfig(
                name=pipeline_name,
                description=pipeline_data.get("description", ""),
                workflow_type=WorkflowType(pipeline_data.get("workflow_type", "custom")),
                execution_mode=ExecutionMode(pipeline_data.get("execution_mode", "sequential")),
                error_handling=ErrorHandling(pipeline_data.get("error_handling", "fail_fast")),
                max_execution_time=pipeline_data.get("max_execution_time", 1800),
                enable_monitoring=pipeline_data.get("enable_monitoring", True),
                enable_caching=pipeline_data.get("enable_caching", True),
                stages=stages
            )
            
            self.pipeline_configs[pipeline_name] = config
            
        except Exception as e:
            logger.error(f"加载流水线配置 {pipeline_name} 失败: {str(e)}")
    
    def get_pipeline_config(self, pipeline_name: str) -> Optional[PipelineConfig]:
        """获取流水线配置"""
        return self.pipeline_configs.get(pipeline_name)
    
    def get_default_config(self, config_category: str) -> Dict[str, Any]:
        """获取默认配置"""
        return self.default_configs.get(config_category, {})
    
    def create_custom_pipeline(
        self,
        name: str,
        description: str,
        stages: List[StageConfig],
        execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL,
        error_handling: ErrorHandling = ErrorHandling.FAIL_FAST,
        max_execution_time: int = 1800
    ) -> PipelineConfig:
        """创建自定义流水线"""
        config = PipelineConfig(
            name=name,
            description=description,
            workflow_type=WorkflowType.CUSTOM,
            execution_mode=execution_mode,
            error_handling=error_handling,
            max_execution_time=max_execution_time,
            stages=stages
        )
        
        self.pipeline_configs[name] = config
        return config
    
    def update_pipeline_config(self, pipeline_name: str, updates: Dict[str, Any]):
        """更新流水线配置"""
        if pipeline_name not in self.pipeline_configs:
            logger.warning(f"流水线配置 {pipeline_name} 不存在")
            return
        
        config = self.pipeline_configs[pipeline_name]
        
        # 更新基本属性
        for attr in ["description", "execution_mode", "error_handling", "max_execution_time"]:
            if attr in updates:
                setattr(config, attr, updates[attr])
        
        # 更新阶段配置
        if "stages" in updates:
            for stage_update in updates["stages"]:
                stage_name = stage_update.get("name")
                if stage_name:
                    self._update_stage_config(config, stage_name, stage_update)
    
    def _update_stage_config(self, config: PipelineConfig, stage_name: str, updates: Dict[str, Any]):
        """更新阶段配置"""
        for stage in config.stages:
            if stage.name == stage_name:
                for attr in ["enabled", "timeout", "retry_attempts", "retry_delay"]:
                    if attr in updates:
                        setattr(stage, attr, updates[attr])
                
                if "parameters" in updates:
                    stage.parameters.update(updates["parameters"])
                
                break
    
    def validate_pipeline_config(self, pipeline_name: str) -> Dict[str, Any]:
        """验证流水线配置"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        config = self.get_pipeline_config(pipeline_name)
        if not config:
            validation_result["errors"].append(f"流水线配置 {pipeline_name} 不存在")
            validation_result["is_valid"] = False
            return validation_result
        
        # 验证阶段依赖
        stage_names = {stage.name for stage in config.stages}
        for stage in config.stages:
            for dependency in stage.dependencies:
                if dependency not in stage_names:
                    validation_result["errors"].append(f"阶段 {stage.name} 的依赖 {dependency} 不存在")
                    validation_result["is_valid"] = False
        
        # 验证超时设置
        total_timeout = sum(stage.timeout for stage in config.stages if stage.enabled)
        if total_timeout > config.max_execution_time:
            validation_result["warnings"].append("阶段超时总和超过最大执行时间")
        
        # 验证循环依赖
        if self._has_circular_dependencies(config.stages):
            validation_result["errors"].append("存在循环依赖")
            validation_result["is_valid"] = False
        
        return validation_result
    
    def _has_circular_dependencies(self, stages: List[StageConfig]) -> bool:
        """检查是否存在循环依赖"""
        # 简化的循环依赖检查
        stage_deps = {stage.name: set(stage.dependencies) for stage in stages}
        
        def has_cycle(node, visited, rec_stack):
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in stage_deps.get(node, set()):
                if neighbor not in visited:
                    if has_cycle(neighbor, visited, rec_stack):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node)
            return False
        
        visited = set()
        for stage_name in stage_deps:
            if stage_name not in visited:
                if has_cycle(stage_name, visited, set()):
                    return True
        
        return False
    
    def list_pipeline_configs(self) -> List[str]:
        """列出所有流水线配置"""
        return list(self.pipeline_configs.keys())
    
    def save_config(self, config_file: Optional[str] = None):
        """保存配置到文件"""
        config_file = config_file or self.config_file
        if not config_file:
            logger.warning("未指定配置文件路径")
            return
        
        try:
            config_data = {
                "pipelines": {},
                "default_configs": self.default_configs
            }
            
            # 序列化流水线配置
            for name, config in self.pipeline_configs.items():
                config_data["pipelines"][name] = self._serialize_pipeline_config(config)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"工作流配置已保存到文件: {config_file}")
            
        except Exception as e:
            logger.error(f"保存工作流配置文件失败: {str(e)}")
    
    def _serialize_pipeline_config(self, config: PipelineConfig) -> Dict[str, Any]:
        """序列化流水线配置"""
        return {
            "description": config.description,
            "workflow_type": config.workflow_type.value,
            "execution_mode": config.execution_mode.value,
            "error_handling": config.error_handling.value,
            "max_execution_time": config.max_execution_time,
            "enable_monitoring": config.enable_monitoring,
            "enable_caching": config.enable_caching,
            "stages": [
                {
                    "name": stage.name,
                    "enabled": stage.enabled,
                    "timeout": stage.timeout,
                    "retry_attempts": stage.retry_attempts,
                    "retry_delay": stage.retry_delay,
                    "dependencies": stage.dependencies,
                    "parameters": stage.parameters
                }
                for stage in config.stages
            ]
        }
