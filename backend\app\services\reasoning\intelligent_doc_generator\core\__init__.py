"""
智能文档生成器核心组件

提供文档生成的核心功能组件：
- DocumentPlanner: 文档结构规划
- ContentGenerator: 智能内容生成
- TemplateManager: AI提示词模板管理
- ContextAnalyzer: 项目上下文分析
- QualityController: 文档质量控制
"""

from .doc_planner import DocumentPlanner, PlannerStrategy, AdaptivePlanner
from .content_generator import ContentGenerator, ContentStyle, StructuredStyle, NarrativeStyle
from .template_manager import TemplateManager, AITemplate, TemplateVersion
from .context_analyzer import ContextAnalyzer, ProjectFeatures
from .quality_controller import QualityController, QualityReport, QualityMetrics, QualityIssue

__all__ = [
    # 主要组件
    "DocumentPlanner",
    "ContentGenerator", 
    "TemplateManager",
    "ContextAnalyzer",
    "QualityController",
    
    # 规划器相关
    "PlannerStrategy",
    "AdaptivePlanner",
    
    # 生成器相关
    "ContentStyle",
    "StructuredStyle", 
    "NarrativeStyle",
    
    # 模板管理相关
    "AITemplate",
    "TemplateVersion",
    
    # 上下文分析相关
    "ProjectFeatures",
    
    # 质量控制相关
    "QualityReport",
    "QualityMetrics",
    "QualityIssue"
]
