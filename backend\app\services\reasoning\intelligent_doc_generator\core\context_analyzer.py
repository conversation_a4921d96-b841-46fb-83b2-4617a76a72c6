"""
项目上下文分析器

提取和分析项目特征，为智能文档生成提供上下文信息，支持：
- 项目类型识别
- 技术栈分析
- 功能特征提取
- 目标受众推断
"""

import os
import re
from typing import Dict, Any, List, Optional, Set
from pathlib import Path
from dataclasses import dataclass, field


@dataclass
class ProjectFeatures:
    """项目特征"""
    project_type: str = "unknown"
    primary_language: str = "unknown"
    frameworks: List[str] = field(default_factory=list)
    has_api: bool = False
    has_web_ui: bool = False
    has_cli: bool = False
    has_tests: bool = False
    has_docs: bool = False
    has_database: bool = False
    has_deployment: bool = False
    has_monitoring: bool = False
    complexity_level: str = "medium"  # simple, medium, complex
    target_audience: str = "developers"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "project_type": self.project_type,
            "primary_language": self.primary_language,
            "frameworks": self.frameworks,
            "has_api": self.has_api,
            "has_web_ui": self.has_web_ui,
            "has_cli": self.has_cli,
            "has_tests": self.has_tests,
            "has_docs": self.has_docs,
            "has_database": self.has_database,
            "has_deployment": self.has_deployment,
            "has_monitoring": self.has_monitoring,
            "complexity_level": self.complexity_level,
            "target_audience": self.target_audience
        }


class ContextAnalyzer:
    """项目上下文分析器"""
    
    def __init__(self):
        self.project_type_patterns = {
            "web_app": [
                r"app\.py", r"main\.py", r"server\.py", r"wsgi\.py",
                r"package\.json", r"index\.html", r"app\.js"
            ],
            "api_service": [
                r"api/", r"routes/", r"endpoints/", r"handlers/",
                r"fastapi", r"flask", r"express", r"router"
            ],
            "library": [
                r"setup\.py", r"__init__\.py", r"lib/", r"src/",
                r"package\.json.*library"
            ],
            "cli_tool": [
                r"cli\.py", r"command\.py", r"main\.py.*argparse",
                r"bin/", r"scripts/"
            ],
            "data_analysis": [
                r"\.ipynb", r"jupyter", r"pandas", r"numpy",
                r"analysis", r"data/"
            ],
            "ml_project": [
                r"model", r"train", r"predict", r"tensorflow",
                r"pytorch", r"sklearn", r"ml/"
            ]
        }
        
        self.framework_patterns = {
            "fastapi": [r"fastapi", r"from fastapi"],
            "flask": [r"flask", r"from flask"],
            "django": [r"django", r"manage\.py"],
            "tornado": [r"tornado", r"from tornado"],
            "react": [r"react", r"package\.json.*react"],
            "vue": [r"vue", r"package\.json.*vue"],
            "angular": [r"angular", r"@angular"],
            "express": [r"express", r"package\.json.*express"],
            "spring": [r"spring", r"@SpringBootApplication"],
            "laravel": [r"laravel", r"composer\.json.*laravel"]
        }
        
        self.feature_patterns = {
            "has_api": [
                r"/api/", r"@app\.route", r"@router\.", r"endpoints",
                r"swagger", r"openapi", r"rest"
            ],
            "has_web_ui": [
                r"templates/", r"static/", r"public/", r"assets/",
                r"\.html", r"\.css", r"\.js", r"frontend"
            ],
            "has_cli": [
                r"argparse", r"click", r"typer", r"command",
                r"bin/", r"scripts/", r"cli"
            ],
            "has_tests": [
                r"test_", r"tests/", r"pytest", r"unittest",
                r"jest", r"mocha", r"spec"
            ],
            "has_docs": [
                r"docs/", r"documentation/", r"README", r"sphinx",
                r"mkdocs", r"gitbook"
            ],
            "has_database": [
                r"models", r"database", r"db", r"sql", r"mongo",
                r"redis", r"postgresql", r"mysql"
            ],
            "has_deployment": [
                r"Dockerfile", r"docker-compose", r"kubernetes",
                r"deployment", r"deploy", r"heroku", r"aws"
            ],
            "has_monitoring": [
                r"logging", r"metrics", r"monitoring", r"prometheus",
                r"grafana", r"elk", r"sentry"
            ]
        }
    
    def analyze_project(self, project_path: str, analysis_data: Dict[str, Any] = None) -> ProjectFeatures:
        """分析项目特征"""
        features = ProjectFeatures()
        
        # 如果提供了分析数据，优先使用
        if analysis_data:
            features = self._analyze_from_data(analysis_data)
        
        # 基于文件系统分析
        if os.path.exists(project_path):
            file_features = self._analyze_file_structure(project_path)
            features = self._merge_features(features, file_features)
        
        # 推断复杂度和目标受众
        features.complexity_level = self._infer_complexity(features)
        features.target_audience = self._infer_target_audience(features)
        
        return features
    
    def _analyze_from_data(self, analysis_data: Dict[str, Any]) -> ProjectFeatures:
        """基于分析数据提取特征"""
        features = ProjectFeatures()
        
        # 分析技术栈信息
        tech_stack = analysis_data.get("tech_stack", {})
        if tech_stack:
            languages = tech_stack.get("languages", [])
            if languages:
                features.primary_language = languages[0].get("name", "unknown").lower()
            
            frameworks = tech_stack.get("frameworks", [])
            features.frameworks = [f.get("name", "") for f in frameworks if f.get("name")]
        
        # 分析依赖信息
        dependencies = analysis_data.get("dependencies", {})
        if dependencies:
            external_deps = dependencies.get("external_dependencies", [])
            dep_names = [dep.get("name", "").lower() for dep in external_deps]
            
            # 检测框架
            for framework, patterns in self.framework_patterns.items():
                if any(pattern in " ".join(dep_names) for pattern in patterns):
                    if framework not in features.frameworks:
                        features.frameworks.append(framework)
        
        # 分析项目结构
        structure = analysis_data.get("structure", {})
        if structure:
            files = structure.get("files", [])
            directories = structure.get("directories", [])
            
            all_paths = [f.get("path", "") for f in files] + [d.get("path", "") for d in directories]
            path_text = " ".join(all_paths).lower()
            
            # 检测功能特征
            for feature_name, patterns in self.feature_patterns.items():
                if any(re.search(pattern, path_text, re.IGNORECASE) for pattern in patterns):
                    setattr(features, feature_name, True)
        
        # 分析模块信息
        modules = analysis_data.get("modules", {})
        if modules:
            ai_analysis = modules.get("ai_analysis", {})
            if ai_analysis:
                insights = ai_analysis.get("insights", [])
                for insight in insights:
                    insight_text = insight.get("description", "").lower()
                    
                    # 基于AI洞察推断特征
                    if any(keyword in insight_text for keyword in ["api", "rest", "endpoint"]):
                        features.has_api = True
                    if any(keyword in insight_text for keyword in ["web", "frontend", "ui"]):
                        features.has_web_ui = True
                    if any(keyword in insight_text for keyword in ["cli", "command", "terminal"]):
                        features.has_cli = True
        
        # 推断项目类型
        features.project_type = self._infer_project_type(features)
        
        return features
    
    def _analyze_file_structure(self, project_path: str) -> ProjectFeatures:
        """基于文件结构分析"""
        features = ProjectFeatures()
        project_path = Path(project_path)
        
        # 收集所有文件路径
        all_files = []
        try:
            for file_path in project_path.rglob("*"):
                if file_path.is_file():
                    relative_path = file_path.relative_to(project_path)
                    all_files.append(str(relative_path))
        except Exception as e:
            print(f"分析文件结构时出错: {e}")
            return features
        
        # 分析文件模式
        file_text = " ".join(all_files).lower()
        
        # 检测项目类型
        for project_type, patterns in self.project_type_patterns.items():
            if any(re.search(pattern, file_text, re.IGNORECASE) for pattern in patterns):
                features.project_type = project_type
                break
        
        # 检测框架
        for framework, patterns in self.framework_patterns.items():
            if any(re.search(pattern, file_text, re.IGNORECASE) for pattern in patterns):
                features.frameworks.append(framework)
        
        # 检测功能特征
        for feature_name, patterns in self.feature_patterns.items():
            if any(re.search(pattern, file_text, re.IGNORECASE) for pattern in patterns):
                setattr(features, feature_name, True)
        
        # 推断主要语言
        language_extensions = {
            "python": [".py"],
            "javascript": [".js", ".ts"],
            "java": [".java"],
            "go": [".go"],
            "rust": [".rs"],
            "cpp": [".cpp", ".cc", ".cxx"],
            "csharp": [".cs"],
            "php": [".php"]
        }
        
        extension_counts = {}
        for file_path in all_files:
            ext = Path(file_path).suffix.lower()
            if ext:
                extension_counts[ext] = extension_counts.get(ext, 0) + 1
        
        if extension_counts:
            most_common_ext = max(extension_counts.items(), key=lambda x: x[1])[0]
            for language, extensions in language_extensions.items():
                if most_common_ext in extensions:
                    features.primary_language = language
                    break
        
        return features
    
    def _merge_features(self, features1: ProjectFeatures, features2: ProjectFeatures) -> ProjectFeatures:
        """合并特征"""
        merged = ProjectFeatures()
        
        # 选择更具体的项目类型
        if features1.project_type != "unknown":
            merged.project_type = features1.project_type
        elif features2.project_type != "unknown":
            merged.project_type = features2.project_type
        
        # 选择更具体的主要语言
        if features1.primary_language != "unknown":
            merged.primary_language = features1.primary_language
        elif features2.primary_language != "unknown":
            merged.primary_language = features2.primary_language
        
        # 合并框架列表
        merged.frameworks = list(set(features1.frameworks + features2.frameworks))
        
        # 合并布尔特征（任一为True则为True）
        bool_features = [
            "has_api", "has_web_ui", "has_cli", "has_tests", 
            "has_docs", "has_database", "has_deployment", "has_monitoring"
        ]
        
        for feature in bool_features:
            value1 = getattr(features1, feature, False)
            value2 = getattr(features2, feature, False)
            setattr(merged, feature, value1 or value2)
        
        return merged
    
    def _infer_project_type(self, features: ProjectFeatures) -> str:
        """推断项目类型"""
        if features.has_api and features.has_web_ui:
            return "web_app"
        elif features.has_api:
            return "api_service"
        elif features.has_cli:
            return "cli_tool"
        elif features.has_web_ui:
            return "web_app"
        elif "django" in features.frameworks or "flask" in features.frameworks:
            return "web_app"
        elif "fastapi" in features.frameworks:
            return "api_service"
        else:
            return features.project_type
    
    def _infer_complexity(self, features: ProjectFeatures) -> str:
        """推断复杂度"""
        complexity_score = 0
        
        # 基础特征得分
        if features.has_api:
            complexity_score += 2
        if features.has_web_ui:
            complexity_score += 2
        if features.has_database:
            complexity_score += 2
        if features.has_deployment:
            complexity_score += 1
        if features.has_monitoring:
            complexity_score += 1
        if features.has_tests:
            complexity_score += 1
        
        # 框架数量得分
        complexity_score += len(features.frameworks)
        
        if complexity_score <= 3:
            return "simple"
        elif complexity_score <= 7:
            return "medium"
        else:
            return "complex"
    
    def _infer_target_audience(self, features: ProjectFeatures) -> str:
        """推断目标受众"""
        if features.has_cli and not features.has_web_ui:
            return "developers"
        elif features.has_web_ui and not features.has_api:
            return "end_users"
        elif features.project_type == "library":
            return "developers"
        elif features.project_type == "web_app":
            return "end_users"
        elif features.project_type == "api_service":
            return "developers"
        else:
            return "developers"
    
    def extract_documentation_requirements(self, features: ProjectFeatures) -> Dict[str, Any]:
        """提取文档需求"""
        requirements = {
            "必需章节": ["overview", "installation"],
            "推荐章节": [],
            "可选章节": [],
            "特殊要求": []
        }
        
        # 基于功能特征推荐章节
        if features.has_api:
            requirements["推荐章节"].extend(["api_reference", "examples"])
        
        if features.has_cli:
            requirements["推荐章节"].append("cli_usage")
        
        if features.has_web_ui:
            requirements["推荐章节"].append("user_guide")
        
        if features.has_tests:
            requirements["可选章节"].append("testing")
        
        if features.has_deployment:
            requirements["推荐章节"].append("deployment")
        
        if features.complexity_level == "complex":
            requirements["推荐章节"].extend(["architecture", "troubleshooting"])
        
        # 基于目标受众调整
        if features.target_audience == "end_users":
            requirements["特殊要求"].append("使用通俗易懂的语言")
            requirements["特殊要求"].append("提供详细的操作步骤")
        else:
            requirements["特殊要求"].append("包含技术细节和代码示例")
        
        return requirements
