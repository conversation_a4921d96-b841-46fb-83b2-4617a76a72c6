"""
智能文档规划器

基于AI驱动的文档结构规划组件，支持：
- 自适应章节规划
- 依赖关系分析
- 拓扑排序
- 多策略规划
"""

import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Set
from abc import ABC, abstractmethod

from ..models.document_models import IntelligentDocument, DynamicSection
from ..models.planning_models import (
    DocumentPlan, SectionPlan, PlanningStrategy, PlanningContext, 
    PlanEvaluation, SectionType, PriorityLevel
)


class PlannerStrategy(ABC):
    """规划策略抽象基类"""
    
    @abstractmethod
    async def plan(self, context: PlanningContext) -> DocumentPlan:
        """执行规划"""
        pass


class AdaptivePlanner(PlannerStrategy):
    """自适应规划器"""
    
    async def plan(self, context: PlanningContext) -> DocumentPlan:
        """基于项目特征自适应规划文档结构"""
        sections = []
        
        # 基础章节
        sections.extend(self._plan_basic_sections(context))
        
        # 特征驱动章节
        if context.project_features.get("has_api"):
            sections.append(self._create_api_section())
        
        if context.project_features.get("has_tests"):
            sections.append(self._create_testing_section())
        
        if context.project_features.get("has_deployment"):
            sections.append(self._create_deployment_section())
        
        # 构建规划
        plan = DocumentPlan(
            plan_id=str(uuid.uuid4()),
            document_id=context.document_id,
            strategy=PlanningStrategy.ADAPTIVE,
            sections=sections,
            created_at=datetime.now(timezone.utc)
        )
        
        # 分析依赖关系
        plan.analyze_dependencies()
        
        return plan
    
    def _plan_basic_sections(self, context: PlanningContext) -> List[SectionPlan]:
        """规划基础章节"""
        return [
            SectionPlan(
                section_id="overview",
                title="项目概述",
                section_type=SectionType.OVERVIEW,
                priority=PriorityLevel.HIGH,
                estimated_length=300
            ),
            SectionPlan(
                section_id="features",
                title="功能特性",
                section_type=SectionType.FEATURES,
                priority=PriorityLevel.HIGH,
                estimated_length=500
            ),
            SectionPlan(
                section_id="installation",
                title="安装指南",
                section_type=SectionType.INSTALLATION,
                priority=PriorityLevel.HIGH,
                estimated_length=400
            ),
            SectionPlan(
                section_id="usage",
                title="使用说明",
                section_type=SectionType.USAGE,
                priority=PriorityLevel.HIGH,
                estimated_length=600
            )
        ]
    
    def _create_api_section(self) -> SectionPlan:
        """创建API章节"""
        return SectionPlan(
            section_id="api",
            title="API参考",
            section_type=SectionType.API_REFERENCE,
            priority=PriorityLevel.MEDIUM,
            estimated_length=800,
            dependencies=["usage"]
        )
    
    def _create_testing_section(self) -> SectionPlan:
        """创建测试章节"""
        return SectionPlan(
            section_id="testing",
            title="测试指南",
            section_type=SectionType.TESTING,
            priority=PriorityLevel.MEDIUM,
            estimated_length=400,
            dependencies=["installation"]
        )
    
    def _create_deployment_section(self) -> SectionPlan:
        """创建部署章节"""
        return SectionPlan(
            section_id="deployment",
            title="部署指南",
            section_type=SectionType.DEPLOYMENT,
            priority=PriorityLevel.MEDIUM,
            estimated_length=500,
            dependencies=["installation", "testing"]
        )


class DocumentPlanner:
    """智能文档规划器"""
    
    def __init__(self):
        self.strategies = {
            PlanningStrategy.ADAPTIVE: AdaptivePlanner(),
        }
    
    async def create_plan(
        self,
        document_id: str,
        context: PlanningContext,
        strategy: PlanningStrategy = PlanningStrategy.ADAPTIVE
    ) -> DocumentPlan:
        """创建文档规划"""
        planner = self.strategies.get(strategy)
        if not planner:
            raise ValueError(f"不支持的规划策略: {strategy}")
        
        # 执行规划
        plan = await planner.plan(context)
        
        # 验证规划
        validation_result = await self.validate_plan(plan, context)
        plan.validation_result = validation_result
        
        return plan
    
    async def validate_plan(self, plan: DocumentPlan, context: PlanningContext) -> Dict[str, Any]:
        """验证规划方案"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # 检查必需章节
        required_sections = {"overview", "features", "installation", "usage"}
        existing_sections = {section.section_id for section in plan.sections}
        missing_sections = required_sections - existing_sections
        
        if missing_sections:
            validation_result["warnings"].append(f"缺少建议章节: {missing_sections}")
        
        # 检查依赖循环
        if plan.has_circular_dependencies():
            validation_result["errors"].append("存在循环依赖")
            validation_result["is_valid"] = False
        
        # 检查估计长度
        total_length = sum(section.estimated_length for section in plan.sections)
        if total_length > 5000:
            validation_result["warnings"].append("文档可能过长，建议精简内容")
        
        return validation_result
    
    async def optimize_plan(self, plan: DocumentPlan, context: PlanningContext) -> DocumentPlan:
        """优化规划方案"""
        # 重新排序章节
        ordered_sections = plan.get_topological_order()
        plan.sections = [plan.get_section(sid) for sid in ordered_sections if plan.get_section(sid)]
        
        # 调整优先级
        self._adjust_priorities(plan)
        
        # 更新估计时间
        plan.estimated_completion_time = self._estimate_completion_time(plan)
        
        return plan
    
    def _adjust_priorities(self, plan: DocumentPlan):
        """调整章节优先级"""
        # 基础章节保持高优先级
        basic_sections = {"overview", "features", "installation", "usage"}
        
        for section in plan.sections:
            if section.section_id in basic_sections:
                section.priority = PriorityLevel.HIGH
            elif len(section.dependencies) == 0:
                # 无依赖的章节提高优先级
                section.priority = PriorityLevel.MEDIUM
    
    def _estimate_completion_time(self, plan: DocumentPlan) -> float:
        """估计完成时间（分钟）"""
        base_time_per_word = 0.1  # 每字0.1分钟
        total_words = sum(section.estimated_length for section in plan.sections)
        
        # 考虑AI生成时间
        ai_generation_time = len(plan.sections) * 2  # 每章节2分钟
        
        return total_words * base_time_per_word + ai_generation_time
