"""
文档质量控制器

评估和控制生成文档的质量，支持：
- 多维度质量评估
- 自动质量检测
- 质量改进建议
- 质量报告生成
"""

import re
import math
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timezone

from ..models.generation_models import GenerationResult, QualityLevel


@dataclass
class QualityMetrics:
    """质量评估指标"""
    content_length: int = 0
    sentence_count: int = 0
    paragraph_count: int = 0
    code_block_count: int = 0
    link_count: int = 0
    heading_count: int = 0
    
    # 可读性指标
    avg_sentence_length: float = 0.0
    avg_paragraph_length: float = 0.0
    readability_score: float = 0.0
    
    # 结构指标
    has_clear_structure: bool = False
    has_code_examples: bool = False
    has_proper_headings: bool = False
    
    # 完整性指标
    completeness_score: float = 0.0
    coverage_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "content_length": self.content_length,
            "sentence_count": self.sentence_count,
            "paragraph_count": self.paragraph_count,
            "code_block_count": self.code_block_count,
            "link_count": self.link_count,
            "heading_count": self.heading_count,
            "avg_sentence_length": self.avg_sentence_length,
            "avg_paragraph_length": self.avg_paragraph_length,
            "readability_score": self.readability_score,
            "has_clear_structure": self.has_clear_structure,
            "has_code_examples": self.has_code_examples,
            "has_proper_headings": self.has_proper_headings,
            "completeness_score": self.completeness_score,
            "coverage_score": self.coverage_score
        }


@dataclass
class QualityIssue:
    """质量问题"""
    issue_type: str
    severity: str  # low, medium, high, critical
    description: str
    location: Optional[str] = None
    suggestion: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "issue_type": self.issue_type,
            "severity": self.severity,
            "description": self.description,
            "location": self.location,
            "suggestion": self.suggestion
        }


@dataclass
class QualityReport:
    """质量报告"""
    overall_score: float
    quality_level: QualityLevel
    metrics: QualityMetrics
    issues: List[QualityIssue] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "overall_score": self.overall_score,
            "quality_level": self.quality_level.value,
            "metrics": self.metrics.to_dict(),
            "issues": [issue.to_dict() for issue in self.issues],
            "suggestions": self.suggestions,
            "created_at": self.created_at.isoformat(),
            "issue_count_by_severity": self._count_issues_by_severity()
        }
    
    def _count_issues_by_severity(self) -> Dict[str, int]:
        """按严重程度统计问题"""
        counts = {"critical": 0, "high": 0, "medium": 0, "low": 0}
        for issue in self.issues:
            counts[issue.severity] = counts.get(issue.severity, 0) + 1
        return counts


class QualityController:
    """文档质量控制器"""
    
    def __init__(self):
        self.quality_thresholds = {
            QualityLevel.HIGH: 0.8,
            QualityLevel.STANDARD: 0.6,
            QualityLevel.BASIC: 0.4
        }
        
        self.quality_checkers = [
            self._check_content_length,
            self._check_structure,
            self._check_readability,
            self._check_completeness,
            self._check_formatting,
            self._check_code_examples
        ]
    
    def evaluate_content(self, content: str, section_type: str = None, requirements: Dict[str, Any] = None) -> QualityReport:
        """评估内容质量"""
        # 计算质量指标
        metrics = self._calculate_metrics(content)
        
        # 检测质量问题
        issues = []
        for checker in self.quality_checkers:
            checker_issues = checker(content, section_type, metrics, requirements)
            issues.extend(checker_issues)
        
        # 计算总体得分
        overall_score = self._calculate_overall_score(metrics, issues)
        
        # 确定质量等级
        quality_level = self._determine_quality_level(overall_score)
        
        # 生成改进建议
        suggestions = self._generate_suggestions(issues, metrics)
        
        return QualityReport(
            overall_score=overall_score,
            quality_level=quality_level,
            metrics=metrics,
            issues=issues,
            suggestions=suggestions
        )
    
    def evaluate_generation_result(self, result: GenerationResult, requirements: Dict[str, Any] = None) -> QualityReport:
        """评估生成结果质量"""
        return self.evaluate_content(
            content=result.generated_content,
            section_type=result.section_id,
            requirements=requirements
        )
    
    def batch_evaluate(self, results: List[GenerationResult], requirements: Dict[str, Any] = None) -> Dict[str, QualityReport]:
        """批量评估质量"""
        reports = {}
        for result in results:
            report = self.evaluate_generation_result(result, requirements)
            reports[result.section_id] = report
        return reports
    
    def _calculate_metrics(self, content: str) -> QualityMetrics:
        """计算质量指标"""
        metrics = QualityMetrics()
        
        # 基础统计
        metrics.content_length = len(content)
        
        # 句子统计
        sentences = re.split(r'[.!?。！？]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        metrics.sentence_count = len(sentences)
        
        if metrics.sentence_count > 0:
            metrics.avg_sentence_length = metrics.content_length / metrics.sentence_count
        
        # 段落统计
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        metrics.paragraph_count = len(paragraphs)
        
        if metrics.paragraph_count > 0:
            metrics.avg_paragraph_length = metrics.content_length / metrics.paragraph_count
        
        # 代码块统计
        metrics.code_block_count = len(re.findall(r'```[\s\S]*?```', content))
        
        # 链接统计
        metrics.link_count = len(re.findall(r'\[.*?\]\(.*?\)', content))
        
        # 标题统计
        metrics.heading_count = len(re.findall(r'^#+\s+', content, re.MULTILINE))
        
        # 可读性评分（简化版Flesch Reading Ease）
        if metrics.sentence_count > 0:
            avg_words_per_sentence = len(content.split()) / metrics.sentence_count
            # 简化公式，实际应该更复杂
            metrics.readability_score = max(0, min(100, 100 - avg_words_per_sentence * 2))
        
        # 结构检查
        metrics.has_clear_structure = metrics.heading_count > 0
        metrics.has_code_examples = metrics.code_block_count > 0
        metrics.has_proper_headings = self._check_heading_hierarchy(content)
        
        return metrics
    
    def _check_heading_hierarchy(self, content: str) -> bool:
        """检查标题层次结构"""
        headings = re.findall(r'^(#+)\s+', content, re.MULTILINE)
        if not headings:
            return False
        
        heading_levels = [len(h) for h in headings]
        
        # 检查是否有合理的层次结构
        if len(set(heading_levels)) > 1:
            return max(heading_levels) - min(heading_levels) <= 3
        
        return True
    
    def _check_content_length(self, content: str, section_type: str, metrics: QualityMetrics, requirements: Dict[str, Any]) -> List[QualityIssue]:
        """检查内容长度"""
        issues = []
        
        min_length = requirements.get("min_length", 100) if requirements else 100
        max_length = requirements.get("max_length", 2000) if requirements else 2000
        
        if metrics.content_length < min_length:
            issues.append(QualityIssue(
                issue_type="length",
                severity="medium",
                description=f"内容过短，当前长度 {metrics.content_length}，建议至少 {min_length} 字符",
                suggestion="增加更多详细说明和示例"
            ))
        
        if metrics.content_length > max_length:
            issues.append(QualityIssue(
                issue_type="length",
                severity="low",
                description=f"内容可能过长，当前长度 {metrics.content_length}，建议不超过 {max_length} 字符",
                suggestion="考虑精简内容或分割为多个章节"
            ))
        
        return issues
    
    def _check_structure(self, content: str, section_type: str, metrics: QualityMetrics, requirements: Dict[str, Any]) -> List[QualityIssue]:
        """检查内容结构"""
        issues = []
        
        if not metrics.has_clear_structure:
            issues.append(QualityIssue(
                issue_type="structure",
                severity="high",
                description="缺少清晰的结构化标题",
                suggestion="添加适当的标题来组织内容"
            ))
        
        if metrics.paragraph_count == 1 and metrics.content_length > 500:
            issues.append(QualityIssue(
                issue_type="structure",
                severity="medium",
                description="内容应该分段以提高可读性",
                suggestion="将长段落分解为多个较短的段落"
            ))
        
        if not metrics.has_proper_headings:
            issues.append(QualityIssue(
                issue_type="structure",
                severity="medium",
                description="标题层次结构不合理",
                suggestion="使用合理的标题层次（H1-H4）"
            ))
        
        return issues
    
    def _check_readability(self, content: str, section_type: str, metrics: QualityMetrics, requirements: Dict[str, Any]) -> List[QualityIssue]:
        """检查可读性"""
        issues = []
        
        if metrics.avg_sentence_length > 30:
            issues.append(QualityIssue(
                issue_type="readability",
                severity="medium",
                description=f"句子平均长度过长（{metrics.avg_sentence_length:.1f}字符）",
                suggestion="使用更简洁的句子表达"
            ))
        
        if metrics.readability_score < 30:
            issues.append(QualityIssue(
                issue_type="readability",
                severity="medium",
                description=f"可读性得分较低（{metrics.readability_score:.1f}）",
                suggestion="简化语言表达，使用更通俗易懂的词汇"
            ))
        
        return issues
    
    def _check_completeness(self, content: str, section_type: str, metrics: QualityMetrics, requirements: Dict[str, Any]) -> List[QualityIssue]:
        """检查完整性"""
        issues = []
        
        # 基于章节类型检查必需元素
        required_elements = {
            "installation": ["安装", "配置", "依赖"],
            "usage": ["使用", "示例", "方法"],
            "api": ["接口", "参数", "返回值"],
            "overview": ["介绍", "功能", "特点"]
        }
        
        if section_type in required_elements:
            required = required_elements[section_type]
            missing_elements = []
            
            for element in required:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                issues.append(QualityIssue(
                    issue_type="completeness",
                    severity="high",
                    description=f"缺少必需元素: {', '.join(missing_elements)}",
                    suggestion=f"添加关于 {', '.join(missing_elements)} 的相关内容"
                ))
        
        return issues
    
    def _check_formatting(self, content: str, section_type: str, metrics: QualityMetrics, requirements: Dict[str, Any]) -> List[QualityIssue]:
        """检查格式"""
        issues = []
        
        # 检查Markdown格式
        if '```' in content and content.count('```') % 2 != 0:
            issues.append(QualityIssue(
                issue_type="formatting",
                severity="high",
                description="代码块格式不完整",
                suggestion="确保代码块的开始和结束标记成对出现"
            ))
        
        # 检查链接格式
        broken_links = re.findall(r'\[.*?\]\(\s*\)', content)
        if broken_links:
            issues.append(QualityIssue(
                issue_type="formatting",
                severity="medium",
                description="存在空链接",
                suggestion="确保所有链接都有有效的URL"
            ))
        
        return issues
    
    def _check_code_examples(self, content: str, section_type: str, metrics: QualityMetrics, requirements: Dict[str, Any]) -> List[QualityIssue]:
        """检查代码示例"""
        issues = []
        
        # 对于需要代码示例的章节，检查是否包含示例
        code_required_sections = ["usage", "api", "examples", "tutorial"]
        
        if section_type in code_required_sections and metrics.code_block_count == 0:
            issues.append(QualityIssue(
                issue_type="code_examples",
                severity="high",
                description="缺少代码示例",
                suggestion="添加相关的代码示例来说明用法"
            ))
        
        return issues
    
    def _calculate_overall_score(self, metrics: QualityMetrics, issues: List[QualityIssue]) -> float:
        """计算总体质量得分"""
        base_score = 1.0
        
        # 基于指标调整得分
        if metrics.content_length < 50:
            base_score *= 0.5
        elif metrics.content_length < 100:
            base_score *= 0.7
        
        if not metrics.has_clear_structure:
            base_score *= 0.8
        
        if metrics.readability_score < 50:
            base_score *= 0.9
        
        # 基于问题严重程度扣分
        severity_penalties = {
            "critical": 0.3,
            "high": 0.15,
            "medium": 0.08,
            "low": 0.03
        }
        
        for issue in issues:
            penalty = severity_penalties.get(issue.severity, 0.05)
            base_score *= (1 - penalty)
        
        return max(0.0, min(1.0, base_score))
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """确定质量等级"""
        if score >= self.quality_thresholds[QualityLevel.HIGH]:
            return QualityLevel.HIGH
        elif score >= self.quality_thresholds[QualityLevel.STANDARD]:
            return QualityLevel.STANDARD
        else:
            return QualityLevel.BASIC
    
    def _generate_suggestions(self, issues: List[QualityIssue], metrics: QualityMetrics) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 从问题中提取建议
        for issue in issues:
            if issue.suggestion and issue.suggestion not in suggestions:
                suggestions.append(issue.suggestion)
        
        # 基于指标生成通用建议
        if metrics.content_length < 200:
            suggestions.append("增加更多详细信息和解释")
        
        if metrics.code_block_count == 0 and metrics.content_length > 300:
            suggestions.append("考虑添加代码示例来增强说明")
        
        if metrics.heading_count == 0:
            suggestions.append("使用标题来组织内容结构")
        
        return suggestions[:5]  # 最多返回5个建议
    
    def generate_quality_summary(self, reports: Dict[str, QualityReport]) -> Dict[str, Any]:
        """生成质量汇总报告"""
        if not reports:
            return {"message": "没有质量报告数据"}
        
        total_sections = len(reports)
        scores = [report.overall_score for report in reports.values()]
        avg_score = sum(scores) / len(scores)
        
        quality_levels = [report.quality_level for report in reports.values()]
        level_counts = {level: quality_levels.count(level) for level in QualityLevel}
        
        total_issues = sum(len(report.issues) for report in reports.values())
        
        return {
            "总章节数": total_sections,
            "平均质量得分": round(avg_score, 3),
            "质量等级分布": {level.value: count for level, count in level_counts.items()},
            "总问题数": total_issues,
            "需要改进的章节": [
                section_id for section_id, report in reports.items()
                if report.quality_level == QualityLevel.BASIC
            ],
            "高质量章节": [
                section_id for section_id, report in reports.items()
                if report.quality_level == QualityLevel.HIGH
            ]
        }
