"""
AI提示词模板管理器

管理智能文档生成的AI提示词模板，支持：
- 模板版本控制
- 动态模板加载
- 模板参数化
- 模板缓存
"""

import os
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template


class TemplateVersion:
    """模板版本信息"""
    
    def __init__(self, version: str, created_at: datetime, description: str = ""):
        self.version = version
        self.created_at = created_at
        self.description = description
        self.is_active = False


class AITemplate:
    """AI提示词模板"""
    
    def __init__(self, template_id: str, name: str, content: str, 
                 template_type: str = "generation", parameters: Dict[str, Any] = None):
        self.template_id = template_id
        self.name = name
        self.content = content
        self.template_type = template_type
        self.parameters = parameters or {}
        self.versions = []
        self.current_version = "1.0.0"
        self.created_at = datetime.now(timezone.utc)
        self.updated_at = self.created_at
        self.usage_count = 0
        
        # Jinja2模板对象
        self._jinja_template = None
    
    @property
    def jinja_template(self) -> Template:
        """获取Jinja2模板对象"""
        if self._jinja_template is None:
            env = Environment()
            self._jinja_template = env.from_string(self.content)
        return self._jinja_template
    
    def render(self, **kwargs) -> str:
        """渲染模板"""
        try:
            # 合并默认参数和传入参数
            render_params = {**self.parameters, **kwargs}
            rendered_content = self.jinja_template.render(**render_params)
            self.usage_count += 1
            return rendered_content
        except Exception as e:
            raise ValueError(f"模板渲染失败: {e}")
    
    def add_version(self, version: str, content: str, description: str = ""):
        """添加新版本"""
        # 保存当前版本
        current_version = TemplateVersion(
            version=self.current_version,
            created_at=self.updated_at,
            description="Previous version"
        )
        self.versions.append(current_version)
        
        # 更新到新版本
        self.content = content
        self.current_version = version
        self.updated_at = datetime.now(timezone.utc)
        self._jinja_template = None  # 重置模板对象
        
        new_version = TemplateVersion(
            version=version,
            created_at=self.updated_at,
            description=description
        )
        new_version.is_active = True
        self.versions.append(new_version)
    
    def get_version_history(self) -> List[Dict[str, Any]]:
        """获取版本历史"""
        return [
            {
                "version": v.version,
                "created_at": v.created_at.isoformat(),
                "description": v.description,
                "is_active": v.is_active
            }
            for v in self.versions
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "template_id": self.template_id,
            "name": self.name,
            "template_type": self.template_type,
            "current_version": self.current_version,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "usage_count": self.usage_count,
            "parameters": self.parameters,
            "version_count": len(self.versions)
        }


class TemplateManager:
    """AI提示词模板管理器"""
    
    def __init__(self, templates_dir: Optional[str] = None):
        self.templates_dir = Path(templates_dir) if templates_dir else Path(__file__).parent.parent / "templates"
        self.templates = {}
        self.template_cache = {}
        self.jinja_env = None
        
        # 确保模板目录存在
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Jinja2环境
        self._init_jinja_env()
        
        # 加载预定义模板
        self._load_default_templates()
    
    def _init_jinja_env(self):
        """初始化Jinja2环境"""
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
    
    def _load_default_templates(self):
        """加载默认模板"""
        default_templates = {
            "section_planning": {
                "name": "章节规划模板",
                "type": "planning",
                "content": """你是一个专业的技术文档结构规划专家。

项目信息：
- 项目名称: {{ project_name }}
- 项目类型: {{ project_type }}
- 目标受众: {{ target_audience }}

请基于以下项目特征规划文档章节结构：
{% for feature, value in project_features.items() %}
- {{ feature }}: {{ value }}
{% endfor %}

规划要求：
1. 章节结构要逻辑清晰，循序渐进
2. 考虑目标受众的知识背景
3. 确保内容完整性和实用性
4. 合理安排章节依赖关系

请提供JSON格式的章节规划方案。"""
            },
            
            "content_generation": {
                "name": "内容生成模板",
                "type": "generation",
                "content": """你是一个专业的技术文档撰写专家。

任务信息：
- 章节标题: {{ section_title }}
- 章节类型: {{ section_type }}
- 目标长度: {{ target_length }}字
- 内容风格: {{ content_style }}

项目上下文：
- 项目名称: {{ project_name }}
- 项目描述: {{ project_description }}

{% if related_sections %}
相关章节内容：
{% for section_id, content in related_sections.items() %}
## {{ section_id }}
{{ content[:200] }}...
{% endfor %}
{% endif %}

生成要求：
1. 内容准确，基于实际项目信息
2. 语言清晰，适合目标受众
3. 结构合理，便于阅读理解
4. 包含必要的代码示例和说明

请生成该章节的完整内容。"""
            },
            
            "quality_review": {
                "name": "质量评审模板",
                "type": "review",
                "content": """你是一个专业的技术文档质量评审专家。

请评审以下文档内容：

## 章节标题
{{ section_title }}

## 内容
{{ generated_content }}

评审维度：
1. 内容准确性 - 信息是否准确可靠
2. 结构清晰性 - 逻辑结构是否清晰
3. 语言流畅性 - 表达是否自然流畅
4. 完整性 - 内容是否完整充分
5. 实用性 - 对读者是否有实际帮助

请提供详细的评审报告，包括评分（0-1）和改进建议。"""
            }
        }
        
        for template_id, template_info in default_templates.items():
            template = AITemplate(
                template_id=template_id,
                name=template_info["name"],
                content=template_info["content"],
                template_type=template_info["type"]
            )
            self.templates[template_id] = template
    
    def get_template(self, template_id: str) -> Optional[AITemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def add_template(self, template: AITemplate) -> bool:
        """添加模板"""
        if template.template_id in self.templates:
            return False
        
        self.templates[template.template_id] = template
        self._save_template_to_file(template)
        return True
    
    def update_template(self, template_id: str, content: str, version: str = None, description: str = "") -> bool:
        """更新模板"""
        template = self.templates.get(template_id)
        if not template:
            return False
        
        if version is None:
            # 自动生成版本号
            current_version_parts = template.current_version.split('.')
            minor_version = int(current_version_parts[-1]) + 1
            version = f"{'.'.join(current_version_parts[:-1])}.{minor_version}"
        
        template.add_version(version, content, description)
        self._save_template_to_file(template)
        return True
    
    def remove_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id in self.templates:
            del self.templates[template_id]
            self._remove_template_file(template_id)
            return True
        return False
    
    def render_template(self, template_id: str, **kwargs) -> str:
        """渲染模板"""
        template = self.get_template(template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        return template.render(**kwargs)
    
    def list_templates(self, template_type: str = None) -> List[Dict[str, Any]]:
        """列出模板"""
        templates = []
        for template in self.templates.values():
            if template_type is None or template.template_type == template_type:
                templates.append(template.to_dict())
        
        return sorted(templates, key=lambda x: x['name'])
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        total_templates = len(self.templates)
        type_counts = {}
        total_usage = 0
        
        for template in self.templates.values():
            template_type = template.template_type
            type_counts[template_type] = type_counts.get(template_type, 0) + 1
            total_usage += template.usage_count
        
        return {
            "总模板数": total_templates,
            "类型分布": type_counts,
            "总使用次数": total_usage,
            "平均使用次数": total_usage / total_templates if total_templates > 0 else 0
        }
    
    def _save_template_to_file(self, template: AITemplate):
        """保存模板到文件"""
        template_file = self.templates_dir / f"{template.template_id}.json"
        template_data = {
            "template_id": template.template_id,
            "name": template.name,
            "content": template.content,
            "template_type": template.template_type,
            "parameters": template.parameters,
            "current_version": template.current_version,
            "created_at": template.created_at.isoformat(),
            "updated_at": template.updated_at.isoformat(),
            "usage_count": template.usage_count,
            "versions": template.get_version_history()
        }
        
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存模板文件失败: {e}")
    
    def _remove_template_file(self, template_id: str):
        """删除模板文件"""
        template_file = self.templates_dir / f"{template_id}.json"
        try:
            if template_file.exists():
                template_file.unlink()
        except Exception as e:
            print(f"删除模板文件失败: {e}")
    
    def load_templates_from_directory(self):
        """从目录加载模板"""
        for template_file in self.templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                
                template = AITemplate(
                    template_id=template_data["template_id"],
                    name=template_data["name"],
                    content=template_data["content"],
                    template_type=template_data.get("template_type", "generation"),
                    parameters=template_data.get("parameters", {})
                )
                
                template.current_version = template_data.get("current_version", "1.0.0")
                template.usage_count = template_data.get("usage_count", 0)
                
                if "created_at" in template_data:
                    template.created_at = datetime.fromisoformat(template_data["created_at"])
                if "updated_at" in template_data:
                    template.updated_at = datetime.fromisoformat(template_data["updated_at"])
                
                self.templates[template.template_id] = template
                
            except Exception as e:
                print(f"加载模板文件 {template_file} 失败: {e}")
    
    def backup_templates(self, backup_path: str):
        """备份模板"""
        backup_dir = Path(backup_path)
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        backup_data = {
            "backup_time": datetime.now(timezone.utc).isoformat(),
            "templates": {}
        }
        
        for template_id, template in self.templates.items():
            backup_data["templates"][template_id] = {
                "template_id": template.template_id,
                "name": template.name,
                "content": template.content,
                "template_type": template.template_type,
                "parameters": template.parameters,
                "current_version": template.current_version,
                "created_at": template.created_at.isoformat(),
                "updated_at": template.updated_at.isoformat(),
                "usage_count": template.usage_count,
                "versions": template.get_version_history()
            }
        
        backup_file = backup_dir / f"templates_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
        
        return str(backup_file)
