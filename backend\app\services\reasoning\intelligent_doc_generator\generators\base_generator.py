"""
基础内容生成器

提供所有内容生成器的基础抽象类和通用功能
"""

import uuid
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple

from ...ai_agent_core import BaseAgent, AgentInput, AgentOutput, AgentManager
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy

logger = logging.getLogger(__name__)


class BaseGenerator(BaseAgent, ABC):
    """基础内容生成器抽象类"""
    
    def __init__(self):
        """初始化基础生成器"""
        super().__init__()
        self.generation_strategy = GenerationStrategy.DEFAULT
        self.supported_content_types: List[ContentType] = []
        self.quality_threshold = 0.7
        
    async def _initialize(self) -> None:
        """初始化生成器特定资源"""
        logger.info(f"初始化{self.name}生成器特定资源")
        await self._setup_generator()
    
    @abstractmethod
    async def _setup_generator(self) -> None:
        """设置生成器特定配置"""
        pass
    
    async def _process(self, input_data: AgentInput) -> Tuple[Any, Dict[str, Any]]:
        """处理生成请求"""
        try:
            # 解析生成请求
            request = self._parse_generation_request(input_data.parameters)
            
            # 验证请求
            validation_result = await self._validate_request(request)
            if not validation_result["is_valid"]:
                return None, {
                    "error": f"请求验证失败: {validation_result['errors']}",
                    "validation_result": validation_result
                }
            
            # 执行内容生成
            result = await self._generate_content(request)
            
            # 质量评估
            quality_score = await self._evaluate_quality(result)
            result.quality_score = quality_score
            
            # 如果质量不达标，尝试优化
            if quality_score < self.quality_threshold:
                logger.warning(f"内容质量不达标 ({quality_score:.2f}), 尝试优化")
                result = await self._optimize_content(result, request)
                result.quality_score = await self._evaluate_quality(result)
            
            return result, {
                "generation_id": result.generation_id,
                "content_type": result.content_type.value,
                "strategy": result.strategy.value,
                "quality_score": result.quality_score,
                "word_count": len(result.content.split()),
                "generation_time": result.generation_time
            }
            
        except Exception as e:
            logger.error(f"内容生成失败: {str(e)}")
            return None, {"error": str(e)}
    
    def _parse_generation_request(self, parameters: Dict[str, Any]) -> GenerationRequest:
        """解析生成请求参数"""
        return GenerationRequest(
            request_id=parameters.get("request_id", str(uuid.uuid4())),
            content_type=ContentType(parameters.get("content_type", "text")),
            strategy=GenerationStrategy(parameters.get("strategy", "default")),
            template_name=parameters.get("template_name"),
            context_data=parameters.get("context_data", {}),
            generation_params=parameters.get("generation_params", {}),
            quality_requirements=parameters.get("quality_requirements", {}),
            constraints=parameters.get("constraints", {})
        )
    
    async def _validate_request(self, request: GenerationRequest) -> Dict[str, Any]:
        """验证生成请求"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查内容类型支持
        if request.content_type not in self.supported_content_types:
            validation_result["errors"].append(
                f"不支持的内容类型: {request.content_type.value}"
            )
            validation_result["is_valid"] = False
        
        # 检查必需的上下文数据
        required_context = await self._get_required_context_keys()
        missing_keys = [key for key in required_context if key not in request.context_data]
        if missing_keys:
            validation_result["errors"].append(f"缺少必需的上下文数据: {missing_keys}")
            validation_result["is_valid"] = False
        
        return validation_result
    
    @abstractmethod
    async def _generate_content(self, request: GenerationRequest) -> GenerationResult:
        """生成内容的具体实现"""
        pass
    
    @abstractmethod
    async def _get_required_context_keys(self) -> List[str]:
        """获取必需的上下文数据键"""
        pass
    
    async def _evaluate_quality(self, result: GenerationResult) -> float:
        """评估内容质量"""
        # 基础质量评估逻辑
        quality_factors = []
        
        # 长度检查
        word_count = len(result.content.split())
        if word_count > 50:  # 基本长度要求
            quality_factors.append(0.8)
        else:
            quality_factors.append(0.4)
        
        # 结构检查
        if self._has_good_structure(result.content):
            quality_factors.append(0.9)
        else:
            quality_factors.append(0.6)
        
        # 内容完整性检查
        if self._is_content_complete(result):
            quality_factors.append(0.8)
        else:
            quality_factors.append(0.5)
        
        return sum(quality_factors) / len(quality_factors) if quality_factors else 0.5
    
    def _has_good_structure(self, content: str) -> bool:
        """检查内容结构"""
        # 简单的结构检查
        lines = content.strip().split('\n')
        return len(lines) > 3 and any(line.strip().startswith('#') for line in lines)
    
    def _is_content_complete(self, result: GenerationResult) -> bool:
        """检查内容完整性"""
        # 基础完整性检查
        return len(result.content.strip()) > 100
    
    async def _optimize_content(self, result: GenerationResult, request: GenerationRequest) -> GenerationResult:
        """优化内容质量"""
        # 基础优化逻辑 - 子类可以重写
        optimized_content = await self._apply_basic_optimizations(result.content)
        
        result.content = optimized_content
        result.optimization_applied = True
        result.updated_at = datetime.now(timezone.utc)
        
        return result
    
    async def _apply_basic_optimizations(self, content: str) -> str:
        """应用基础优化"""
        # 移除多余空行
        lines = content.split('\n')
        optimized_lines = []
        prev_empty = False
        
        for line in lines:
            if line.strip():
                optimized_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                optimized_lines.append(line)
                prev_empty = True
        
        return '\n'.join(optimized_lines)
