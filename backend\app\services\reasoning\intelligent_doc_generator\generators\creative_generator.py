"""
创意内容生成器

专门用于生成富有创意、吸引人的文档内容，如：
- 项目宣传
- 特色介绍
- 创新亮点
- 营销文案
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

from ...ai_agent_core import AgentManager
from .base_generator import BaseGenerator
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy

logger = logging.getLogger(__name__)


@AgentManager.register("CreativeGeneratorAgent")
class CreativeGenerator(BaseGenerator):
    """创意内容生成器"""
    
    def __init__(self):
        """初始化创意内容生成器"""
        super().__init__()
        self.name = "CreativeGenerator"
        self.generation_strategy = GenerationStrategy.CREATIVE
        self.supported_content_types = [
            ContentType.MARKETING,
            ContentType.HIGHLIGHTS,
            ContentType.INNOVATION,
            ContentType.SHOWCASE
        ]
    
    async def _setup_generator(self) -> None:
        """设置创意内容生成器配置"""
        logger.info("设置创意内容生成器配置")
        # 创意生成器特定配置
        self.creative_styles = {
            "engaging": "引人入胜，充满活力",
            "professional": "专业创新，突出价值",
            "storytelling": "故事化叙述，情感共鸣",
            "innovative": "创新导向，前瞻性思维"
        }
        
        self.tone_variations = {
            "enthusiastic": "热情洋溢",
            "confident": "自信专业",
            "inspiring": "鼓舞人心",
            "approachable": "亲切友好"
        }
    
    async def _create_chain(self) -> None:
        """创建创意内容生成处理链"""
        try:
            # 创意内容生成提示模板
            creative_template = """
            # 创意内容生成专家

            你是一个富有创意的技术营销专家和内容创作者，擅长将技术特点转化为引人入胜的内容。

            ## 创作任务
            内容类型: {{ content_type }}
            创意风格: {{ creative_style }}
            语调风格: {{ tone_style }}
            目标受众: {{ target_audience }}

            ## 项目信息
            项目名称: {{ project_name }}
            项目类型: {{ project_type }}
            核心价值: {{ core_value }}
            独特卖点: {{ unique_selling_points }}

            ## 创意素材
            {% for key, value in creative_materials.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            ## 创意要求
            1. **吸引力**: 开头要抓住读者注意力
            2. **独特性**: 突出项目的独特价值和创新点
            3. **情感连接**: 与目标受众建立情感共鸣
            4. **可信度**: 在创意的同时保持专业可信
            5. **行动导向**: 激发读者的兴趣和行动意愿

            ## 风格指导
            {{ style_guidance }}

            ## 创意技巧
            - 使用生动的比喻和类比
            - 讲述项目背后的故事
            - 突出解决的实际问题
            - 展示未来的可能性
            - 使用有力的动词和形容词

            ## 生成参数
            {% for key, value in generation_params.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            请创作富有创意且专业的内容，让读者对项目产生浓厚兴趣：
            """
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            prompt = PromptTemplate.from_template(creative_template)
            self.chain = prompt | self.llm | StrOutputParser()
            
            logger.info("创意内容生成处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建创意内容生成处理链失败: {str(e)}")
            raise
    
    async def _generate_content(self, request: GenerationRequest) -> GenerationResult:
        """生成创意内容"""
        start_time = datetime.now(timezone.utc)
        
        try:
            # 确定创意风格和语调
            creative_style = request.generation_params.get("creative_style", "engaging")
            tone_style = request.generation_params.get("tone_style", "confident")
            
            # 提取创意素材
            creative_materials = self._extract_creative_materials(request.context_data)
            
            # 获取风格指导
            style_guidance = self._get_creative_style_guidance(creative_style, tone_style)
            
            # 准备生成参数
            generation_input = {
                "content_type": request.content_type.value,
                "creative_style": creative_style,
                "tone_style": tone_style,
                "target_audience": request.context_data.get("target_audience", "技术爱好者"),
                "project_name": request.context_data.get("project_name", "创新项目"),
                "project_type": request.context_data.get("project_type", "技术项目"),
                "core_value": request.context_data.get("core_value", "提供卓越的技术解决方案"),
                "unique_selling_points": request.context_data.get("unique_selling_points", []),
                "creative_materials": creative_materials,
                "style_guidance": style_guidance,
                "generation_params": request.generation_params
            }
            
            # 调用AI生成内容
            generated_content = await self.chain.ainvoke(generation_input)
            
            # 后处理创意内容
            processed_content = await self._post_process_creative_content(
                generated_content, creative_style
            )
            
            end_time = datetime.now(timezone.utc)
            generation_time = (end_time - start_time).total_seconds()
            
            return GenerationResult(
                generation_id=request.request_id,
                request=request,
                content=processed_content,
                content_type=request.content_type,
                strategy=self.generation_strategy,
                generator_name=self.name,
                generation_time=generation_time,
                created_at=start_time,
                updated_at=end_time
            )
            
        except Exception as e:
            logger.error(f"创意内容生成失败: {str(e)}")
            raise
    
    async def _get_required_context_keys(self) -> List[str]:
        """获取必需的上下文数据键"""
        return ["project_name", "project_type"]
    
    def _extract_creative_materials(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取创意素材"""
        creative_materials = {}
        
        # 提取可用于创意的信息
        creative_keys = [
            "key_features", "benefits", "success_stories", "user_testimonials",
            "innovation_points", "competitive_advantages", "future_vision",
            "problem_solved", "impact_metrics", "awards_recognition"
        ]
        
        for key in creative_keys:
            if key in context_data:
                creative_materials[key] = context_data[key]
        
        # 如果没有足够的创意素材，生成一些基础素材
        if not creative_materials:
            creative_materials = {
                "核心特点": context_data.get("features", "创新的技术解决方案"),
                "解决问题": "提高效率，简化流程",
                "目标愿景": "成为行业领先的解决方案"
            }
        
        return creative_materials
    
    def _get_creative_style_guidance(self, creative_style: str, tone_style: str) -> str:
        """获取创意风格指导"""
        style_desc = self.creative_styles.get(creative_style, "创意表达")
        tone_desc = self.tone_variations.get(tone_style, "专业表达")
        
        guidance_map = {
            "engaging": "使用引人入胜的开头，保持读者兴趣，适当使用问句和感叹句",
            "professional": "保持专业性的同时展现创新思维，使用准确的行业术语",
            "storytelling": "采用故事化叙述，创造情感连接，展现项目的发展历程",
            "innovative": "强调创新性和前瞻性，展示技术的突破性和未来潜力"
        }
        
        specific_guidance = guidance_map.get(creative_style, "平衡创意与专业性")
        
        return f"{style_desc}，{tone_desc}。{specific_guidance}"
    
    async def _post_process_creative_content(self, content: str, creative_style: str) -> str:
        """后处理创意内容"""
        # 增强视觉效果
        content = self._add_visual_elements(content)
        
        # 优化语言表达
        content = self._enhance_language_expression(content, creative_style)
        
        # 添加行动号召
        content = self._add_call_to_action(content)
        
        return content
    
    def _add_visual_elements(self, content: str) -> str:
        """添加视觉元素"""
        # 添加emoji和特殊符号来增强视觉效果
        visual_enhancements = {
            "🚀": ["创新", "突破", "发展", "提升"],
            "💡": ["想法", "创意", "解决方案", "灵感"],
            "⭐": ["特色", "亮点", "优势", "卓越"],
            "🔥": ["热门", "流行", "强大", "高效"],
            "✨": ["特别", "独特", "精彩", "出色"]
        }
        
        for emoji, keywords in visual_enhancements.items():
            for keyword in keywords:
                if keyword in content and emoji not in content:
                    content = content.replace(keyword, f"{emoji} {keyword}", 1)
                    break
        
        return content
    
    def _enhance_language_expression(self, content: str, creative_style: str) -> str:
        """增强语言表达"""
        if creative_style == "storytelling":
            # 为故事化风格添加更多叙述性语言
            content = self._add_narrative_elements(content)
        elif creative_style == "innovative":
            # 为创新风格添加前瞻性语言
            content = self._add_forward_looking_language(content)
        
        return content
    
    def _add_narrative_elements(self, content: str) -> str:
        """添加叙述性元素"""
        # 简化实现，实际可以更复杂
        narrative_starters = [
            "想象一下", "让我们来看看", "这个故事开始于", "在这个项目中"
        ]
        
        lines = content.split('\n')
        if lines and not any(starter in lines[0] for starter in narrative_starters):
            lines[0] = f"想象一下，{lines[0]}"
        
        return '\n'.join(lines)
    
    def _add_forward_looking_language(self, content: str) -> str:
        """添加前瞻性语言"""
        forward_phrases = [
            "未来", "下一代", "革命性", "突破性", "前沿", "领先"
        ]
        
        # 简单的前瞻性语言增强
        return content
    
    def _add_call_to_action(self, content: str) -> str:
        """添加行动号召"""
        cta_phrases = [
            "立即体验这个创新解决方案！",
            "加入我们，共同探索技术的无限可能！",
            "现在就开始您的技术创新之旅！"
        ]
        
        # 如果内容没有明显的行动号召，添加一个
        if not any(phrase in content for phrase in ["立即", "现在", "开始", "体验"]):
            content += f"\n\n{cta_phrases[0]}"
        
        return content
