"""
叙述性内容生成器

专门用于生成流畅、连贯的叙述性文档内容，如：
- 项目介绍
- 使用教程
- 概念说明
- 背景介绍
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

from ...ai_agent_core import AgentManager
from .base_generator import BaseGenerator
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy

logger = logging.getLogger(__name__)


@AgentManager.register("NarrativeGeneratorAgent")
class NarrativeGenerator(BaseGenerator):
    """叙述性内容生成器"""
    
    def __init__(self):
        """初始化叙述性生成器"""
        super().__init__()
        self.name = "NarrativeGenerator"
        self.generation_strategy = GenerationStrategy.NARRATIVE
        self.supported_content_types = [
            ContentType.INTRODUCTION,
            ContentType.OVERVIEW,
            ContentType.TUTORIAL,
            ContentType.BACKGROUND,
            ContentType.USAGE
        ]
    
    async def _setup_generator(self) -> None:
        """设置叙述性生成器配置"""
        logger.info("设置叙述性生成器配置")
        # 叙述性生成器特定配置
        self.narrative_styles = {
            "technical": "技术性叙述，专业准确",
            "friendly": "友好亲切，易于理解",
            "formal": "正式严谨，条理清晰",
            "tutorial": "教学式叙述，循序渐进"
        }
    
    async def _create_chain(self) -> None:
        """创建叙述性生成处理链"""
        try:
            # 叙述性内容生成提示模板
            narrative_template = """
            # 叙述性内容生成专家

            你是一个专业的技术写作专家，擅长创建流畅、连贯、引人入胜的技术文档。

            ## 生成任务
            内容类型: {{ content_type }}
            叙述风格: {{ narrative_style }}
            目标受众: {{ target_audience }}
            语言风格: {{ language_style }}

            ## 项目信息
            项目名称: {{ project_name }}
            项目类型: {{ project_type }}
            主要语言: {{ primary_language }}
            项目描述: {{ project_description }}

            ## 上下文信息
            {% for key, value in context_data.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            ## 叙述要求
            1. **流畅性**: 语言自然流畅，逻辑连贯
            2. **可读性**: 易于理解，避免过于复杂的术语
            3. **吸引力**: 开头引人入胜，保持读者兴趣
            4. **完整性**: 内容完整，覆盖关键信息
            5. **准确性**: 信息准确，避免误导

            ## 写作风格指导
            {{ style_guidance }}

            ## 生成参数
            {% for key, value in generation_params.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            请根据以上要求生成高质量的叙述性内容，确保内容既专业又易读：
            """
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            prompt = PromptTemplate.from_template(narrative_template)
            self.chain = prompt | self.llm | StrOutputParser()
            
            logger.info("叙述性生成处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建叙述性生成处理链失败: {str(e)}")
            raise
    
    async def _generate_content(self, request: GenerationRequest) -> GenerationResult:
        """生成叙述性内容"""
        start_time = datetime.now(timezone.utc)
        
        try:
            # 确定叙述风格
            narrative_style = request.generation_params.get("narrative_style", "technical")
            language_style = request.generation_params.get("language_style", "professional")
            
            # 获取风格指导
            style_guidance = self._get_style_guidance(narrative_style, request.content_type)
            
            # 准备生成参数
            generation_input = {
                "content_type": request.content_type.value,
                "narrative_style": narrative_style,
                "target_audience": request.context_data.get("target_audience", "开发者"),
                "language_style": language_style,
                "project_name": request.context_data.get("project_name", "未知项目"),
                "project_type": request.context_data.get("project_type", "软件项目"),
                "primary_language": request.context_data.get("primary_language", "Python"),
                "project_description": request.context_data.get("project_description", ""),
                "context_data": request.context_data,
                "style_guidance": style_guidance,
                "generation_params": request.generation_params
            }
            
            # 调用AI生成内容
            generated_content = await self.chain.ainvoke(generation_input)
            
            # 后处理叙述性内容
            processed_content = await self._post_process_narrative_content(
                generated_content, narrative_style
            )
            
            end_time = datetime.now(timezone.utc)
            generation_time = (end_time - start_time).total_seconds()
            
            return GenerationResult(
                generation_id=request.request_id,
                request=request,
                content=processed_content,
                content_type=request.content_type,
                strategy=self.generation_strategy,
                generator_name=self.name,
                generation_time=generation_time,
                created_at=start_time,
                updated_at=end_time
            )
            
        except Exception as e:
            logger.error(f"叙述性内容生成失败: {str(e)}")
            raise
    
    async def _get_required_context_keys(self) -> List[str]:
        """获取必需的上下文数据键"""
        return ["project_name", "project_type"]
    
    def _get_style_guidance(self, narrative_style: str, content_type: ContentType) -> str:
        """获取风格指导"""
        base_guidance = self.narrative_styles.get(narrative_style, "专业技术叙述")
        
        # 根据内容类型调整风格指导
        type_specific_guidance = {
            ContentType.INTRODUCTION: "开头要引人入胜，简洁明了地介绍项目价值",
            ContentType.OVERVIEW: "全面概述项目特点，突出核心优势",
            ContentType.TUTORIAL: "循序渐进，步骤清晰，配合示例说明",
            ContentType.BACKGROUND: "提供充分的背景信息，解释项目产生的原因",
            ContentType.USAGE: "实用性强，重点说明如何使用，配合实际场景"
        }
        
        specific_guidance = type_specific_guidance.get(content_type, "")
        
        return f"{base_guidance}。{specific_guidance}"
    
    async def _post_process_narrative_content(self, content: str, narrative_style: str) -> str:
        """后处理叙述性内容"""
        # 改善段落结构
        paragraphs = content.split('\n\n')
        processed_paragraphs = []
        
        for paragraph in paragraphs:
            if paragraph.strip():
                # 确保段落有适当的长度
                processed_paragraph = self._optimize_paragraph_structure(paragraph.strip())
                processed_paragraphs.append(processed_paragraph)
        
        # 添加过渡句和连接词
        enhanced_content = self._add_narrative_flow(processed_paragraphs, narrative_style)
        
        return enhanced_content
    
    def _optimize_paragraph_structure(self, paragraph: str) -> str:
        """优化段落结构"""
        sentences = paragraph.split('.')
        if len(sentences) > 5:  # 段落过长，尝试分割
            mid_point = len(sentences) // 2
            first_part = '.'.join(sentences[:mid_point]).strip()
            second_part = '.'.join(sentences[mid_point:]).strip()
            
            if first_part and second_part:
                return f"{first_part}.\n\n{second_part}"
        
        return paragraph
    
    def _add_narrative_flow(self, paragraphs: List[str], narrative_style: str) -> str:
        """添加叙述流畅性"""
        if len(paragraphs) <= 1:
            return '\n\n'.join(paragraphs)
        
        # 根据风格选择连接方式
        if narrative_style == "tutorial":
            # 教程风格，添加步骤连接
            return self._add_tutorial_flow(paragraphs)
        elif narrative_style == "friendly":
            # 友好风格，添加自然过渡
            return self._add_friendly_flow(paragraphs)
        else:
            # 默认技术风格
            return '\n\n'.join(paragraphs)
    
    def _add_tutorial_flow(self, paragraphs: List[str]) -> str:
        """添加教程式流畅性"""
        # 为教程添加步骤标识和过渡
        enhanced_paragraphs = []
        for i, paragraph in enumerate(paragraphs):
            if i == 0:
                enhanced_paragraphs.append(paragraph)
            else:
                # 添加适当的过渡词
                transition_words = ["接下来", "然后", "此外", "另外", "最后"]
                if i < len(transition_words):
                    enhanced_paragraphs.append(f"{transition_words[i-1]}，{paragraph}")
                else:
                    enhanced_paragraphs.append(paragraph)
        
        return '\n\n'.join(enhanced_paragraphs)
    
    def _add_friendly_flow(self, paragraphs: List[str]) -> str:
        """添加友好式流畅性"""
        # 为友好风格添加自然过渡
        return '\n\n'.join(paragraphs)  # 简化实现
