"""
结构化内容生成器

专门用于生成具有明确结构的文档内容，如：
- API文档
- 配置说明
- 安装指南
- 功能列表
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

from ...ai_agent_core import AgentManager
from .base_generator import BaseGenerator
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy

logger = logging.getLogger(__name__)


@AgentManager.register("StructuredGeneratorAgent")
class StructuredGenerator(BaseGenerator):
    """结构化内容生成器"""
    
    def __init__(self):
        """初始化结构化生成器"""
        super().__init__()
        self.name = "StructuredGenerator"
        self.generation_strategy = GenerationStrategy.STRUCTURED
        self.supported_content_types = [
            ContentType.API_REFERENCE,
            ContentType.INSTALLATION,
            ContentType.CONFIGURATION,
            ContentType.FEATURES
        ]
    
    async def _setup_generator(self) -> None:
        """设置结构化生成器配置"""
        logger.info("设置结构化生成器配置")
        # 结构化生成器特定配置
        self.structure_templates = {
            ContentType.API_REFERENCE: self._get_api_structure_template(),
            ContentType.INSTALLATION: self._get_installation_structure_template(),
            ContentType.CONFIGURATION: self._get_config_structure_template(),
            ContentType.FEATURES: self._get_features_structure_template()
        }
    
    async def _create_chain(self) -> None:
        """创建结构化生成处理链"""
        try:
            # 结构化内容生成提示模板
            structured_template = """
            # 结构化内容生成专家

            你是一个专业的技术文档撰写专家，擅长创建结构清晰、逻辑严密的技术文档。

            ## 生成任务
            内容类型: {{ content_type }}
            生成策略: {{ strategy }}
            目标受众: {{ target_audience }}

            ## 项目信息
            项目名称: {{ project_name }}
            项目类型: {{ project_type }}
            主要语言: {{ primary_language }}

            ## 上下文数据
            {% for key, value in context_data.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            ## 结构要求
            {{ structure_template }}

            ## 生成参数
            {% for key, value in generation_params.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            ## 质量要求
            1. 结构清晰，层次分明
            2. 信息准确，逻辑严密
            3. 格式规范，易于阅读
            4. 内容完整，覆盖全面

            请根据以上要求生成高质量的结构化内容：
            """
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            prompt = PromptTemplate.from_template(structured_template)
            self.chain = prompt | self.llm | StrOutputParser()
            
            logger.info("结构化生成处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建结构化生成处理链失败: {str(e)}")
            raise
    
    async def _generate_content(self, request: GenerationRequest) -> GenerationResult:
        """生成结构化内容"""
        start_time = datetime.now(timezone.utc)
        
        try:
            # 获取结构模板
            structure_template = self.structure_templates.get(
                request.content_type, 
                "请按照标准结构组织内容"
            )
            
            # 准备生成参数
            generation_input = {
                "content_type": request.content_type.value,
                "strategy": request.strategy.value,
                "target_audience": request.context_data.get("target_audience", "开发者"),
                "project_name": request.context_data.get("project_name", "未知项目"),
                "project_type": request.context_data.get("project_type", "软件项目"),
                "primary_language": request.context_data.get("primary_language", "Python"),
                "context_data": request.context_data,
                "structure_template": structure_template,
                "generation_params": request.generation_params
            }
            
            # 调用AI生成内容
            generated_content = await self.chain.ainvoke(generation_input)
            
            # 后处理结构化内容
            processed_content = await self._post_process_structured_content(
                generated_content, request.content_type
            )
            
            end_time = datetime.now(timezone.utc)
            generation_time = (end_time - start_time).total_seconds()
            
            return GenerationResult(
                generation_id=request.request_id,
                request=request,
                content=processed_content,
                content_type=request.content_type,
                strategy=self.generation_strategy,
                generator_name=self.name,
                generation_time=generation_time,
                created_at=start_time,
                updated_at=end_time
            )
            
        except Exception as e:
            logger.error(f"结构化内容生成失败: {str(e)}")
            raise
    
    async def _get_required_context_keys(self) -> List[str]:
        """获取必需的上下文数据键"""
        return ["project_name", "project_type"]
    
    def _get_api_structure_template(self) -> str:
        """获取API文档结构模板"""
        return """
        请按照以下结构组织API文档：
        1. API概述
        2. 认证方式
        3. 基础URL和版本
        4. 请求格式
        5. 响应格式
        6. 错误处理
        7. API端点列表（按功能分组）
        8. 示例代码
        9. SDK和工具
        """
    
    def _get_installation_structure_template(self) -> str:
        """获取安装指南结构模板"""
        return """
        请按照以下结构组织安装指南：
        1. 系统要求
        2. 依赖项检查
        3. 安装方式（多种方式）
        4. 配置步骤
        5. 验证安装
        6. 常见问题
        7. 故障排除
        """
    
    def _get_config_structure_template(self) -> str:
        """获取配置说明结构模板"""
        return """
        请按照以下结构组织配置说明：
        1. 配置文件位置
        2. 配置文件格式
        3. 核心配置项
        4. 高级配置选项
        5. 环境变量
        6. 配置示例
        7. 配置验证
        """
    
    def _get_features_structure_template(self) -> str:
        """获取功能特性结构模板"""
        return """
        请按照以下结构组织功能特性：
        1. 核心功能概述
        2. 主要特性列表
        3. 技术亮点
        4. 性能特点
        5. 兼容性说明
        6. 扩展能力
        7. 未来规划
        """
    
    async def _post_process_structured_content(self, content: str, content_type: ContentType) -> str:
        """后处理结构化内容"""
        # 确保标题格式正确
        lines = content.split('\n')
        processed_lines = []
        
        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('#') and self._looks_like_title(stripped):
                # 为看起来像标题的行添加Markdown格式
                if self._is_main_title(stripped):
                    processed_lines.append(f"## {stripped}")
                else:
                    processed_lines.append(f"### {stripped}")
            else:
                processed_lines.append(line)
        
        return '\n'.join(processed_lines)
    
    def _looks_like_title(self, text: str) -> bool:
        """判断文本是否看起来像标题"""
        # 简单的标题识别逻辑
        return (
            len(text) < 50 and
            not text.endswith('.') and
            not text.startswith('-') and
            not text.startswith('*') and
            text[0].isupper()
        )
    
    def _is_main_title(self, text: str) -> bool:
        """判断是否为主标题"""
        main_title_keywords = ['概述', '介绍', '说明', '指南', '文档', '参考']
        return any(keyword in text for keyword in main_title_keywords)
