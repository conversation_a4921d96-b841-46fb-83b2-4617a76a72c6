"""
技术文档生成器

专门用于生成技术性强、准确性高的文档内容，如：
- 架构说明
- 技术规范
- 开发指南
- 部署文档
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timezone

from ...ai_agent_core import AgentManager
from .base_generator import BaseGenerator
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy

logger = logging.getLogger(__name__)


@AgentManager.register("TechnicalGeneratorAgent")
class TechnicalGenerator(BaseGenerator):
    """技术文档生成器"""
    
    def __init__(self):
        """初始化技术文档生成器"""
        super().__init__()
        self.name = "TechnicalGenerator"
        self.generation_strategy = GenerationStrategy.TECHNICAL
        self.supported_content_types = [
            ContentType.ARCHITECTURE,
            ContentType.DEVELOPMENT,
            ContentType.DEPLOYMENT,
            ContentType.TESTING,
            ContentType.TROUBLESHOOTING
        ]
        # 技术文档要求更高的质量标准
        self.quality_threshold = 0.8
    
    async def _setup_generator(self) -> None:
        """设置技术文档生成器配置"""
        logger.info("设置技术文档生成器配置")
        # 技术文档生成器特定配置
        self.technical_frameworks = {
            "architecture": "系统架构分析框架",
            "development": "开发流程标准框架",
            "deployment": "部署最佳实践框架",
            "testing": "测试策略框架",
            "troubleshooting": "问题诊断框架"
        }
    
    async def _create_chain(self) -> None:
        """创建技术文档生成处理链"""
        try:
            # 技术文档生成提示模板
            technical_template = """
            # 技术文档生成专家

            你是一个资深的技术架构师和文档专家，擅长创建准确、详细、实用的技术文档。

            ## 生成任务
            内容类型: {{ content_type }}
            技术框架: {{ technical_framework }}
            技术栈: {{ tech_stack }}
            复杂度级别: {{ complexity_level }}

            ## 项目技术信息
            项目名称: {{ project_name }}
            项目类型: {{ project_type }}
            主要语言: {{ primary_language }}
            架构模式: {{ architecture_pattern }}
            技术栈: {{ tech_stack }}

            ## 技术上下文
            {% for key, value in technical_context.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            ## 技术要求
            1. **准确性**: 技术信息必须准确无误
            2. **完整性**: 覆盖所有关键技术点
            3. **实用性**: 提供可操作的技术指导
            4. **专业性**: 使用准确的技术术语
            5. **可维护性**: 考虑长期维护和更新

            ## 技术框架指导
            {{ framework_guidance }}

            ## 生成参数
            {% for key, value in generation_params.items() %}
            {{ key }}: {{ value }}
            {% endfor %}

            ## 质量标准
            - 技术细节准确，避免模糊表述
            - 提供具体的配置和代码示例
            - 包含错误处理和边界情况
            - 考虑性能和安全因素
            - 提供版本兼容性信息

            请根据以上要求生成高质量的技术文档：
            """
            
            from langchain_core.prompts import PromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            
            prompt = PromptTemplate.from_template(technical_template)
            self.chain = prompt | self.llm | StrOutputParser()
            
            logger.info("技术文档生成处理链创建成功")
            
        except Exception as e:
            logger.error(f"创建技术文档生成处理链失败: {str(e)}")
            raise
    
    async def _generate_content(self, request: GenerationRequest) -> GenerationResult:
        """生成技术文档内容"""
        start_time = datetime.now(timezone.utc)
        
        try:
            # 提取技术相关参数
            tech_stack = request.context_data.get("tech_stack", [])
            architecture_pattern = request.context_data.get("architecture_pattern", "未知")
            complexity_level = request.generation_params.get("complexity_level", "medium")
            
            # 获取技术框架指导
            framework_guidance = self._get_technical_framework_guidance(
                request.content_type, complexity_level
            )
            
            # 准备技术上下文
            technical_context = self._prepare_technical_context(request.context_data)
            
            # 准备生成参数
            generation_input = {
                "content_type": request.content_type.value,
                "technical_framework": self.technical_frameworks.get(
                    request.content_type.value, "通用技术框架"
                ),
                "tech_stack": ", ".join(tech_stack) if isinstance(tech_stack, list) else str(tech_stack),
                "complexity_level": complexity_level,
                "project_name": request.context_data.get("project_name", "未知项目"),
                "project_type": request.context_data.get("project_type", "软件项目"),
                "primary_language": request.context_data.get("primary_language", "Python"),
                "architecture_pattern": architecture_pattern,
                "technical_context": technical_context,
                "framework_guidance": framework_guidance,
                "generation_params": request.generation_params
            }
            
            # 调用AI生成内容
            generated_content = await self.chain.ainvoke(generation_input)
            
            # 后处理技术文档内容
            processed_content = await self._post_process_technical_content(
                generated_content, request.content_type
            )
            
            end_time = datetime.now(timezone.utc)
            generation_time = (end_time - start_time).total_seconds()
            
            return GenerationResult(
                generation_id=request.request_id,
                request=request,
                content=processed_content,
                content_type=request.content_type,
                strategy=self.generation_strategy,
                generator_name=self.name,
                generation_time=generation_time,
                created_at=start_time,
                updated_at=end_time
            )
            
        except Exception as e:
            logger.error(f"技术文档内容生成失败: {str(e)}")
            raise
    
    async def _get_required_context_keys(self) -> List[str]:
        """获取必需的上下文数据键"""
        return ["project_name", "project_type", "primary_language"]
    
    def _prepare_technical_context(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备技术上下文"""
        technical_context = {}
        
        # 提取技术相关的上下文信息
        technical_keys = [
            "dependencies", "frameworks", "databases", "apis", 
            "deployment_targets", "testing_frameworks", "build_tools",
            "performance_requirements", "security_requirements"
        ]
        
        for key in technical_keys:
            if key in context_data:
                technical_context[key] = context_data[key]
        
        return technical_context
    
    def _get_technical_framework_guidance(self, content_type: ContentType, complexity_level: str) -> str:
        """获取技术框架指导"""
        base_guidance = {
            ContentType.ARCHITECTURE: "系统架构设计原则和最佳实践",
            ContentType.DEVELOPMENT: "开发流程规范和代码质量标准",
            ContentType.DEPLOYMENT: "部署策略和运维最佳实践",
            ContentType.TESTING: "测试策略和质量保证流程",
            ContentType.TROUBLESHOOTING: "问题诊断方法和解决方案"
        }
        
        complexity_guidance = {
            "low": "重点关注基础概念和简单实现",
            "medium": "平衡理论和实践，提供详细说明",
            "high": "深入技术细节，考虑复杂场景和边界情况"
        }
        
        base = base_guidance.get(content_type, "通用技术指导")
        complexity = complexity_guidance.get(complexity_level, "")
        
        return f"{base}。{complexity}"
    
    async def _post_process_technical_content(self, content: str, content_type: ContentType) -> str:
        """后处理技术文档内容"""
        # 确保代码块格式正确
        content = self._format_code_blocks(content)
        
        # 添加技术警告和注意事项
        content = self._add_technical_warnings(content, content_type)
        
        # 验证技术术语一致性
        content = self._ensure_terminology_consistency(content)
        
        return content
    
    def _format_code_blocks(self, content: str) -> str:
        """格式化代码块"""
        lines = content.split('\n')
        formatted_lines = []
        in_code_block = False
        
        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                formatted_lines.append(line)
            elif in_code_block:
                # 确保代码块内的缩进正确
                formatted_lines.append(line)
            else:
                # 检查是否为单行代码
                if '`' in line and line.count('`') >= 2:
                    formatted_lines.append(line)
                else:
                    formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def _add_technical_warnings(self, content: str, content_type: ContentType) -> str:
        """添加技术警告和注意事项"""
        warnings = {
            ContentType.DEPLOYMENT: "\n> ⚠️ **注意**: 部署前请确保在测试环境中验证所有配置。",
            ContentType.ARCHITECTURE: "\n> 💡 **提示**: 架构设计应考虑未来的扩展性和维护性。",
            ContentType.TESTING: "\n> 🔍 **重要**: 测试覆盖率应达到80%以上以确保代码质量。"
        }
        
        warning = warnings.get(content_type, "")
        if warning:
            content += warning
        
        return content
    
    def _ensure_terminology_consistency(self, content: str) -> str:
        """确保技术术语一致性"""
        # 简单的术语标准化
        terminology_map = {
            "API接口": "API",
            "数据库DB": "数据库",
            "配置文件config": "配置文件"
        }
        
        for old_term, new_term in terminology_map.items():
            content = content.replace(old_term, new_term)
        
        return content
