"""
智能文档生成器主类

提供统一的接口来访问所有文档生成功能，包括规划、生成、优化和输出。
这是整个智能文档生成系统的主要入口点。
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union

from .core.doc_planner import DocumentPlanner
from .core.content_generator import ContentGenerator
from .core.content_optimizer import ContentOptimizer
from .workflows.pipeline_manager import PipelineManager
from .agents.coordinator_agent import CoordinatorAgent
from .models.planning_models import DocumentPlan, PlanningContext, PlanningStrategy
from .models.generation_models import GenerationRequest, GenerationResult
from .config.generator_config import GeneratorConfig
from .config.template_config import TemplateConfig
from .config.workflow_config import WorkflowConfig
from .utils.text_processor import TextProcessor
from .utils.format_converter import FormatConverter, OutputFormat, ConversionOptions
from .utils.content_analyzer import ContentAnalyzer
from .utils.validation_utils import ValidationUtils, ValidationLevel

logger = logging.getLogger(__name__)


class IntelligentDocGenerator:
    """智能文档生成器主类
    
    提供统一的接口来访问所有文档生成功能，包括规划、生成、优化和输出。
    这是整个智能文档生成系统的主要入口点。
    
    主要功能：
    - 智能文档规划：基于项目特征自动规划文档结构
    - 多策略内容生成：支持结构化、叙述性、技术性、创意性等多种生成策略
    - 智能内容优化：自动优化内容质量、风格和一致性
    - 多格式输出：支持Markdown、HTML、PDF等多种输出格式
    - 工作流管理：提供完整的流水线管理和监控
    
    使用示例：
        generator = IntelligentDocGenerator()
        result = await generator.generate_document(
            project_path="/path/to/project",
            project_name="My Project",
            config={
                "strategy": "adaptive",
                "quality_level": "high",
                "output_format": "markdown"
            }
        )
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化智能文档生成器
        
        Args:
            config: 配置参数字典，可选
        """
        self.generator_id = str(uuid.uuid4())
        self.config = config or {}
        self.initialized = False
        
        # 初始化配置管理器
        self.generator_config = GeneratorConfig()
        self.template_config = TemplateConfig()
        self.workflow_config = WorkflowConfig()
        
        # 初始化核心组件
        self.planner = DocumentPlanner()
        self.content_generator = ContentGenerator()
        self.content_optimizer = ContentOptimizer()
        
        # 初始化流水线管理器和协调器
        self.pipeline_manager = PipelineManager()
        self.coordinator_agent = CoordinatorAgent()
        
        # 初始化工具
        self.text_processor = TextProcessor()
        self.format_converter = FormatConverter()
        self.content_analyzer = ContentAnalyzer()
        self.validation_utils = ValidationUtils()
        
        # 统计信息
        self.generation_stats = {
            "total_documents": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "total_processing_time": 0.0
        }
        
        logger.info(f"智能文档生成器初始化完成: {self.generator_id}")
    
    async def initialize(self):
        """异步初始化组件"""
        if self.initialized:
            return
        
        try:
            # 初始化智能体
            await self.coordinator_agent.initialize()
            
            self.initialized = True
            logger.info("智能文档生成器异步初始化完成")
            
        except Exception as e:
            logger.error(f"智能文档生成器初始化失败: {str(e)}")
            raise
    
    async def generate_document(
        self,
        project_path: str,
        project_name: str,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """生成完整文档
        
        这是主要的文档生成方法，执行完整的生成流水线。
        
        Args:
            project_path: 项目路径
            project_name: 项目名称
            config: 生成配置
            
        Returns:
            生成结果字典，包含文档内容、元数据和统计信息
            
        Raises:
            ValueError: 输入参数无效
            RuntimeError: 生成过程中发生错误
        """
        start_time = datetime.now(timezone.utc)
        
        try:
            # 确保已初始化
            await self.initialize()
            
            logger.info(f"开始生成文档: {project_name}")
            
            # 验证输入参数
            validation_result = self._validate_generation_input(project_path, project_name, config)
            if not validation_result["is_valid"]:
                raise ValueError(f"输入参数验证失败: {validation_result['errors']}")
            
            # 合并配置
            generation_config = self._merge_configs(config)
            
            # 执行完整流水线
            result = await self.pipeline_manager.execute_pipeline(
                project_path=project_path,
                project_name=project_name,
                pipeline_config=generation_config
            )
            
            # 更新统计信息
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            self._update_stats(True, processing_time)
            
            # 添加生成器信息
            result["generator_info"] = {
                "generator_id": self.generator_id,
                "version": "1.0.0",
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "processing_time": processing_time
            }
            
            logger.info(f"文档生成完成: {project_name}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            self._update_stats(False, processing_time)
            
            logger.error(f"文档生成失败: {str(e)}")
            raise RuntimeError(f"文档生成失败: {str(e)}") from e
    
    async def plan_document(
        self,
        project_path: str,
        project_name: str,
        planning_config: Optional[Dict[str, Any]] = None
    ) -> DocumentPlan:
        """规划文档结构
        
        仅执行文档规划阶段，不进行内容生成。
        
        Args:
            project_path: 项目路径
            project_name: 项目名称
            planning_config: 规划配置
            
        Returns:
            文档规划结果
        """
        try:
            await self.initialize()
            logger.info(f"开始规划文档: {project_name}")
            
            # 创建规划上下文
            context = PlanningContext(
                project_path=project_path,
                project_name=project_name,
                project_type=planning_config.get("project_type", "software") if planning_config else "software",
                target_audience=planning_config.get("target_audience", "developers") if planning_config else "developers",
                documentation_style=planning_config.get("documentation_style", "technical") if planning_config else "technical",
                language=planning_config.get("language", "zh-CN") if planning_config else "zh-CN"
            )
            
            # 执行规划
            strategy = PlanningStrategy(planning_config.get("strategy", "adaptive") if planning_config else "adaptive")
            plan = await self.planner.create_plan(
                document_id=f"{project_name}_plan_{uuid.uuid4()}",
                context=context,
                strategy=strategy
            )
            
            logger.info(f"文档规划完成: {project_name}")
            return plan
            
        except Exception as e:
            logger.error(f"文档规划失败: {str(e)}")
            raise
    
    async def generate_content(
        self,
        document_plan: DocumentPlan,
        generation_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, GenerationResult]:
        """生成内容
        
        基于文档规划生成具体内容。
        
        Args:
            document_plan: 文档规划
            generation_config: 生成配置
            
        Returns:
            生成结果字典
        """
        try:
            await self.initialize()
            logger.info("开始生成内容")
            
            # 执行生成工作流
            from .workflows.generation_workflow import GenerationWorkflow
            
            workflow = GenerationWorkflow()
            results = await workflow.execute_generation_workflow(
                document_plan=document_plan,
                generation_config=generation_config or {}
            )
            
            logger.info("内容生成完成")
            return results
            
        except Exception as e:
            logger.error(f"内容生成失败: {str(e)}")
            raise
    
    async def optimize_content(
        self,
        generation_results: Dict[str, GenerationResult],
        optimization_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, GenerationResult]:
        """优化内容
        
        对生成的内容进行质量优化和风格调整。
        
        Args:
            generation_results: 生成结果
            optimization_config: 优化配置
            
        Returns:
            优化后的结果
        """
        try:
            await self.initialize()
            logger.info("开始优化内容")
            
            # 执行优化工作流
            from .workflows.refinement_workflow import RefinementWorkflow
            
            workflow = RefinementWorkflow()
            results = await workflow.execute_refinement_workflow(
                generation_results=generation_results,
                refinement_config=optimization_config or {}
            )
            
            logger.info("内容优化完成")
            return results
            
        except Exception as e:
            logger.error(f"内容优化失败: {str(e)}")
            raise
    
    def convert_format(
        self,
        content: str,
        source_format: str,
        target_format: str,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """转换格式
        
        将内容从一种格式转换为另一种格式。
        
        Args:
            content: 内容
            source_format: 源格式
            target_format: 目标格式
            options: 转换选项
            
        Returns:
            转换后的内容
        """
        try:
            # 创建转换选项
            conversion_options = ConversionOptions()
            if options:
                for key, value in options.items():
                    if hasattr(conversion_options, key):
                        setattr(conversion_options, key, value)
            
            # 执行转换
            result = self.format_converter.convert_to_format(
                content=content,
                source_format=source_format,
                target_format=OutputFormat(target_format),
                options=conversion_options
            )
            
            return result
            
        except Exception as e:
            logger.error(f"格式转换失败: {str(e)}")
            raise
    
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """分析内容
        
        对内容进行质量、结构和语义分析。
        
        Args:
            content: 待分析内容
            
        Returns:
            分析结果
        """
        try:
            # 执行内容分析
            quality_analysis = self.content_analyzer.analyze_content_quality(content)
            structure_analysis = self.content_analyzer.analyze_structure(content)
            semantic_analysis = self.content_analyzer.analyze_semantics(content)
            
            return {
                "quality": quality_analysis.__dict__,
                "structure": structure_analysis.__dict__,
                "semantics": semantic_analysis.__dict__
            }
            
        except Exception as e:
            logger.error(f"内容分析失败: {str(e)}")
            raise

    def validate_input(
        self,
        parameters: Dict[str, Any],
        validation_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """验证输入

        验证输入参数的有效性。

        Args:
            parameters: 输入参数
            validation_config: 验证配置

        Returns:
            验证结果
        """
        try:
            validation_config = validation_config or {}

            # 执行验证
            result = self.validation_utils.validate_input_parameters(
                parameters=parameters,
                required_fields=validation_config.get("required_fields", []),
                optional_fields=validation_config.get("optional_fields", []),
                validation_level=ValidationLevel(validation_config.get("level", "normal"))
            )

            return result.__dict__

        except Exception as e:
            logger.error(f"输入验证失败: {str(e)}")
            raise

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态

        Returns:
            系统状态信息
        """
        return {
            "generator_id": self.generator_id,
            "version": "1.0.0",
            "initialized": self.initialized,
            "components": {
                "planner": "ready",
                "content_generator": "ready",
                "content_optimizer": "ready",
                "pipeline_manager": "ready",
                "coordinator_agent": "ready" if self.coordinator_agent.initialized else "not_ready"
            },
            "config": {
                "generator_config": "loaded",
                "template_config": "loaded",
                "workflow_config": "loaded"
            },
            "tools": {
                "text_processor": "ready",
                "format_converter": "ready",
                "content_analyzer": "ready",
                "validation_utils": "ready"
            },
            "statistics": self.generation_stats
        }

    def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息

        Returns:
            生成统计信息
        """
        stats = self.generation_stats.copy()

        # 计算成功率
        total = stats["total_documents"]
        if total > 0:
            stats["success_rate"] = stats["successful_generations"] / total
            stats["failure_rate"] = stats["failed_generations"] / total
            stats["average_processing_time"] = stats["total_processing_time"] / total
        else:
            stats["success_rate"] = 0.0
            stats["failure_rate"] = 0.0
            stats["average_processing_time"] = 0.0

        return stats

    def reset_statistics(self):
        """重置统计信息"""
        self.generation_stats = {
            "total_documents": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "total_processing_time": 0.0
        }
        logger.info("生成统计信息已重置")

    def _validate_generation_input(
        self,
        project_path: str,
        project_name: str,
        config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """验证生成输入参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }

        # 验证项目路径
        if not project_path or not project_path.strip():
            validation_result["errors"].append("项目路径不能为空")
            validation_result["is_valid"] = False

        # 验证项目名称
        if not project_name or not project_name.strip():
            validation_result["errors"].append("项目名称不能为空")
            validation_result["is_valid"] = False

        # 验证配置
        if config is not None and not isinstance(config, dict):
            validation_result["errors"].append("配置必须是字典类型")
            validation_result["is_valid"] = False

        return validation_result

    def _merge_configs(self, user_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并配置"""
        # 从配置管理器获取默认配置
        default_config = {
            "planning": self.workflow_config.get_default_config("planning"),
            "generation": self.workflow_config.get_default_config("generation"),
            "refinement": self.workflow_config.get_default_config("refinement"),
            "monitoring": self.workflow_config.get_default_config("monitoring"),
            "caching": self.workflow_config.get_default_config("caching")
        }

        # 合并用户配置
        merged_config = {**self.config, **default_config}
        if user_config:
            merged_config.update(user_config)

        return merged_config

    def _update_stats(self, success: bool, processing_time: float):
        """更新统计信息"""
        self.generation_stats["total_documents"] += 1
        self.generation_stats["total_processing_time"] += processing_time

        if success:
            self.generation_stats["successful_generations"] += 1
        else:
            self.generation_stats["failed_generations"] += 1

    async def shutdown(self):
        """关闭生成器"""
        try:
            # 这里可以添加清理逻辑
            logger.info(f"智能文档生成器已关闭: {self.generator_id}")

        except Exception as e:
            logger.error(f"关闭生成器时发生错误: {str(e)}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        # 同步关闭（如果需要异步关闭，用户需要手动调用shutdown）
        # 忽略异常信息，因为这里不需要处理
        pass
