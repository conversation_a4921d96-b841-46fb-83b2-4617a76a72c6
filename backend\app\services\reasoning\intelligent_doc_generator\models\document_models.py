"""
智能文档数据模型

定义智能文档生成器使用的核心数据结构，包括：
- IntelligentDocument: 智能文档主体
- DynamicSection: 动态章节
- ContentMetadata: 内容元数据
- DocumentVersion: 文档版本控制
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator
import json


class SectionType(str, Enum):
    """章节类型枚举"""
    INTRODUCTION = "introduction"
    FEATURES = "features"
    INSTALLATION = "installation"
    USAGE = "usage"
    API_REFERENCE = "api_reference"
    ARCHITECTURE = "architecture"
    DEPENDENCIES = "dependencies"
    CONTRIBUTION = "contribution"
    LICENSE = "license"
    CUSTOM = "custom"


class ContentFormat(str, Enum):
    """内容格式枚举"""
    MARKDOWN = "markdown"
    HTML = "html"
    PLAINTEXT = "plaintext"
    JSON = "json"


class GenerationStatus(str, Enum):
    """生成状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ContentMetadata(BaseModel):
    """内容元数据模型"""
    
    # 基础信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    version: str = Field(default="1.0.0")
    
    # 生成信息
    generator_type: str = Field(default="")
    generation_time: float = Field(default=0.0)  # 生成耗时（秒）
    token_count: int = Field(default=0)  # AI token消耗
    
    # 质量信息
    quality_score: float = Field(default=0.0, ge=0.0, le=1.0)
    completeness_score: float = Field(default=0.0, ge=0.0, le=1.0)
    relevance_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 内容特征
    word_count: int = Field(default=0)
    complexity_level: str = Field(default="medium")  # low, medium, high
    target_audience: str = Field(default="general")
    
    # 标签和分类
    tags: List[str] = Field(default_factory=list)
    categories: List[str] = Field(default_factory=list)
    
    # 扩展属性
    extra_data: Dict[str, Any] = Field(default_factory=dict)
    
    @field_validator('created_at', 'updated_at')
    @classmethod
    def validate_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v


class DynamicSection(BaseModel):
    """动态章节模型"""
    
    # 基础信息
    section_id: str = Field(...)
    section_type: SectionType = Field(...)
    title: str = Field(...)
    
    # 内容
    content: str = Field(default="")
    content_format: ContentFormat = Field(default=ContentFormat.MARKDOWN)
    raw_content: Optional[str] = Field(default=None)  # 原始生成内容
    
    # 结构信息
    order: int = Field(default=0)
    level: int = Field(default=1, ge=1, le=6)  # 章节层级
    parent_id: Optional[str] = Field(default=None)
    children_ids: List[str] = Field(default_factory=list)
    
    # 生成信息
    status: GenerationStatus = Field(default=GenerationStatus.PENDING)
    dependencies: List[str] = Field(default_factory=list)  # 依赖的其他章节
    
    # 元数据
    metadata: ContentMetadata = Field(default_factory=ContentMetadata)
    
    # 配置参数
    generation_config: Dict[str, Any] = Field(default_factory=dict)
    template_name: Optional[str] = Field(default=None)
    
    # 反馈和改进
    feedback: List[str] = Field(default_factory=list)
    improvement_suggestions: List[str] = Field(default_factory=list)
    
    def to_markdown(self) -> str:
        """转换为Markdown格式"""
        if self.content_format == ContentFormat.MARKDOWN:
            return self.content
        elif self.content_format == ContentFormat.HTML:
            # 简单的HTML到Markdown转换
            import re
            markdown_content = self.content
            # 转换标题
            markdown_content = re.sub(r'<h([1-6])>(.*?)</h[1-6]>', r'{"#" * int(r"\1")} \2', markdown_content)
            # 转换粗体
            markdown_content = re.sub(r'<strong>(.*?)</strong>', r'**\1**', markdown_content)
            # 转换斜体
            markdown_content = re.sub(r'<em>(.*?)</em>', r'*\1*', markdown_content)
            # 转换代码块
            markdown_content = re.sub(r'<code>(.*?)</code>', r'`\1`', markdown_content)
            # 转换段落
            markdown_content = re.sub(r'<p>(.*?)</p>', r'\1\n', markdown_content)
            return markdown_content
        else:
            return self.content
    
    def to_html(self) -> str:
        """转换为HTML格式"""
        if self.content_format == ContentFormat.HTML:
            return self.content
        elif self.content_format == ContentFormat.MARKDOWN:
            # 简单的Markdown到HTML转换
            import re
            html_content = self.content
            # 转换标题
            html_content = re.sub(r'^(#{1,6})\s+(.*?)$', lambda m: f'<h{len(m.group(1))}>{m.group(2)}</h{len(m.group(1))}>', html_content, flags=re.MULTILINE)
            # 转换粗体
            html_content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html_content)
            # 转换斜体
            html_content = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html_content)
            # 转换代码
            html_content = re.sub(r'`(.*?)`', r'<code>\1</code>', html_content)
            # 转换段落
            html_content = re.sub(r'\n\n', '</p><p>', html_content)
            return f'<p>{html_content}</p>'
        else:
            return f'<pre>{self.content}</pre>'
    
    def update_metadata(self):
        """更新元数据"""
        self.metadata.updated_at = datetime.now(timezone.utc)
        self.metadata.word_count = len(self.content.split())


class DocumentVersion(BaseModel):
    """文档版本模型"""
    
    version_id: str = Field(...)
    version_number: str = Field(...)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 版本信息
    description: str = Field(default="")
    changelog: List[str] = Field(default_factory=list)
    
    # 内容快照
    sections: List[DynamicSection] = Field(default_factory=list)
    
    # 统计信息
    total_sections: int = Field(default=0)
    total_words: int = Field(default=0)
    generation_time: float = Field(default=0.0)
    
    # 质量指标
    average_quality_score: float = Field(default=0.0)
    completion_rate: float = Field(default=0.0)
    
    @field_validator('created_at')
    @classmethod
    def validate_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v


class IntelligentDocument(BaseModel):
    """智能文档主体模型"""
    
    # 基础信息
    document_id: str = Field(...)
    title: str = Field(...)
    description: str = Field(default="")
    
    # 项目信息
    project_path: str = Field(...)
    project_name: str = Field(...)
    project_type: str = Field(default="unknown")
    
    # 文档结构
    sections: List[DynamicSection] = Field(default_factory=list)
    section_order: List[str] = Field(default_factory=list)  # 章节ID的有序列表
    
    # 生成配置
    target_audience: str = Field(default="developers")
    documentation_style: str = Field(default="technical")
    language: str = Field(default="zh-CN")
    
    # 状态信息
    status: GenerationStatus = Field(default=GenerationStatus.PENDING)
    progress: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 元数据
    metadata: ContentMetadata = Field(default_factory=ContentMetadata)
    
    # 版本控制
    current_version: str = Field(default="1.0.0")
    versions: List[DocumentVersion] = Field(default_factory=list)
    
    # 配置和上下文
    generation_context: Dict[str, Any] = Field(default_factory=dict)
    customization_config: Dict[str, Any] = Field(default_factory=dict)
    
    # 反馈和统计
    user_feedback: List[str] = Field(default_factory=list)
    generation_metrics: Dict[str, Any] = Field(default_factory=dict)
    
    def get_section_by_id(self, section_id: str) -> Optional[DynamicSection]:
        """根据ID获取章节"""
        for section in self.sections:
            if section.section_id == section_id:
                return section
        return None
    
    def get_sections_by_type(self, section_type: SectionType) -> List[DynamicSection]:
        """根据类型获取章节列表"""
        return [section for section in self.sections if section.section_type == section_type]
    
    def add_section(self, section: DynamicSection, position: Optional[int] = None):
        """添加章节"""
        # 添加到章节列表
        self.sections.append(section)
        
        # 添加到有序列表
        if position is None:
            self.section_order.append(section.section_id)
        else:
            self.section_order.insert(position, section.section_id)
        
        # 更新进度
        self._update_progress()
    
    def remove_section(self, section_id: str):
        """移除章节"""
        # 从章节列表移除
        self.sections = [s for s in self.sections if s.section_id != section_id]
        
        # 从有序列表移除
        if section_id in self.section_order:
            self.section_order.remove(section_id)
        
        # 更新进度
        self._update_progress()
    
    def reorder_sections(self, section_order: List[str]):
        """重新排序章节"""
        # 验证章节ID有效性
        valid_ids = {s.section_id for s in self.sections}
        if not all(sid in valid_ids for sid in section_order):
            raise ValueError("无效的章节ID")
        
        self.section_order = section_order
        
        # 更新章节的order属性
        for i, section_id in enumerate(section_order):
            section = self.get_section_by_id(section_id)
            if section:
                section.order = i
    
    def _update_progress(self):
        """更新生成进度"""
        if not self.sections:
            self.progress = 0.0
            return
        
        completed_sections = len([s for s in self.sections if s.status == GenerationStatus.COMPLETED])
        self.progress = completed_sections / len(self.sections)
        
        # 更新整体状态
        if self.progress == 1.0:
            self.status = GenerationStatus.COMPLETED
        elif self.progress > 0:
            self.status = GenerationStatus.IN_PROGRESS
        else:
            self.status = GenerationStatus.PENDING
    
    def to_markdown(self) -> str:
        """转换为完整的Markdown文档"""
        markdown_parts = []
        
        # 文档标题
        if self.title:
            markdown_parts.append(f"# {self.title}")
            markdown_parts.append("")
        
        # 文档描述
        if self.description:
            markdown_parts.append(self.description)
            markdown_parts.append("")
        
        # 按顺序输出章节
        for section_id in self.section_order:
            section = self.get_section_by_id(section_id)
            if section and section.status == GenerationStatus.COMPLETED:
                # 添加章节标题
                header_level = "#" * (section.level + 1)  # 文档标题是h1，章节从h2开始
                markdown_parts.append(f"{header_level} {section.title}")
                markdown_parts.append("")
                
                # 添加章节内容
                section_content = section.to_markdown()
                if section_content:
                    markdown_parts.append(section_content)
                    markdown_parts.append("")
        
        return "\n".join(markdown_parts)
    
    def to_html(self) -> str:
        """转换为完整的HTML文档"""
        html_parts = []
        
        # 文档头部
        html_parts.append("<!DOCTYPE html>")
        html_parts.append("<html lang='zh-CN'>")
        html_parts.append("<head>")
        html_parts.append(f"<title>{self.title}</title>")
        html_parts.append("<meta charset='UTF-8'>")
        html_parts.append("<meta name='viewport' content='width=device-width, initial-scale=1.0'>")
        html_parts.append("</head>")
        html_parts.append("<body>")
        
        # 文档标题
        if self.title:
            html_parts.append(f"<h1>{self.title}</h1>")
        
        # 文档描述
        if self.description:
            html_parts.append(f"<p>{self.description}</p>")
        
        # 按顺序输出章节
        for section_id in self.section_order:
            section = self.get_section_by_id(section_id)
            if section and section.status == GenerationStatus.COMPLETED:
                # 添加章节标题
                header_level = section.level + 1  # 文档标题是h1，章节从h2开始
                html_parts.append(f"<h{header_level}>{section.title}</h{header_level}>")
                
                # 添加章节内容
                section_content = section.to_html()
                if section_content:
                    html_parts.append(section_content)
        
        # 文档尾部
        html_parts.append("</body>")
        html_parts.append("</html>")
        
        return "\n".join(html_parts)
    
    def create_version_snapshot(self, description: str = "", changelog: List[str] = None) -> DocumentVersion:
        """创建版本快照"""
        if changelog is None:
            changelog = []
        
        # 计算统计信息
        total_sections = len(self.sections)
        total_words = sum(section.metadata.word_count for section in self.sections)
        average_quality = sum(section.metadata.quality_score for section in self.sections) / max(total_sections, 1)
        completion_rate = len([s for s in self.sections if s.status == GenerationStatus.COMPLETED]) / max(total_sections, 1)
        
        # 创建版本快照
        version = DocumentVersion(
            version_id=f"{self.document_id}_v{self.current_version}",
            version_number=self.current_version,
            description=description,
            changelog=changelog,
            sections=self.sections.copy(),
            total_sections=total_sections,
            total_words=total_words,
            average_quality_score=average_quality,
            completion_rate=completion_rate
        )
        
        # 添加到版本历史
        self.versions.append(version)
        
        return version
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        return {
            "总章节数": len(self.sections),
            "已完成章节数": len([s for s in self.sections if s.status == GenerationStatus.COMPLETED]),
            "总字数": sum(section.metadata.word_count for section in self.sections),
            "平均质量分": sum(section.metadata.quality_score for section in self.sections) / max(len(self.sections), 1),
            "完成进度": self.progress,
            "生成状态": self.status.value,
            "目标受众": self.target_audience,
            "文档风格": self.documentation_style,
            "版本数量": len(self.versions)
        }
