"""
内容生成数据模型

定义内容生成过程中使用的数据结构，包括：
- GenerationTask: 生成任务
- GenerationContext: 生成上下文
- GenerationResult: 生成结果
- GenerationFeedback: 生成反馈
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class GenerationType(str, Enum):
    """生成类型枚举"""
    STRUCTURED = "structured"  # 结构化生成
    NARRATIVE = "narrative"  # 叙述性生成
    TECHNICAL = "technical"  # 技术性生成
    CREATIVE = "creative"  # 创意性生成
    MIXED = "mixed"  # 混合生成


class QualityLevel(str, Enum):
    """质量等级枚举"""
    DRAFT = "draft"  # 草稿质量
    STANDARD = "standard"  # 标准质量
    HIGH = "high"  # 高质量
    PREMIUM = "premium"  # 顶级质量


class GenerationTask(BaseModel):
    """生成任务模型"""
    
    # 基础信息
    task_id: str = Field(...)
    section_id: str = Field(...)
    title: str = Field(...)
    description: str = Field(default="")
    
    # 任务配置
    generation_type: GenerationType = Field(default=GenerationType.STRUCTURED)
    quality_level: QualityLevel = Field(default=QualityLevel.STANDARD)
    target_length: int = Field(default=500)  # 目标字数
    
    # 生成参数
    template_name: Optional[str] = Field(default=None)
    generation_params: Dict[str, Any] = Field(default_factory=dict)
    prompt_params: Dict[str, Any] = Field(default_factory=dict)
    
    # 任务状态
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    priority: int = Field(default=5, ge=1, le=10)  # 1-10，10最高
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    
    # 依赖关系
    dependencies: List[str] = Field(default_factory=list)  # 依赖的任务ID
    dependents: List[str] = Field(default_factory=list)  # 依赖此任务的任务ID
    
    # 重试配置
    max_retries: int = Field(default=3)
    retry_count: int = Field(default=0)
    retry_delay: float = Field(default=1.0)  # 重试延迟（秒）
    
    # 错误信息
    error_message: Optional[str] = Field(default=None)
    error_details: Dict[str, Any] = Field(default_factory=dict)
    
    # 资源需求
    estimated_tokens: int = Field(default=0)
    estimated_time: float = Field(default=0.0)  # 预估时间（秒）
    
    @field_validator('created_at', 'started_at', 'completed_at')
    @classmethod
    def validate_datetime(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def start(self):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now(timezone.utc)
    
    def complete(self):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
    
    def fail(self, error_message: str, error_details: Dict[str, Any] = None):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.error_details = error_details or {}
        self.completed_at = datetime.now(timezone.utc)
    
    def can_retry(self) -> bool:
        """判断是否可以重试"""
        return (
            self.status == TaskStatus.FAILED and
            self.retry_count < self.max_retries
        )
    
    def prepare_retry(self):
        """准备重试"""
        if self.can_retry():
            self.retry_count += 1
            self.status = TaskStatus.RETRYING
            self.error_message = None
            self.error_details = {}
    
    def get_duration(self) -> Optional[float]:
        """获取任务执行时长"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


class GenerationContext(BaseModel):
    """生成上下文模型"""
    
    # 基础信息
    context_id: str = Field(...)
    task_id: str = Field(...)
    
    # 项目信息
    project_path: str = Field(...)
    project_name: str = Field(...)
    project_type: str = Field(default="unknown")
    
    # 目标信息
    target_audience: str = Field(default="developers")
    documentation_style: str = Field(default="technical")
    language: str = Field(default="zh-CN")
    
    # 分析数据
    structure_analysis: Dict[str, Any] = Field(default_factory=dict)
    dependency_analysis: Dict[str, Any] = Field(default_factory=dict)
    module_analysis: Dict[str, Any] = Field(default_factory=dict)
    
    # 相关章节内容
    related_sections: Dict[str, str] = Field(default_factory=dict)  # 章节ID -> 内容
    previous_sections: List[str] = Field(default_factory=list)  # 已生成的章节ID
    
    # 内容指导
    content_guidelines: List[str] = Field(default_factory=list)
    key_points: List[str] = Field(default_factory=list)
    examples: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 格式要求
    format_requirements: Dict[str, Any] = Field(default_factory=dict)
    style_preferences: Dict[str, Any] = Field(default_factory=dict)
    
    # 质量要求
    quality_criteria: List[str] = Field(default_factory=list)
    review_checklist: List[str] = Field(default_factory=list)
    
    # 外部资源
    external_resources: List[str] = Field(default_factory=list)
    reference_materials: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 用户反馈
    user_preferences: Dict[str, Any] = Field(default_factory=dict)
    previous_feedback: List[str] = Field(default_factory=list)
    
    def get_relevant_data(self, data_type: str) -> Dict[str, Any]:
        """获取相关数据"""
        if data_type == "structure":
            return self.structure_analysis
        elif data_type == "dependency":
            return self.dependency_analysis
        elif data_type == "module":
            return self.module_analysis
        else:
            return {}
    
    def add_related_section(self, section_id: str, content: str):
        """添加相关章节内容"""
        self.related_sections[section_id] = content
        if section_id not in self.previous_sections:
            self.previous_sections.append(section_id)
    
    def get_context_summary(self) -> Dict[str, Any]:
        """获取上下文摘要"""
        return {
            "项目名称": self.project_name,
            "项目类型": self.project_type,
            "目标受众": self.target_audience,
            "文档风格": self.documentation_style,
            "语言": self.language,
            "已生成章节数": len(self.previous_sections),
            "相关章节数": len(self.related_sections),
            "内容指导数": len(self.content_guidelines),
            "关键点数": len(self.key_points)
        }


class GenerationResult(BaseModel):
    """生成结果模型"""
    
    # 基础信息
    result_id: str = Field(...)
    task_id: str = Field(...)
    section_id: str = Field(...)
    
    # 生成结果
    generated_content: str = Field(...)
    raw_content: Optional[str] = Field(default=None)  # 原始AI输出
    
    # 生成信息
    generation_type: GenerationType = Field(...)
    template_used: Optional[str] = Field(default=None)
    
    # 质量指标
    quality_score: float = Field(default=0.0, ge=0.0, le=1.0)
    coherence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    relevance_score: float = Field(default=0.0, ge=0.0, le=1.0)
    completeness_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 统计信息
    word_count: int = Field(default=0)
    character_count: int = Field(default=0)
    paragraph_count: int = Field(default=0)
    
    # 时间和资源
    generation_time: float = Field(default=0.0)  # 生成时间（秒）
    tokens_used: int = Field(default=0)
    api_calls: int = Field(default=0)
    
    # 改进建议
    improvement_suggestions: List[str] = Field(default_factory=list)
    potential_issues: List[str] = Field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)
    generation_params: Dict[str, Any] = Field(default_factory=dict)
    
    # 时间戳
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    @field_validator('created_at')
    @classmethod
    def validate_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def calculate_statistics(self):
        """计算统计信息"""
        if self.generated_content:
            self.word_count = len(self.generated_content.split())
            self.character_count = len(self.generated_content)
            self.paragraph_count = len([p for p in self.generated_content.split('\n\n') if p.strip()])
    
    def get_quality_summary(self) -> Dict[str, Any]:
        """获取质量摘要"""
        return {
            "整体质量": self.quality_score,
            "连贯性": self.coherence_score,
            "相关性": self.relevance_score,
            "完整性": self.completeness_score,
            "字数": self.word_count,
            "生成时间": f"{self.generation_time:.2f}秒",
            "Token消耗": self.tokens_used,
            "改进建议数": len(self.improvement_suggestions),
            "潜在问题数": len(self.potential_issues)
        }
    
    def is_acceptable(self, min_quality: float = 0.6) -> bool:
        """判断结果是否可接受"""
        return (
            self.quality_score >= min_quality and
            self.completeness_score >= 0.5 and
            self.word_count > 0
        )


class FeedbackType(str, Enum):
    """反馈类型枚举"""
    QUALITY = "quality"
    CONTENT = "content"
    STYLE = "style"
    ACCURACY = "accuracy"
    COMPLETENESS = "completeness"
    SUGGESTION = "suggestion"


class FeedbackSource(str, Enum):
    """反馈来源枚举"""
    USER = "user"
    AI_REVIEWER = "ai_reviewer"
    QUALITY_CONTROLLER = "quality_controller"
    PEER_REVIEW = "peer_review"
    AUTOMATED_CHECK = "automated_check"


class GenerationFeedback(BaseModel):
    """生成反馈模型"""
    
    # 基础信息
    feedback_id: str = Field(...)
    result_id: str = Field(...)
    task_id: str = Field(...)
    
    # 反馈信息
    feedback_type: FeedbackType = Field(...)
    source: FeedbackSource = Field(...)
    rating: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 反馈内容
    comments: str = Field(default="")
    suggestions: List[str] = Field(default_factory=list)
    issues: List[str] = Field(default_factory=list)
    
    # 具体评分
    detailed_scores: Dict[str, float] = Field(default_factory=dict)
    
    # 改进建议
    improvement_actions: List[str] = Field(default_factory=list)
    priority_issues: List[str] = Field(default_factory=list)
    
    # 上下文信息
    reviewer_context: Dict[str, Any] = Field(default_factory=dict)
    review_criteria: List[str] = Field(default_factory=list)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 反馈处理
    is_addressed: bool = Field(default=False)
    addressed_at: Optional[datetime] = Field(default=None)
    resolution_notes: str = Field(default="")
    
    @field_validator('created_at', 'addressed_at')
    @classmethod
    def validate_datetime(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def mark_addressed(self, resolution_notes: str = ""):
        """标记反馈已处理"""
        self.is_addressed = True
        self.addressed_at = datetime.now(timezone.utc)
        self.resolution_notes = resolution_notes
    
    def get_priority_level(self) -> str:
        """获取优先级等级"""
        if self.rating < 0.3:
            return "critical"
        elif self.rating < 0.6:
            return "high"
        elif self.rating < 0.8:
            return "medium"
        else:
            return "low"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "反馈ID": self.feedback_id,
            "反馈类型": self.feedback_type.value,
            "反馈来源": self.source.value,
            "评分": self.rating,
            "优先级": self.get_priority_level(),
            "评论": self.comments,
            "建议数": len(self.suggestions),
            "问题数": len(self.issues),
            "是否已处理": self.is_addressed,
            "创建时间": self.created_at.isoformat()
        }


class GenerationBatch(BaseModel):
    """批量生成模型"""
    
    # 基础信息
    batch_id: str = Field(...)
    document_id: str = Field(...)
    title: str = Field(...)
    
    # 任务信息
    tasks: List[GenerationTask] = Field(default_factory=list)
    total_tasks: int = Field(default=0)
    completed_tasks: int = Field(default=0)
    failed_tasks: int = Field(default=0)
    
    # 批次状态
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    progress: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    
    # 配置信息
    parallel_limit: int = Field(default=3)  # 并行任务数量限制
    retry_failed: bool = Field(default=True)
    
    # 结果信息
    results: List[GenerationResult] = Field(default_factory=list)
    batch_statistics: Dict[str, Any] = Field(default_factory=dict)
    
    @field_validator('created_at', 'started_at', 'completed_at')
    @classmethod
    def validate_datetime(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def add_task(self, task: GenerationTask):
        """添加任务"""
        self.tasks.append(task)
        self.total_tasks = len(self.tasks)
        self._update_progress()
    
    def update_task_status(self, task_id: str, status: TaskStatus):
        """更新任务状态"""
        task = next((t for t in self.tasks if t.task_id == task_id), None)
        if task:
            task.status = status
            self._update_counts()
            self._update_progress()
    
    def _update_counts(self):
        """更新计数"""
        self.completed_tasks = len([t for t in self.tasks if t.status == TaskStatus.COMPLETED])
        self.failed_tasks = len([t for t in self.tasks if t.status == TaskStatus.FAILED])
    
    def _update_progress(self):
        """更新进度"""
        if self.total_tasks == 0:
            self.progress = 0.0
        else:
            self.progress = self.completed_tasks / self.total_tasks
        
        # 更新批次状态
        if self.progress == 1.0:
            self.status = TaskStatus.COMPLETED
            if not self.completed_at:
                self.completed_at = datetime.now(timezone.utc)
        elif self.progress > 0:
            self.status = TaskStatus.RUNNING
            if not self.started_at:
                self.started_at = datetime.now(timezone.utc)
    
    def get_pending_tasks(self) -> List[GenerationTask]:
        """获取待处理任务"""
        return [t for t in self.tasks if t.status == TaskStatus.PENDING]
    
    def get_failed_tasks(self) -> List[GenerationTask]:
        """获取失败任务"""
        return [t for t in self.tasks if t.status == TaskStatus.FAILED]
    
    def get_batch_summary(self) -> Dict[str, Any]:
        """获取批次摘要"""
        return {
            "批次ID": self.batch_id,
            "总任务数": self.total_tasks,
            "已完成": self.completed_tasks,
            "失败任务": self.failed_tasks,
            "进度": f"{self.progress:.1%}",
            "状态": self.status.value,
            "并行限制": self.parallel_limit,
            "创建时间": self.created_at.isoformat(),
            "耗时": self.get_duration()
        }
    
    def get_duration(self) -> Optional[str]:
        """获取执行时长"""
        if self.started_at and self.completed_at:
            duration = self.completed_at - self.started_at
            return f"{duration.total_seconds():.1f}秒"
        return None
