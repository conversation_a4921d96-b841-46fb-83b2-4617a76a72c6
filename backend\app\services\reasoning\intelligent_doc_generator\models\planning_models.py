"""
文档规划数据模型

定义文档规划过程中使用的数据结构，包括：
- DocumentPlan: 文档规划方案
- SectionPlan: 章节规划
- PlanningContext: 规划上下文
- PlanEvaluation: 规划评估
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Tuple
from enum import Enum
from pydantic import BaseModel, Field, field_validator

from .document_models import SectionType


class PlanningStrategy(str, Enum):
    """规划策略枚举"""
    ADAPTIVE = "adaptive"  # 自适应规划
    TEMPLATE_BASED = "template_based"  # 基于模板规划
    DOMAIN_SPECIFIC = "domain_specific"  # 领域特定规划
    USER_DEFINED = "user_defined"  # 用户自定义规划


class SectionPriority(str, Enum):
    """章节优先级枚举"""
    CRITICAL = "critical"  # 关键章节
    HIGH = "high"  # 高优先级
    MEDIUM = "medium"  # 中等优先级
    LOW = "low"  # 低优先级
    OPTIONAL = "optional"  # 可选章节


class PlanningStatus(str, Enum):
    """规划状态枚举"""
    DRAFT = "draft"  # 草案状态
    REVIEWED = "reviewed"  # 已审阅
    APPROVED = "approved"  # 已批准
    REJECTED = "rejected"  # 已拒绝
    MODIFIED = "modified"  # 已修改


class SectionPlan(BaseModel):
    """章节规划模型"""
    
    # 基础信息
    section_id: str = Field(...)
    section_type: SectionType = Field(...)
    title: str = Field(...)
    description: str = Field(default="")
    
    # 规划信息
    priority: SectionPriority = Field(default=SectionPriority.MEDIUM)
    estimated_length: int = Field(default=500)  # 预估字数
    complexity_level: str = Field(default="medium")  # low, medium, high
    
    # 结构信息
    order: int = Field(default=0)
    level: int = Field(default=1, ge=1, le=6)
    parent_id: Optional[str] = Field(default=None)
    children_ids: List[str] = Field(default_factory=list)
    
    # 依赖关系
    dependencies: List[str] = Field(default_factory=list)  # 依赖的章节ID
    blocks: List[str] = Field(default_factory=list)  # 阻塞的章节ID
    
    # 生成配置
    generation_strategy: str = Field(default="default")
    template_name: Optional[str] = Field(default=None)
    generation_params: Dict[str, Any] = Field(default_factory=dict)
    
    # 内容指导
    content_guidelines: List[str] = Field(default_factory=list)
    key_points: List[str] = Field(default_factory=list)
    target_audience_notes: str = Field(default="")
    
    # 质量要求
    quality_requirements: Dict[str, Any] = Field(default_factory=dict)
    review_criteria: List[str] = Field(default_factory=list)
    
    # 资源需求
    required_data_sources: List[str] = Field(default_factory=list)
    external_resources: List[str] = Field(default_factory=list)
    
    def calculate_dependency_depth(self, all_sections: List['SectionPlan']) -> int:
        """计算依赖深度"""
        if not self.dependencies:
            return 0
        
        max_depth = 0
        section_map = {s.section_id: s for s in all_sections}
        
        for dep_id in self.dependencies:
            if dep_id in section_map:
                dep_section = section_map[dep_id]
                depth = dep_section.calculate_dependency_depth(all_sections) + 1
                max_depth = max(max_depth, depth)
        
        return max_depth
    
    def is_ready_for_generation(self, completed_sections: List[str]) -> bool:
        """检查是否准备好生成"""
        return all(dep_id in completed_sections for dep_id in self.dependencies)


class PlanningContext(BaseModel):
    """规划上下文模型"""
    
    # 项目信息
    project_path: str = Field(...)
    project_name: str = Field(...)
    project_type: str = Field(default="unknown")
    
    # 分析数据
    structure_analysis: Dict[str, Any] = Field(default_factory=dict)
    dependency_analysis: Dict[str, Any] = Field(default_factory=dict)
    module_analysis: Dict[str, Any] = Field(default_factory=dict)
    
    # 用户需求
    target_audience: str = Field(default="developers")
    documentation_style: str = Field(default="technical")
    language: str = Field(default="zh-CN")
    
    # 约束条件
    max_sections: int = Field(default=20)
    preferred_length: int = Field(default=5000)  # 总字数
    time_constraints: Optional[int] = Field(default=None)  # 时间限制（分钟）
    
    # 自定义配置
    custom_sections: List[Dict[str, Any]] = Field(default_factory=list)
    excluded_sections: List[SectionType] = Field(default_factory=list)
    required_sections: List[SectionType] = Field(default_factory=list)
    
    # 模板信息
    template_preferences: List[str] = Field(default_factory=list)
    style_preferences: Dict[str, Any] = Field(default_factory=dict)
    
    # 历史信息
    previous_plans: List[str] = Field(default_factory=list)  # 历史规划ID
    user_feedback: List[str] = Field(default_factory=list)
    
    def get_project_characteristics(self) -> Dict[str, Any]:
        """获取项目特征"""
        characteristics = {
            "项目类型": self.project_type,
            "目标受众": self.target_audience,
            "文档风格": self.documentation_style,
            "语言": self.language
        }
        
        # 从分析数据中提取特征
        if self.structure_analysis:
            characteristics.update({
                "主要语言": self.structure_analysis.get("primary_language", "unknown"),
                "项目规模": self.structure_analysis.get("project_size", "unknown"),
                "复杂度": self.structure_analysis.get("complexity", "medium")
            })
        
        return characteristics


class PlanEvaluation(BaseModel):
    """规划评估模型"""
    
    # 基础信息
    evaluation_id: str = Field(...)
    plan_id: str = Field(...)
    evaluator: str = Field(...)  # 评估者（AI/人工）
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 评估分数
    overall_score: float = Field(default=0.0, ge=0.0, le=1.0)
    completeness_score: float = Field(default=0.0, ge=0.0, le=1.0)
    relevance_score: float = Field(default=0.0, ge=0.0, le=1.0)
    feasibility_score: float = Field(default=0.0, ge=0.0, le=1.0)
    coherence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 详细评估
    strengths: List[str] = Field(default_factory=list)
    weaknesses: List[str] = Field(default_factory=list)
    improvement_suggestions: List[str] = Field(default_factory=list)
    
    # 章节评估
    section_evaluations: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    missing_sections: List[str] = Field(default_factory=list)
    redundant_sections: List[str] = Field(default_factory=list)
    
    # 结构评估
    structure_issues: List[str] = Field(default_factory=list)
    dependency_issues: List[str] = Field(default_factory=list)
    ordering_suggestions: List[str] = Field(default_factory=list)
    
    # 资源评估
    resource_requirements: Dict[str, Any] = Field(default_factory=dict)
    estimated_time: float = Field(default=0.0)  # 预估生成时间（分钟）
    estimated_tokens: int = Field(default=0)  # 预估token消耗
    
    @field_validator('created_at')
    @classmethod
    def validate_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def is_approved(self) -> bool:
        """判断规划是否通过评估"""
        return (
            self.overall_score >= 0.7 and
            self.completeness_score >= 0.6 and
            self.relevance_score >= 0.7 and
            self.feasibility_score >= 0.8
        )
    
    def get_critical_issues(self) -> List[str]:
        """获取关键问题"""
        critical_issues = []
        
        if self.overall_score < 0.5:
            critical_issues.append("整体质量过低")
        
        if self.completeness_score < 0.4:
            critical_issues.append("规划完整性不足")
        
        if self.feasibility_score < 0.6:
            critical_issues.append("实现可行性存疑")
            
        critical_issues.extend(self.structure_issues)
        critical_issues.extend(self.dependency_issues)
        
        return critical_issues


class DocumentPlan(BaseModel):
    """文档规划方案模型"""
    
    # 基础信息
    plan_id: str = Field(...)
    title: str = Field(...)
    description: str = Field(default="")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 规划配置
    strategy: PlanningStrategy = Field(default=PlanningStrategy.ADAPTIVE)
    template_name: Optional[str] = Field(default=None)
    
    # 规划内容
    sections: List[SectionPlan] = Field(default_factory=list)
    section_order: List[str] = Field(default_factory=list)
    
    # 上下文信息
    planning_context: PlanningContext = Field(...)
    
    # 状态信息
    status: PlanningStatus = Field(default=PlanningStatus.DRAFT)
    version: str = Field(default="1.0.0")
    
    # 评估信息
    evaluations: List[PlanEvaluation] = Field(default_factory=list)
    current_evaluation: Optional[PlanEvaluation] = Field(default=None)
    
    # 统计信息
    total_estimated_length: int = Field(default=0)
    estimated_generation_time: float = Field(default=0.0)
    complexity_distribution: Dict[str, int] = Field(default_factory=dict)
    
    # 配置参数
    generation_config: Dict[str, Any] = Field(default_factory=dict)
    quality_config: Dict[str, Any] = Field(default_factory=dict)
    
    @field_validator('created_at', 'updated_at')
    @classmethod
    def validate_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def add_section(self, section_plan: SectionPlan, position: Optional[int] = None):
        """添加章节规划"""
        self.sections.append(section_plan)
        
        if position is None:
            self.section_order.append(section_plan.section_id)
        else:
            self.section_order.insert(position, section_plan.section_id)
        
        self._update_statistics()
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_section(self, section_id: str):
        """移除章节规划"""
        self.sections = [s for s in self.sections if s.section_id != section_id]
        
        if section_id in self.section_order:
            self.section_order.remove(section_id)
        
        self._update_statistics()
        self.updated_at = datetime.now(timezone.utc)
    
    def get_section_by_id(self, section_id: str) -> Optional[SectionPlan]:
        """根据ID获取章节规划"""
        for section in self.sections:
            if section.section_id == section_id:
                return section
        return None
    
    def get_sections_by_priority(self, priority: SectionPriority) -> List[SectionPlan]:
        """根据优先级获取章节规划"""
        return [s for s in self.sections if s.priority == priority]
    
    def get_generation_order(self) -> List[str]:
        """获取生成顺序（考虑依赖关系）"""
        # 拓扑排序，处理依赖关系
        result = []
        visited = set()
        temp_visited = set()
        
        def visit(section_id: str):
            if section_id in temp_visited:
                raise ValueError(f"检测到循环依赖: {section_id}")
            if section_id in visited:
                return
            
            temp_visited.add(section_id)
            section = self.get_section_by_id(section_id)
            
            if section:
                for dep_id in section.dependencies:
                    if dep_id in [s.section_id for s in self.sections]:
                        visit(dep_id)
            
            temp_visited.remove(section_id)
            visited.add(section_id)
            result.append(section_id)
        
        # 按照section_order的顺序处理
        for section_id in self.section_order:
            if section_id not in visited:
                visit(section_id)
        
        return result
    
    def validate_dependencies(self) -> List[str]:
        """验证依赖关系"""
        issues = []
        section_ids = {s.section_id for s in self.sections}
        
        for section in self.sections:
            # 检查依赖是否存在
            for dep_id in section.dependencies:
                if dep_id not in section_ids:
                    issues.append(f"章节 {section.section_id} 依赖不存在的章节 {dep_id}")
            
            # 检查循环依赖
            try:
                section.calculate_dependency_depth(self.sections)
            except RecursionError:
                issues.append(f"章节 {section.section_id} 存在循环依赖")
        
        return issues
    
    def _update_statistics(self):
        """更新统计信息"""
        self.total_estimated_length = sum(s.estimated_length for s in self.sections)
        
        # 统计复杂度分布
        complexity_count = {}
        for section in self.sections:
            complexity = section.complexity_level
            complexity_count[complexity] = complexity_count.get(complexity, 0) + 1
        self.complexity_distribution = complexity_count
        
        # 估算生成时间（基于复杂度和长度）
        time_factors = {"low": 0.5, "medium": 1.0, "high": 2.0}
        total_time = 0
        for section in self.sections:
            factor = time_factors.get(section.complexity_level, 1.0)
            section_time = (section.estimated_length / 100) * factor  # 每100字约1分钟
            total_time += section_time
        self.estimated_generation_time = total_time
    
    def create_evaluation(self, evaluator: str) -> PlanEvaluation:
        """创建评估"""
        evaluation = PlanEvaluation(
            evaluation_id=f"{self.plan_id}_eval_{len(self.evaluations)}",
            plan_id=self.plan_id,
            evaluator=evaluator
        )
        
        # 基础评估逻辑
        total_sections = len(self.sections)
        if total_sections == 0:
            evaluation.completeness_score = 0.0
        else:
            # 检查关键章节是否存在
            required_types = [SectionType.INTRODUCTION, SectionType.FEATURES, SectionType.USAGE]
            present_types = {s.section_type for s in self.sections}
            required_present = len(required_types & present_types)
            evaluation.completeness_score = required_present / len(required_types)
        
        # 相关性评估（基于项目类型）
        evaluation.relevance_score = 0.8  # 默认相关性
        
        # 可行性评估
        dependency_issues = self.validate_dependencies()
        evaluation.feasibility_score = 0.9 if not dependency_issues else 0.6
        evaluation.dependency_issues = dependency_issues
        
        # 连贯性评估
        evaluation.coherence_score = 0.8  # 默认连贯性
        
        # 整体评分
        evaluation.overall_score = (
            evaluation.completeness_score * 0.3 +
            evaluation.relevance_score * 0.25 +
            evaluation.feasibility_score * 0.25 +
            evaluation.coherence_score * 0.2
        )
        
        # 添加到评估列表
        self.evaluations.append(evaluation)
        self.current_evaluation = evaluation
        
        return evaluation
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "规划ID": self.plan_id,
            "标题": self.title,
            "策略": self.strategy.value,
            "章节数量": len(self.sections),
            "预估总长度": self.total_estimated_length,
            "预估生成时间": f"{self.estimated_generation_time:.1f}分钟",
            "状态": self.status.value,
            "版本": self.version,
            "复杂度分布": self.complexity_distribution,
            "章节列表": [
                {
                    "ID": s.section_id,
                    "类型": s.section_type.value,
                    "标题": s.title,
                    "优先级": s.priority.value,
                    "预估长度": s.estimated_length,
                    "复杂度": s.complexity_level
                }
                for s in self.sections
            ]
        }
