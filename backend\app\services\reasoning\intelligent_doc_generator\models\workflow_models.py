"""
工作流数据模型

定义工作流管理相关的数据结构，包括：
- WorkflowState: 工作流状态
- WorkflowStep: 工作流步骤
- WorkflowExecution: 工作流执行
- TaskExecution: 任务执行
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator


class WorkflowStatus(str, Enum):
    """工作流状态枚举"""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class StepType(str, Enum):
    """步骤类型枚举"""
    ANALYSIS = "analysis"  # 分析步骤
    PLANNING = "planning"  # 规划步骤
    GENERATION = "generation"  # 生成步骤
    REVIEW = "review"  # 审查步骤
    REFINEMENT = "refinement"  # 精化步骤
    VALIDATION = "validation"  # 验证步骤
    ASSEMBLY = "assembly"  # 组装步骤


class ExecutionMode(str, Enum):
    """执行模式枚举"""
    SEQUENTIAL = "sequential"  # 顺序执行
    PARALLEL = "parallel"  # 并行执行
    CONDITIONAL = "conditional"  # 条件执行
    LOOP = "loop"  # 循环执行


class WorkflowStep(BaseModel):
    """工作流步骤模型"""
    
    # 基础信息
    step_id: str = Field(...)
    name: str = Field(...)
    description: str = Field(default="")
    step_type: StepType = Field(...)
    
    # 执行配置
    execution_mode: ExecutionMode = Field(default=ExecutionMode.SEQUENTIAL)
    timeout: int = Field(default=300)  # 超时时间（秒）
    max_retries: int = Field(default=3)
    
    # 依赖关系
    dependencies: List[str] = Field(default_factory=list)  # 依赖的步骤ID
    conditions: Dict[str, Any] = Field(default_factory=dict)  # 执行条件
    
    # 输入输出
    input_schema: Dict[str, Any] = Field(default_factory=dict)
    output_schema: Dict[str, Any] = Field(default_factory=dict)
    
    # 执行配置
    processor_class: str = Field(...)  # 处理器类名
    processor_config: Dict[str, Any] = Field(default_factory=dict)
    
    # 错误处理
    error_handling: str = Field(default="stop")  # stop, skip, retry
    fallback_step: Optional[str] = Field(default=None)
    
    # 监控配置
    enable_monitoring: bool = Field(default=True)
    enable_logging: bool = Field(default=True)
    
    def can_execute(self, completed_steps: List[str]) -> bool:
        """检查是否可以执行"""
        return all(dep in completed_steps for dep in self.dependencies)
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        if not self.input_schema:
            return True
        
        # 简单验证 - 检查必需字段
        required_fields = self.input_schema.get("required", [])
        return all(field in input_data for field in required_fields)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "步骤ID": self.step_id,
            "名称": self.name,
            "类型": self.step_type.value,
            "执行模式": self.execution_mode.value,
            "超时时间": f"{self.timeout}秒",
            "最大重试": self.max_retries,
            "依赖数": len(self.dependencies),
            "处理器": self.processor_class
        }


class WorkflowState(BaseModel):
    """工作流状态模型"""
    
    # 基础信息
    workflow_id: str = Field(...)
    execution_id: str = Field(...)
    
    # 状态信息
    status: WorkflowStatus = Field(default=WorkflowStatus.CREATED)
    current_step: Optional[str] = Field(default=None)
    
    # 执行信息
    completed_steps: List[str] = Field(default_factory=list)
    failed_steps: List[str] = Field(default_factory=list)
    skipped_steps: List[str] = Field(default_factory=list)
    
    # 数据状态
    shared_data: Dict[str, Any] = Field(default_factory=dict)  # 步骤间共享数据
    step_outputs: Dict[str, Any] = Field(default_factory=dict)  # 步骤输出
    step_inputs: Dict[str, Any] = Field(default_factory=dict)  # 步骤输入
    
    # 执行统计
    total_steps: int = Field(default=0)
    completed_count: int = Field(default=0)
    failed_count: int = Field(default=0)
    progress: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(default=None)
    updated_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    
    # 错误信息
    error_message: Optional[str] = Field(default=None)
    error_details: Dict[str, Any] = Field(default_factory=dict)
    error_stack: List[str] = Field(default_factory=list)
    
    # 暂停恢复
    pause_reason: Optional[str] = Field(default=None)
    can_resume: bool = Field(default=True)
    
    @field_validator('created_at', 'started_at', 'updated_at', 'completed_at')
    @classmethod
    def validate_datetime(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def start(self):
        """开始执行"""
        self.status = WorkflowStatus.RUNNING
        self.started_at = datetime.now(timezone.utc)
        self.updated_at = self.started_at
    
    def complete_step(self, step_id: str, output_data: Dict[str, Any] = None):
        """完成步骤"""
        if step_id not in self.completed_steps:
            self.completed_steps.append(step_id)
            self.completed_count = len(self.completed_steps)
            
        if output_data:
            self.step_outputs[step_id] = output_data
            
        self._update_progress()
        self._update_timestamp()
    
    def fail_step(self, step_id: str, error_message: str):
        """步骤失败"""
        if step_id not in self.failed_steps:
            self.failed_steps.append(step_id)
            self.failed_count = len(self.failed_steps)
            
        self.error_message = error_message
        self.error_stack.append(f"{step_id}: {error_message}")
        self._update_timestamp()
    
    def skip_step(self, step_id: str, reason: str = ""):
        """跳过步骤"""
        if step_id not in self.skipped_steps:
            self.skipped_steps.append(step_id)
            
        if reason:
            self.shared_data[f"skip_reason_{step_id}"] = reason
            
        self._update_timestamp()
    
    def set_current_step(self, step_id: str):
        """设置当前步骤"""
        self.current_step = step_id
        self._update_timestamp()
    
    def complete(self):
        """完成工作流"""
        self.status = WorkflowStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        self.updated_at = self.completed_at
        self.current_step = None
    
    def fail(self, error_message: str, error_details: Dict[str, Any] = None):
        """工作流失败"""
        self.status = WorkflowStatus.FAILED
        self.error_message = error_message
        if error_details:
            self.error_details.update(error_details)
        self.completed_at = datetime.now(timezone.utc)
        self.updated_at = self.completed_at
    
    def pause(self, reason: str = ""):
        """暂停工作流"""
        self.status = WorkflowStatus.PAUSED
        self.pause_reason = reason
        self._update_timestamp()
    
    def resume(self):
        """恢复工作流"""
        if self.can_resume:
            self.status = WorkflowStatus.RUNNING
            self.pause_reason = None
            self._update_timestamp()
    
    def cancel(self):
        """取消工作流"""
        self.status = WorkflowStatus.CANCELLED
        self.completed_at = datetime.now(timezone.utc)
        self.updated_at = self.completed_at
    
    def _update_progress(self):
        """更新进度"""
        if self.total_steps > 0:
            self.progress = self.completed_count / self.total_steps
        else:
            self.progress = 0.0
    
    def _update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now(timezone.utc)
    
    def get_state_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            "工作流ID": self.workflow_id,
            "执行ID": self.execution_id,
            "状态": self.status.value,
            "当前步骤": self.current_step,
            "总步骤数": self.total_steps,
            "已完成": self.completed_count,
            "失败数": self.failed_count,
            "跳过数": len(self.skipped_steps),
            "进度": f"{self.progress:.1%}",
            "运行时长": self.get_duration(),
            "最后更新": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_duration(self) -> Optional[str]:
        """获取执行时长"""
        if self.started_at:
            end_time = self.completed_at or datetime.now(timezone.utc)
            duration = end_time - self.started_at
            return f"{duration.total_seconds():.1f}秒"
        return None


class TaskExecution(BaseModel):
    """任务执行模型"""
    
    # 基础信息
    execution_id: str = Field(...)
    task_id: str = Field(...)
    step_id: str = Field(...)
    workflow_id: str = Field(...)
    
    # 执行信息
    status: WorkflowStatus = Field(default=WorkflowStatus.CREATED)
    attempt: int = Field(default=1)
    max_attempts: int = Field(default=3)
    
    # 输入输出
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    
    # 执行配置
    processor: str = Field(...)
    config: Dict[str, Any] = Field(default_factory=dict)
    
    # 资源使用
    memory_usage: float = Field(default=0.0)  # MB
    cpu_usage: float = Field(default=0.0)  # 百分比
    tokens_used: int = Field(default=0)
    api_calls: int = Field(default=0)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = Field(default=None)
    completed_at: Optional[datetime] = Field(default=None)
    
    # 错误信息
    error_message: Optional[str] = Field(default=None)
    error_type: Optional[str] = Field(default=None)
    error_traceback: Optional[str] = Field(default=None)
    
    # 监控数据
    checkpoints: List[Dict[str, Any]] = Field(default_factory=list)
    metrics: Dict[str, float] = Field(default_factory=dict)
    logs: List[str] = Field(default_factory=list)
    
    @field_validator('created_at', 'started_at', 'completed_at')
    @classmethod
    def validate_datetime(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v
    
    def start(self):
        """开始执行"""
        self.status = WorkflowStatus.RUNNING
        self.started_at = datetime.now(timezone.utc)
    
    def complete(self, output_data: Dict[str, Any] = None):
        """完成执行"""
        self.status = WorkflowStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        if output_data:
            self.output_data = output_data
    
    def fail(self, error_message: str, error_type: str = None, traceback: str = None):
        """执行失败"""
        self.status = WorkflowStatus.FAILED
        self.error_message = error_message
        self.error_type = error_type
        self.error_traceback = traceback
        self.completed_at = datetime.now(timezone.utc)
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return (
            self.status == WorkflowStatus.FAILED and
            self.attempt < self.max_attempts
        )
    
    def prepare_retry(self):
        """准备重试"""
        if self.can_retry():
            self.attempt += 1
            self.status = WorkflowStatus.CREATED
            self.error_message = None
            self.error_type = None
            self.error_traceback = None
            self.started_at = None
            self.completed_at = None
    
    def add_checkpoint(self, name: str, data: Dict[str, Any]):
        """添加检查点"""
        checkpoint = {
            "name": name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": data
        }
        self.checkpoints.append(checkpoint)
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志"""
        log_entry = f"[{datetime.now(timezone.utc).isoformat()}] {level}: {message}"
        self.logs.append(log_entry)
    
    def update_metrics(self, metrics: Dict[str, float]):
        """更新指标"""
        self.metrics.update(metrics)
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        return {
            "执行ID": self.execution_id,
            "任务ID": self.task_id,
            "步骤ID": self.step_id,
            "状态": self.status.value,
            "尝试次数": f"{self.attempt}/{self.max_attempts}",
            "处理器": self.processor,
            "执行时长": self.get_duration(),
            "内存使用": f"{self.memory_usage:.1f}MB",
            "CPU使用": f"{self.cpu_usage:.1f}%",
            "Token消耗": self.tokens_used,
            "API调用": self.api_calls,
            "检查点数": len(self.checkpoints),
            "日志条数": len(self.logs)
        }
    
    def get_duration(self) -> Optional[str]:
        """获取执行时长"""
        if self.started_at and self.completed_at:
            duration = self.completed_at - self.started_at
            return f"{duration.total_seconds():.1f}秒"
        return None


class WorkflowExecution(BaseModel):
    """工作流执行模型"""
    
    # 基础信息
    execution_id: str = Field(...)
    workflow_id: str = Field(...)
    name: str = Field(...)
    description: str = Field(default="")
    
    # 工作流定义
    steps: List[WorkflowStep] = Field(default_factory=list)
    step_order: List[str] = Field(default_factory=list)
    
    # 执行状态
    state: WorkflowState = Field(...)
    
    # 执行配置
    execution_config: Dict[str, Any] = Field(default_factory=dict)
    parallel_limit: int = Field(default=3)
    timeout: int = Field(default=3600)  # 总超时时间（秒）
    
    # 任务执行记录
    task_executions: List[TaskExecution] = Field(default_factory=list)
    
    # 触发信息
    trigger_type: str = Field(default="manual")  # manual, scheduled, event
    trigger_data: Dict[str, Any] = Field(default_factory=dict)
    
    # 用户信息
    user_id: Optional[str] = Field(default=None)
    session_id: Optional[str] = Field(default=None)
    
    # 结果信息
    final_output: Dict[str, Any] = Field(default_factory=dict)
    execution_report: Dict[str, Any] = Field(default_factory=dict)
    
    def add_step(self, step: WorkflowStep):
        """添加步骤"""
        if step.step_id not in [s.step_id for s in self.steps]:
            self.steps.append(step)
            self.step_order.append(step.step_id)
            self.state.total_steps = len(self.steps)
    
    def get_step(self, step_id: str) -> Optional[WorkflowStep]:
        """获取步骤"""
        return next((s for s in self.steps if s.step_id == step_id), None)
    
    def get_next_steps(self) -> List[WorkflowStep]:
        """获取下一个可执行的步骤"""
        next_steps = []
        for step in self.steps:
            if (step.step_id not in self.state.completed_steps and
                step.step_id not in self.state.failed_steps and
                step.step_id != self.state.current_step and
                step.can_execute(self.state.completed_steps)):
                next_steps.append(step)
        return next_steps
    
    def can_complete(self) -> bool:
        """检查是否可以完成"""
        required_steps = [s.step_id for s in self.steps if s.error_handling != "skip"]
        return all(step_id in self.state.completed_steps for step_id in required_steps)
    
    def start_execution(self):
        """开始执行"""
        self.state.start()
    
    def complete_execution(self):
        """完成执行"""
        self.state.complete()
        self._generate_execution_report()
    
    def fail_execution(self, error_message: str):
        """执行失败"""
        self.state.fail(error_message)
        self._generate_execution_report()
    
    def _generate_execution_report(self):
        """生成执行报告"""
        total_duration = self.state.get_duration()
        
        self.execution_report = {
            "执行摘要": self.state.get_state_summary(),
            "步骤详情": [step.to_dict() for step in self.steps],
            "任务执行": [exec.get_execution_summary() for exec in self.task_executions],
            "资源统计": self._get_resource_statistics(),
            "执行时间线": self._get_execution_timeline(),
            "错误分析": self._get_error_analysis()
        }
    
    def _get_resource_statistics(self) -> Dict[str, Any]:
        """获取资源统计"""
        total_tokens = sum(exec.tokens_used for exec in self.task_executions)
        total_api_calls = sum(exec.api_calls for exec in self.task_executions)
        avg_memory = sum(exec.memory_usage for exec in self.task_executions) / len(self.task_executions) if self.task_executions else 0
        
        return {
            "总Token消耗": total_tokens,
            "总API调用": total_api_calls,
            "平均内存使用": f"{avg_memory:.1f}MB",
            "任务执行数": len(self.task_executions)
        }
    
    def _get_execution_timeline(self) -> List[Dict[str, Any]]:
        """获取执行时间线"""
        timeline = []
        
        # 添加工作流事件
        if self.state.started_at:
            timeline.append({
                "时间": self.state.started_at.isoformat(),
                "事件": "工作流开始",
                "详情": f"开始执行工作流: {self.name}"
            })
        
        # 添加任务执行事件
        for exec in sorted(self.task_executions, key=lambda x: x.created_at):
            if exec.started_at:
                timeline.append({
                    "时间": exec.started_at.isoformat(),
                    "事件": "任务开始",
                    "详情": f"开始执行任务: {exec.task_id}"
                })
            if exec.completed_at:
                timeline.append({
                    "时间": exec.completed_at.isoformat(),
                    "事件": "任务完成" if exec.status == WorkflowStatus.COMPLETED else "任务失败",
                    "详情": f"任务 {exec.task_id} 状态: {exec.status.value}"
                })
        
        if self.state.completed_at:
            timeline.append({
                "时间": self.state.completed_at.isoformat(),
                "事件": "工作流完成",
                "详情": f"工作流执行完成，状态: {self.state.status.value}"
            })
        
        return timeline
    
    def _get_error_analysis(self) -> Dict[str, Any]:
        """获取错误分析"""
        failed_executions = [exec for exec in self.task_executions if exec.status == WorkflowStatus.FAILED]
        
        error_types = {}
        for exec in failed_executions:
            error_type = exec.error_type or "未知错误"
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "失败任务数": len(failed_executions),
            "错误类型统计": error_types,
            "错误详情": [
                {
                    "任务ID": exec.task_id,
                    "错误类型": exec.error_type,
                    "错误消息": exec.error_message,
                    "尝试次数": exec.attempt
                }
                for exec in failed_executions
            ]
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "执行ID": self.execution_id,
            "工作流ID": self.workflow_id,
            "名称": self.name,
            "状态": self.state.status.value,
            "进度": f"{self.state.progress:.1%}",
            "步骤数": len(self.steps),
            "已完成步骤": len(self.state.completed_steps),
            "失败步骤": len(self.state.failed_steps),
            "任务执行数": len(self.task_executions),
            "触发类型": self.trigger_type,
            "执行时长": self.state.get_duration(),
            "创建时间": self.state.created_at.isoformat()
        }
