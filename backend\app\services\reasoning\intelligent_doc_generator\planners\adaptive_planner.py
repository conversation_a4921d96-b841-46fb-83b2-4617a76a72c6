"""
自适应文档规划器

根据项目特征和上下文自动调整文档规划策略，提供智能化的文档结构规划。
"""

from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, timezone

from .base_planner import BasePlanner, PlannerError
from ..models.document_models import DocumentPlan, DocumentSection, ProjectContext

logger = logging.getLogger(__name__)


class AdaptivePlanner(BasePlanner):
    """自适应文档规划器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化自适应规划器
        
        Args:
            config: 规划器配置
        """
        super().__init__(config)
        self.adaptation_rules = self._load_adaptation_rules()
        
    async def create_plan(
        self,
        project_context: ProjectContext,
        requirements: Optional[Dict[str, Any]] = None
    ) -> DocumentPlan:
        """
        创建自适应文档规划
        
        Args:
            project_context: 项目上下文
            requirements: 规划要求
            
        Returns:
            DocumentPlan: 文档规划
            
        Raises:
            PlannerError: 规划失败时抛出异常
        """
        try:
            self.logger.info(f"开始创建自适应文档规划，项目类型: {project_context.project_type}")
            
            # 分析项目特征
            project_analysis = await self._analyze_project_characteristics(project_context)
            
            # 合并用户要求和分析结果
            merged_requirements = self._merge_requirements(
                self._analyze_project_requirements(project_context),
                project_analysis,
                requirements or {}
            )
            
            # 生成基础章节
            base_sections = self._create_base_sections()
            
            # 应用自适应规则
            adapted_sections = await self._apply_adaptation_rules(
                base_sections, 
                project_context,
                merged_requirements
            )
            
            # 优化章节顺序
            optimized_sections = self._optimize_section_order(adapted_sections)
            
            # 创建文档规划
            plan = DocumentPlan(
                plan_id=f"adaptive_plan_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
                title=f"{project_context.project_name} 文档规划",
                description="基于项目特征的自适应文档规划",
                sections=optimized_sections,
                metadata={
                    'planner_type': 'adaptive',
                    'project_type': project_context.project_type,
                    'adaptation_applied': True,
                    'requirements': merged_requirements,
                    'analysis_results': project_analysis
                }
            )
            
            # 验证规划
            if not self.validate_plan(plan):
                raise PlannerError("生成的文档规划验证失败")
                
            self.logger.info(
                f"自适应文档规划创建成功，包含 {len(optimized_sections)} 个章节"
            )
            
            return plan
            
        except Exception as e:
            self.logger.error(f"创建自适应文档规划失败: {str(e)}")
            raise PlannerError(f"自适应规划创建失败: {str(e)}")
    
    def validate_plan(self, plan: DocumentPlan) -> bool:
        """
        验证文档规划
        
        Args:
            plan: 待验证的文档规划
            
        Returns:
            bool: 验证结果
        """
        try:
            # 基础验证
            if not plan.sections:
                self.logger.warning("文档规划中没有章节")
                return False
                
            # 检查必需章节
            required_sections = ['introduction', 'installation', 'usage']
            existing_section_ids = {section.section_id for section in plan.sections}
            
            for required_id in required_sections:
                if required_id not in existing_section_ids:
                    self.logger.warning(f"缺少必需章节: {required_id}")
                    return False
            
            # 检查章节顺序
            orders = [section.order for section in plan.sections]
            if orders != sorted(orders):
                self.logger.warning("章节顺序不正确")
                return False
            
            # 检查章节唯一性
            section_ids = [section.section_id for section in plan.sections]
            if len(section_ids) != len(set(section_ids)):
                self.logger.warning("存在重复的章节ID")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证文档规划时出错: {str(e)}")
            return False
    
    async def _analyze_project_characteristics(
        self,
        project_context: ProjectContext
    ) -> Dict[str, Any]:
        """
        分析项目特征
        
        Args:
            project_context: 项目上下文
            
        Returns:
            Dict[str, Any]: 项目特征分析结果
        """
        analysis = {
            'complexity_score': 0,
            'api_complexity': 'none',
            'user_facing': False,
            'enterprise_grade': False,
            'documentation_priority': 'medium',
            'target_audience_analysis': [],
            'special_requirements': []
        }
        
        # 分析复杂度
        complexity_indicators = 0
        
        # 基于项目类型评估复杂度
        type_complexity = {
            'library': 3,
            'framework': 4,
            'web_app': 3,
            'api_service': 3,
            'cli_tool': 2,
            'desktop_app': 3,
            'mobile_app': 3,
            'script': 1,
            'other': 2
        }
        complexity_indicators += type_complexity.get(project_context.project_type, 2)
        
        # 基于技术栈评估复杂度
        if project_context.tech_stack:
            tech_stack_str = str(project_context.tech_stack).lower()
            
            # 多技术栈增加复杂度
            tech_keywords = ['api', 'database', 'redis', 'mongodb', 'postgresql', 
                           'docker', 'kubernetes', 'microservice']
            tech_count = sum(1 for keyword in tech_keywords if keyword in tech_stack_str)
            complexity_indicators += min(tech_count, 3)
            
            # API复杂度分析
            if any(api_tech in tech_stack_str for api_tech in ['rest', 'graphql', 'grpc']):
                analysis['api_complexity'] = 'high'
            elif 'api' in tech_stack_str:
                analysis['api_complexity'] = 'medium'
                
            # 企业级特征
            if any(enterprise_tech in tech_stack_str 
                   for enterprise_tech in ['kubernetes', 'docker', 'microservice', 'auth']):
                analysis['enterprise_grade'] = True
                
        # 用户面向性分析
        user_facing_types = ['web_app', 'mobile_app', 'desktop_app', 'cli_tool']
        if project_context.project_type in user_facing_types:
            analysis['user_facing'] = True
            
        # 设置复杂度分数
        analysis['complexity_score'] = min(complexity_indicators, 10)
        
        # 确定文档优先级
        if analysis['complexity_score'] >= 7:
            analysis['documentation_priority'] = 'high'
        elif analysis['complexity_score'] <= 3:
            analysis['documentation_priority'] = 'low'
        else:
            analysis['documentation_priority'] = 'medium'
            
        # 目标受众分析
        if project_context.project_type in ['library', 'framework']:
            analysis['target_audience_analysis'] = ['developers', 'maintainers']
        elif project_context.project_type in ['web_app', 'mobile_app', 'desktop_app']:
            analysis['target_audience_analysis'] = ['end_users', 'developers']
        elif project_context.project_type == 'cli_tool':
            analysis['target_audience_analysis'] = ['users', 'system_admins']
        else:
            analysis['target_audience_analysis'] = ['developers']
            
        # 特殊需求识别
        if analysis['api_complexity'] != 'none':
            analysis['special_requirements'].append('detailed_api_docs')
            
        if analysis['enterprise_grade']:
            analysis['special_requirements'].append('security_docs')
            analysis['special_requirements'].append('deployment_guide')
            
        if analysis['user_facing']:
            analysis['special_requirements'].append('user_guide')
            analysis['special_requirements'].append('troubleshooting')
            
        return analysis
    
    def _merge_requirements(
        self,
        base_requirements: Dict[str, Any],
        analysis_results: Dict[str, Any],
        user_requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        合并不同来源的需求
        
        Args:
            base_requirements: 基础需求
            analysis_results: 分析结果
            user_requirements: 用户要求
            
        Returns:
            Dict[str, Any]: 合并后的需求
        """
        merged = base_requirements.copy()
        
        # 从分析结果中更新需求
        if analysis_results.get('api_complexity') != 'none':
            merged['has_api'] = True
            merged['api_detail_level'] = analysis_results.get('api_complexity', 'medium')
            
        if analysis_results.get('enterprise_grade'):
            merged['has_config'] = True
            merged['has_security_docs'] = True
            merged['has_deployment_guide'] = True
            
        if analysis_results.get('user_facing'):
            merged['has_user_guide'] = True
            merged['has_troubleshooting'] = True
            
        # 设置复杂度级别
        complexity_score = analysis_results.get('complexity_score', 5)
        if complexity_score >= 7:
            merged['complexity_level'] = 'high'
        elif complexity_score >= 5:
            merged['complexity_level'] = 'medium'
        else:
            merged['complexity_level'] = 'low'
            
        # 设置目标受众
        merged['target_audience'] = analysis_results.get('target_audience_analysis', ['developers'])
        
        # 用户要求具有最高优先级
        merged.update(user_requirements)
        
        return merged
    
    async def _apply_adaptation_rules(
        self,
        sections: List[DocumentSection],
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> List[DocumentSection]:
        """
        应用自适应规则
        
        Args:
            sections: 原始章节列表
            project_context: 项目上下文
            requirements: 合并后的需求
            
        Returns:
            List[DocumentSection]: 调整后的章节列表
        """
        adapted_sections = []
        
        for section in sections:
            # 应用章节级别的适应规则
            adapted_section = await self._adapt_section(section, project_context, requirements)
            if adapted_section:
                adapted_sections.append(adapted_section)
        
        # 添加动态章节
        dynamic_sections = await self._generate_dynamic_sections(project_context, requirements)
        adapted_sections.extend(dynamic_sections)
        
        # 过滤不需要的章节
        final_sections = self._filter_sections_by_requirements(adapted_sections, requirements)
        
        return final_sections
    
    async def _adapt_section(
        self,
        section: DocumentSection,
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> Optional[DocumentSection]:
        """
        适应单个章节
        
        Args:
            section: 原始章节
            project_context: 项目上下文
            requirements: 需求
            
        Returns:
            Optional[DocumentSection]: 适应后的章节，如果不需要则返回None
        """
        # 复制章节以避免修改原始对象
        adapted_section = DocumentSection(
            section_id=section.section_id,
            title=section.title,
            order=section.order,
            is_required=section.is_required,
            template_name=section.template_name,
            description=section.description,
            dependencies=section.dependencies.copy() if section.dependencies else None,
            metadata=section.metadata.copy() if section.metadata else {}
        )
        
        # 根据项目类型调整章节
        if section.section_id == "api_reference":
            if requirements.get('has_api'):
                # 调整API文档详细程度
                api_level = requirements.get('api_detail_level', 'medium')
                adapted_section.metadata['detail_level'] = api_level
                if api_level == 'high':
                    adapted_section.title = "详细API参考"
                    adapted_section.description = "详细的API接口说明和示例"
            else:
                return None  # 不需要API文档
                
        elif section.section_id == "configuration":
            if requirements.get('has_config'):
                if requirements.get('complexity_level') == 'high':
                    adapted_section.title = "详细配置说明"
                    adapted_section.description = "详细的配置选项和部署配置"
            else:
                return None
                
        elif section.section_id == "development":
            if requirements.get('complexity_level') in ['high', 'medium']:
                if 'developers' in requirements.get('target_audience', []):
                    adapted_section.is_required = True
            else:
                return None
                
        return adapted_section
    
    async def _generate_dynamic_sections(
        self,
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> List[DocumentSection]:
        """
        生成动态章节
        
        Args:
            project_context: 项目上下文
            requirements: 需求
            
        Returns:
            List[DocumentSection]: 动态章节列表
        """
        dynamic_sections = []
        
        # 用户指南章节
        if requirements.get('has_user_guide'):
            dynamic_sections.append(
                DocumentSection(
                    section_id="user_guide",
                    title="用户指南",
                    order=3.5,  # 在usage之后
                    is_required=False,
                    template_name="section_user_guide.md",
                    description="面向最终用户的使用指南"
                )
            )
        
        # 故障排除章节
        if requirements.get('has_troubleshooting'):
            dynamic_sections.append(
                DocumentSection(
                    section_id="troubleshooting",
                    title="故障排除",
                    order=7.5,  # 在contributing之前
                    is_required=False,
                    template_name="section_troubleshooting.md",
                    description="常见问题和解决方案"
                )
            )
        
        # 安全文档章节
        if requirements.get('has_security_docs'):
            dynamic_sections.append(
                DocumentSection(
                    section_id="security",
                    title="安全说明",
                    order=5.5,  # 在configuration之后
                    is_required=False,
                    template_name="section_security.md",
                    description="安全配置和最佳实践"
                )
            )
        
        # 部署指南章节
        if requirements.get('has_deployment_guide'):
            dynamic_sections.append(
                DocumentSection(
                    section_id="deployment",
                    title="部署指南",
                    order=6.5,  # 在development之后
                    is_required=False,
                    template_name="section_deployment.md",
                    description="生产环境部署指南"
                )
            )
        
        return dynamic_sections
    
    def _load_adaptation_rules(self) -> Dict[str, Any]:
        """
        加载适应规则
        
        Returns:
            Dict[str, Any]: 适应规则配置
        """
        return {
            'project_type_rules': {
                'library': {
                    'required_sections': ['api_reference'],
                    'optional_sections': ['development', 'contributing'],
                    'documentation_depth': 'detailed'
                },
                'web_app': {
                    'required_sections': ['configuration'],
                    'optional_sections': ['deployment', 'security'],
                    'documentation_depth': 'standard'
                },
                'cli_tool': {
                    'required_sections': ['usage'],
                    'optional_sections': ['troubleshooting'],
                    'documentation_depth': 'user_friendly'
                }
            },
            'complexity_rules': {
                'high': {
                    'add_sections': ['development', 'troubleshooting'],
                    'detail_level': 'comprehensive'
                },
                'medium': {
                    'add_sections': ['configuration'],
                    'detail_level': 'standard'
                },
                'low': {
                    'add_sections': [],
                    'detail_level': 'basic'
                }
            }
        }
