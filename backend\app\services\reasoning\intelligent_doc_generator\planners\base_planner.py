"""
基础规划器抽象类

定义了所有文档规划器的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, timezone

from ..models.document_models import DocumentPlan, DocumentSection, ProjectContext

logger = logging.getLogger(__name__)


class PlannerError(Exception):
    """规划器异常"""
    pass


class BasePlanner(ABC):
    """基础文档规划器抽象类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化规划器
        
        Args:
            config: 规划器配置
        """
        self.config = config or {}
        self.logger = logger
        
    @abstractmethod
    async def create_plan(
        self,
        project_context: ProjectContext,
        requirements: Optional[Dict[str, Any]] = None
    ) -> DocumentPlan:
        """
        创建文档规划
        
        Args:
            project_context: 项目上下文
            requirements: 规划要求
            
        Returns:
            DocumentPlan: 文档规划
            
        Raises:
            PlannerError: 规划失败时抛出异常
        """
        pass
    
    @abstractmethod
    def validate_plan(self, plan: DocumentPlan) -> bool:
        """
        验证文档规划
        
        Args:
            plan: 待验证的文档规划
            
        Returns:
            bool: 验证结果
        """
        pass
    
    def _create_base_sections(self) -> List[DocumentSection]:
        """
        创建基础文档章节
        
        Returns:
            List[DocumentSection]: 基础章节列表
        """
        base_sections = [
            DocumentSection(
                section_id="introduction",
                title="项目介绍",
                order=1,
                is_required=True,
                template_name="section_introduction.md",
                description="项目基本信息和概述"
            ),
            DocumentSection(
                section_id="installation",
                title="安装指南",
                order=2,
                is_required=True,
                template_name="section_installation.md",
                description="项目安装和配置说明"
            ),
            DocumentSection(
                section_id="usage",
                title="使用说明",
                order=3,
                is_required=True,
                template_name="section_usage.md",
                description="项目使用方法和示例"
            ),
            DocumentSection(
                section_id="api_reference",
                title="API参考",
                order=4,
                is_required=False,
                template_name="section_api_reference.md",
                description="API接口详细说明"
            ),
            DocumentSection(
                section_id="configuration",
                title="配置说明",
                order=5,
                is_required=False,
                template_name="section_configuration.md",
                description="项目配置选项说明"
            ),
            DocumentSection(
                section_id="development",
                title="开发指南",
                order=6,
                is_required=False,
                template_name="section_development.md",
                description="开发环境搭建和开发规范"
            ),
            DocumentSection(
                section_id="contributing",
                title="贡献指南",
                order=7,
                is_required=False,
                template_name="section_contributing.md",
                description="项目贡献方式和规范"
            ),
            DocumentSection(
                section_id="changelog",
                title="更新日志",
                order=8,
                is_required=False,
                template_name="section_changelog.md",
                description="项目版本变更记录"
            ),
            DocumentSection(
                section_id="license",
                title="许可证",
                order=9,
                is_required=False,
                template_name="section_license.md",
                description="项目许可证信息"
            )
        ]
        
        return base_sections
    
    def _analyze_project_requirements(
        self,
        project_context: ProjectContext
    ) -> Dict[str, Any]:
        """
        分析项目需求
        
        Args:
            project_context: 项目上下文
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        requirements = {
            'has_api': False,
            'has_config': False,
            'has_tests': False,
            'has_cli': False,
            'has_web_interface': False,
            'complexity_level': 'medium',
            'target_audience': ['developers'],
            'documentation_depth': 'standard'
        }
        
        # 基于项目类型推断需求
        if project_context.project_type in ['library', 'framework']:
            requirements['has_api'] = True
            requirements['documentation_depth'] = 'detailed'
            
        elif project_context.project_type in ['web_app', 'api_service']:
            requirements['has_api'] = True
            requirements['has_config'] = True
            requirements['has_web_interface'] = True
            
        elif project_context.project_type == 'cli_tool':
            requirements['has_cli'] = True
            requirements['target_audience'] = ['users', 'developers']
            
        # 基于技术栈推断需求
        tech_stack = project_context.tech_stack
        if tech_stack and 'api' in str(tech_stack).lower():
            requirements['has_api'] = True
            
        if tech_stack and any(web_tech in str(tech_stack).lower() 
                             for web_tech in ['flask', 'django', 'fastapi', 'express']):
            requirements['has_web_interface'] = True
            requirements['has_config'] = True
            
        return requirements
    
    def _filter_sections_by_requirements(
        self,
        sections: List[DocumentSection],
        requirements: Dict[str, Any]
    ) -> List[DocumentSection]:
        """
        根据需求过滤章节
        
        Args:
            sections: 所有章节
            requirements: 项目需求
            
        Returns:
            List[DocumentSection]: 过滤后的章节
        """
        filtered_sections = []
        
        for section in sections:
            # 必需章节始终保留
            if section.is_required:
                filtered_sections.append(section)
                continue
                
            # 根据需求决定是否包含可选章节
            section_id = section.section_id
            
            if section_id == "api_reference" and requirements.get('has_api', False):
                filtered_sections.append(section)
            elif section_id == "configuration" and requirements.get('has_config', False):
                filtered_sections.append(section)
            elif section_id == "development" and requirements.get('complexity_level') in ['high', 'complex']:
                filtered_sections.append(section)
            elif section_id in ["contributing", "license"] and requirements.get('target_audience'):
                if 'developers' in requirements['target_audience']:
                    filtered_sections.append(section)
                    
        return filtered_sections
    
    def _optimize_section_order(self, sections: List[DocumentSection]) -> List[DocumentSection]:
        """
        优化章节顺序
        
        Args:
            sections: 章节列表
            
        Returns:
            List[DocumentSection]: 优化后的章节列表
        """
        # 按order字段排序
        sorted_sections = sorted(sections, key=lambda x: x.order)
        
        # 重新分配order以确保连续性
        for i, section in enumerate(sorted_sections, 1):
            section.order = i
            
        return sorted_sections
    
    def get_planner_info(self) -> Dict[str, Any]:
        """
        获取规划器信息
        
        Returns:
            Dict[str, Any]: 规划器信息
        """
        return {
            'name': self.__class__.__name__,
            'version': '1.0.0',
            'description': self.__doc__ or '',
            'config': self.config,
            'created_at': datetime.now(timezone.utc).isoformat()
        }
