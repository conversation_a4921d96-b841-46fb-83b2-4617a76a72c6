"""
领域特定文档规划器

针对特定领域和项目类型提供专门优化的文档规划策略。
"""

from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, timezone

from .base_planner import BasePlanner, PlannerError
from ..models.document_models import DocumentPlan, DocumentSection, ProjectContext

logger = logging.getLogger(__name__)


class DomainSpecificPlanner(BasePlanner):
    """领域特定文档规划器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化领域特定规划器
        
        Args:
            config: 规划器配置
        """
        super().__init__(config)
        self.domain_templates = self._load_domain_templates()
        
    async def create_plan(
        self,
        project_context: ProjectContext,
        requirements: Optional[Dict[str, Any]] = None
    ) -> DocumentPlan:
        """
        创建领域特定文档规划
        
        Args:
            project_context: 项目上下文
            requirements: 规划要求
            
        Returns:
            DocumentPlan: 文档规划
            
        Raises:
            PlannerError: 规划失败时抛出异常
        """
        try:
            self.logger.info(
                f"开始创建领域特定文档规划，项目类型: {project_context.project_type}"
            )
            
            # 确定领域类型
            domain_type = self._identify_domain(project_context)
            
            # 获取领域模板
            domain_template = self.domain_templates.get(domain_type)
            if not domain_template:
                self.logger.warning(f"未找到领域 {domain_type} 的模板，使用通用规划")
                domain_template = self.domain_templates['generic']
            
            # 创建领域特定章节
            sections = await self._create_domain_sections(
                domain_template, 
                project_context,
                requirements or {}
            )
            
            # 优化章节顺序
            optimized_sections = self._optimize_section_order(sections)
            
            # 创建文档规划
            plan = DocumentPlan(
                plan_id=f"domain_plan_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
                title=f"{project_context.project_name} 文档规划",
                description=f"针对 {domain_type} 领域的专门文档规划",
                sections=optimized_sections,
                metadata={
                    'planner_type': 'domain_specific',
                    'domain_type': domain_type,
                    'project_type': project_context.project_type,
                    'template_used': domain_template.get('name', domain_type)
                }
            )
            
            # 验证规划
            if not self.validate_plan(plan):
                raise PlannerError("生成的文档规划验证失败")
                
            self.logger.info(
                f"领域特定文档规划创建成功，领域: {domain_type}，"
                f"包含 {len(optimized_sections)} 个章节"
            )
            
            return plan
            
        except Exception as e:
            self.logger.error(f"创建领域特定文档规划失败: {str(e)}")
            raise PlannerError(f"领域特定规划创建失败: {str(e)}")
    
    def validate_plan(self, plan: DocumentPlan) -> bool:
        """
        验证文档规划
        
        Args:
            plan: 待验证的文档规划
            
        Returns:
            bool: 验证结果
        """
        try:
            # 基础验证
            if not plan.sections:
                self.logger.warning("文档规划中没有章节")
                return False
            
            # 获取领域类型
            domain_type = plan.metadata.get('domain_type', 'generic')
            domain_template = self.domain_templates.get(domain_type, self.domain_templates['generic'])
            
            # 检查领域必需章节
            required_sections = domain_template.get('required_sections', [])
            existing_section_ids = {section.section_id for section in plan.sections}
            
            for required_id in required_sections:
                if required_id not in existing_section_ids:
                    self.logger.warning(f"缺少领域必需章节: {required_id}")
                    return False
            
            # 检查章节顺序
            orders = [section.order for section in plan.sections]
            if orders != sorted(orders):
                self.logger.warning("章节顺序不正确")
                return False
            
            # 检查章节唯一性
            section_ids = [section.section_id for section in plan.sections]
            if len(section_ids) != len(set(section_ids)):
                self.logger.warning("存在重复的章节ID")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证文档规划时出错: {str(e)}")
            return False
    
    def _identify_domain(self, project_context: ProjectContext) -> str:
        """
        识别项目领域
        
        Args:
            project_context: 项目上下文
            
        Returns:
            str: 领域类型
        """
        project_type = project_context.project_type
        tech_stack = str(project_context.tech_stack).lower() if project_context.tech_stack else ""
        
        # 基于项目类型的领域识别
        if project_type == 'library':
            return 'library'
        elif project_type == 'framework':
            return 'framework'
        elif project_type in ['web_app', 'api_service']:
            # 进一步细分Web应用类型
            if any(api_tech in tech_stack for api_tech in ['rest', 'graphql', 'api']):
                return 'api_service'
            else:
                return 'web_application'
        elif project_type == 'cli_tool':
            return 'cli_tool'
        elif project_type in ['desktop_app', 'mobile_app']:
            return 'gui_application'
        elif project_type == 'script':
            return 'automation_script'
        else:
            return 'generic'
    
    async def _create_domain_sections(
        self,
        domain_template: Dict[str, Any],
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> List[DocumentSection]:
        """
        创建领域特定章节
        
        Args:
            domain_template: 领域模板
            project_context: 项目上下文
            requirements: 用户要求
            
        Returns:
            List[DocumentSection]: 章节列表
        """
        sections = []
        
        # 获取模板章节定义
        template_sections = domain_template.get('sections', [])
        
        for section_def in template_sections:
            # 检查章节是否应该包含
            if not self._should_include_section(section_def, project_context, requirements):
                continue
            
            # 创建章节
            section = DocumentSection(
                section_id=section_def['id'],
                title=section_def['title'],
                order=section_def.get('order', 999),
                is_required=section_def.get('required', False),
                template_name=section_def.get('template', f"section_{section_def['id']}.md"),
                description=section_def.get('description', ''),
                dependencies=section_def.get('dependencies'),
                metadata=section_def.get('metadata', {})
            )
            
            sections.append(section)
        
        return sections
    
    def _should_include_section(
        self,
        section_def: Dict[str, Any],
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> bool:
        """
        判断是否应该包含某个章节
        
        Args:
            section_def: 章节定义
            project_context: 项目上下文
            requirements: 用户要求
            
        Returns:
            bool: 是否包含
        """
        # 必需章节始终包含
        if section_def.get('required', False):
            return True
        
        # 检查条件
        conditions = section_def.get('conditions', {})
        
        # 检查项目类型条件
        if 'project_types' in conditions:
            if project_context.project_type not in conditions['project_types']:
                return False
        
        # 检查技术栈条件
        if 'tech_stack_contains' in conditions:
            tech_stack = str(project_context.tech_stack).lower() if project_context.tech_stack else ""
            required_techs = conditions['tech_stack_contains']
            if not any(tech.lower() in tech_stack for tech in required_techs):
                return False
        
        # 检查用户要求
        if 'user_requirements' in conditions:
            required_flags = conditions['user_requirements']
            if not all(requirements.get(flag, False) for flag in required_flags):
                return False
        
        return True
    
    def _load_domain_templates(self) -> Dict[str, Dict[str, Any]]:
        """
        加载领域模板
        
        Returns:
            Dict[str, Dict[str, Any]]: 领域模板配置
        """
        return {
            'library': {
                'name': 'Python/JavaScript库',
                'description': '软件库和包的文档模板',
                'required_sections': ['introduction', 'installation', 'api_reference'],
                'sections': [
                    {
                        'id': 'introduction',
                        'title': '项目介绍',
                        'order': 1,
                        'required': True,
                        'template': 'section_introduction.md',
                        'description': '库的基本信息和概述'
                    },
                    {
                        'id': 'installation',
                        'title': '安装指南',
                        'order': 2,
                        'required': True,
                        'template': 'section_installation.md',
                        'description': '库的安装方法'
                    },
                    {
                        'id': 'quick_start',
                        'title': '快速开始',
                        'order': 3,
                        'required': False,
                        'template': 'section_quick_start.md',
                        'description': '快速上手示例'
                    },
                    {
                        'id': 'api_reference',
                        'title': 'API参考',
                        'order': 4,
                        'required': True,
                        'template': 'section_api_reference.md',
                        'description': '详细的API文档'
                    },
                    {
                        'id': 'examples',
                        'title': '使用示例',
                        'order': 5,
                        'required': False,
                        'template': 'section_examples.md',
                        'description': '实际使用案例'
                    },
                    {
                        'id': 'contributing',
                        'title': '贡献指南',
                        'order': 6,
                        'required': False,
                        'template': 'section_contributing.md',
                        'description': '如何为库做贡献'
                    },
                    {
                        'id': 'changelog',
                        'title': '更新日志',
                        'order': 7,
                        'required': False,
                        'template': 'section_changelog.md',
                        'description': '版本变更历史'
                    },
                    {
                        'id': 'license',
                        'title': '许可证',
                        'order': 8,
                        'required': False,
                        'template': 'section_license.md',
                        'description': '开源许可证信息'
                    }
                ]
            },
            'api_service': {
                'name': 'API服务',
                'description': 'RESTful API和Web服务的文档模板',
                'required_sections': ['introduction', 'installation', 'api_reference', 'authentication'],
                'sections': [
                    {
                        'id': 'introduction',
                        'title': '服务介绍',
                        'order': 1,
                        'required': True,
                        'template': 'section_introduction.md',
                        'description': 'API服务的基本信息'
                    },
                    {
                        'id': 'installation',
                        'title': '部署指南',
                        'order': 2,
                        'required': True,
                        'template': 'section_installation.md',
                        'description': '服务部署和配置'
                    },
                    {
                        'id': 'authentication',
                        'title': '身份验证',
                        'order': 3,
                        'required': True,
                        'template': 'section_authentication.md',
                        'description': 'API身份验证方式'
                    },
                    {
                        'id': 'api_reference',
                        'title': 'API参考',
                        'order': 4,
                        'required': True,
                        'template': 'section_api_reference.md',
                        'description': '详细的API端点文档'
                    },
                    {
                        'id': 'rate_limiting',
                        'title': '速率限制',
                        'order': 5,
                        'required': False,
                        'template': 'section_rate_limiting.md',
                        'description': 'API调用频率限制'
                    },
                    {
                        'id': 'error_handling',
                        'title': '错误处理',
                        'order': 6,
                        'required': False,
                        'template': 'section_error_handling.md',
                        'description': '错误码和处理方式'
                    },
                    {
                        'id': 'monitoring',
                        'title': '监控运维',
                        'order': 7,
                        'required': False,
                        'template': 'section_monitoring.md',
                        'description': '服务监控和运维指南'
                    }
                ]
            },
            'cli_tool': {
                'name': '命令行工具',
                'description': '命令行工具和脚本的文档模板',
                'required_sections': ['introduction', 'installation', 'usage'],
                'sections': [
                    {
                        'id': 'introduction',
                        'title': '工具介绍',
                        'order': 1,
                        'required': True,
                        'template': 'section_introduction.md',
                        'description': '命令行工具的功能介绍'
                    },
                    {
                        'id': 'installation',
                        'title': '安装指南',
                        'order': 2,
                        'required': True,
                        'template': 'section_installation.md',
                        'description': '工具安装方法'
                    },
                    {
                        'id': 'usage',
                        'title': '使用说明',
                        'order': 3,
                        'required': True,
                        'template': 'section_usage.md',
                        'description': '命令使用方法和参数'
                    },
                    {
                        'id': 'examples',
                        'title': '使用示例',
                        'order': 4,
                        'required': False,
                        'template': 'section_examples.md',
                        'description': '实际使用案例'
                    },
                    {
                        'id': 'configuration',
                        'title': '配置文件',
                        'order': 5,
                        'required': False,
                        'template': 'section_configuration.md',
                        'description': '配置文件说明'
                    },
                    {
                        'id': 'troubleshooting',
                        'title': '故障排除',
                        'order': 6,
                        'required': False,
                        'template': 'section_troubleshooting.md',
                        'description': '常见问题和解决方案'
                    }
                ]
            },
            'web_application': {
                'name': 'Web应用',
                'description': 'Web应用和网站的文档模板',
                'required_sections': ['introduction', 'installation', 'usage', 'configuration'],
                'sections': [
                    {
                        'id': 'introduction',
                        'title': '应用介绍',
                        'order': 1,
                        'required': True,
                        'template': 'section_introduction.md',
                        'description': 'Web应用的功能和特性'
                    },
                    {
                        'id': 'installation',
                        'title': '部署指南',
                        'order': 2,
                        'required': True,
                        'template': 'section_installation.md',
                        'description': '应用部署和环境配置'
                    },
                    {
                        'id': 'configuration',
                        'title': '配置说明',
                        'order': 3,
                        'required': True,
                        'template': 'section_configuration.md',
                        'description': '应用配置选项'
                    },
                    {
                        'id': 'usage',
                        'title': '使用指南',
                        'order': 4,
                        'required': True,
                        'template': 'section_usage.md',
                        'description': '用户操作指南'
                    },
                    {
                        'id': 'development',
                        'title': '开发指南',
                        'order': 5,
                        'required': False,
                        'template': 'section_development.md',
                        'description': '本地开发环境搭建'
                    },
                    {
                        'id': 'security',
                        'title': '安全配置',
                        'order': 6,
                        'required': False,
                        'template': 'section_security.md',
                        'description': '安全设置和最佳实践'
                    },
                    {
                        'id': 'backup',
                        'title': '备份恢复',
                        'order': 7,
                        'required': False,
                        'template': 'section_backup.md',
                        'description': '数据备份和恢复'
                    }
                ]
            },
            'generic': {
                'name': '通用项目',
                'description': '通用项目的文档模板',
                'required_sections': ['introduction', 'installation', 'usage'],
                'sections': [
                    {
                        'id': 'introduction',
                        'title': '项目介绍',
                        'order': 1,
                        'required': True,
                        'template': 'section_introduction.md'
                    },
                    {
                        'id': 'installation',
                        'title': '安装指南',
                        'order': 2,
                        'required': True,
                        'template': 'section_installation.md'
                    },
                    {
                        'id': 'usage',
                        'title': '使用说明',
                        'order': 3,
                        'required': True,
                        'template': 'section_usage.md'
                    },
                    {
                        'id': 'configuration',
                        'title': '配置说明',
                        'order': 4,
                        'required': False,
                        'template': 'section_configuration.md'
                    },
                    {
                        'id': 'contributing',
                        'title': '贡献指南',
                        'order': 5,
                        'required': False,
                        'template': 'section_contributing.md'
                    },
                    {
                        'id': 'license',
                        'title': '许可证',
                        'order': 6,
                        'required': False,
                        'template': 'section_license.md'
                    }
                ]
            }
        }
