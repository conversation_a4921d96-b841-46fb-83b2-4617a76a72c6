"""
模板驱动文档规划器

基于预定义模板和配置文件创建文档规划，支持模板继承和自定义扩展。
"""

from typing import List, Dict, Any, Optional
import logging
import json
from pathlib import Path
from datetime import datetime, timezone

from .base_planner import BasePlanner, PlannerError
from ..models.document_models import DocumentPlan, DocumentSection, ProjectContext

logger = logging.getLogger(__name__)


class TemplatePlanner(BasePlanner):
    """模板驱动文档规划器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化模板规划器
        
        Args:
            config: 规划器配置
        """
        super().__init__(config)
        self.template_path = config.get('template_path') if config else None
        self.custom_templates = {}
        self._load_custom_templates()
        
    async def create_plan(
        self,
        project_context: ProjectContext,
        requirements: Optional[Dict[str, Any]] = None
    ) -> DocumentPlan:
        """
        创建基于模板的文档规划
        
        Args:
            project_context: 项目上下文
            requirements: 规划要求
            
        Returns:
            DocumentPlan: 文档规划
            
        Raises:
            PlannerError: 规划失败时抛出异常
        """
        try:
            self.logger.info(f"开始创建模板驱动文档规划，项目类型: {project_context.project_type}")
            
            # 选择合适的模板
            template_name = self._select_template(project_context, requirements or {})
            template_config = await self._load_template_config(template_name)
            
            # 处理模板继承
            resolved_template = await self._resolve_template_inheritance(template_config)
            
            # 创建基于模板的章节
            sections = await self._create_template_sections(
                resolved_template,
                project_context,
                requirements or {}
            )
            
            # 应用用户自定义
            customized_sections = await self._apply_customizations(
                sections,
                project_context,
                requirements or {}
            )
            
            # 优化章节顺序
            optimized_sections = self._optimize_section_order(customized_sections)
            
            # 创建文档规划
            plan = DocumentPlan(
                plan_id=f"template_plan_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
                title=f"{project_context.project_name} 文档规划",
                description=f"基于 {template_name} 模板的文档规划",
                sections=optimized_sections,
                metadata={
                    'planner_type': 'template_driven',
                    'template_name': template_name,
                    'template_version': resolved_template.get('version', '1.0.0'),
                    'project_type': project_context.project_type,
                    'customizations_applied': bool(requirements)
                }
            )
            
            # 验证规划
            if not self.validate_plan(plan):
                raise PlannerError("生成的文档规划验证失败")
                
            self.logger.info(
                f"模板驱动文档规划创建成功，模板: {template_name}，"
                f"包含 {len(optimized_sections)} 个章节"
            )
            
            return plan
            
        except Exception as e:
            self.logger.error(f"创建模板驱动文档规划失败: {str(e)}")
            raise PlannerError(f"模板驱动规划创建失败: {str(e)}")
    
    def validate_plan(self, plan: DocumentPlan) -> bool:
        """
        验证文档规划
        
        Args:
            plan: 待验证的文档规划
            
        Returns:
            bool: 验证结果
        """
        try:
            # 基础验证
            if not plan.sections:
                self.logger.warning("文档规划中没有章节")
                return False
            
            # 获取模板配置
            template_name = plan.metadata.get('template_name')
            if template_name:
                try:
                    template_config = self._get_template_config_sync(template_name)
                    required_sections = template_config.get('required_sections', [])
                    
                    # 检查模板必需章节
                    existing_section_ids = {section.section_id for section in plan.sections}
                    for required_id in required_sections:
                        if required_id not in existing_section_ids:
                            self.logger.warning(f"缺少模板必需章节: {required_id}")
                            return False
                except Exception as e:
                    self.logger.warning(f"无法验证模板必需章节: {str(e)}")
            
            # 检查章节顺序
            orders = [section.order for section in plan.sections]
            if orders != sorted(orders):
                self.logger.warning("章节顺序不正确")
                return False
            
            # 检查章节唯一性
            section_ids = [section.section_id for section in plan.sections]
            if len(section_ids) != len(set(section_ids)):
                self.logger.warning("存在重复的章节ID")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"验证文档规划时出错: {str(e)}")
            return False
    
    def _select_template(
        self,
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> str:
        """
        选择合适的模板
        
        Args:
            project_context: 项目上下文
            requirements: 用户要求
            
        Returns:
            str: 模板名称
        """
        # 用户指定模板优先
        if 'template_name' in requirements:
            return requirements['template_name']
        
        # 基于项目类型选择默认模板
        project_type = project_context.project_type
        
        template_mapping = {
            'library': 'library_standard',
            'framework': 'framework_comprehensive',
            'web_app': 'webapp_standard',
            'api_service': 'api_comprehensive',
            'cli_tool': 'cli_user_friendly',
            'desktop_app': 'gui_standard',
            'mobile_app': 'mobile_standard',
            'script': 'script_minimal',
            'other': 'generic_standard'
        }
        
        return template_mapping.get(project_type, 'generic_standard')
    
    async def _load_template_config(self, template_name: str) -> Dict[str, Any]:
        """
        加载模板配置
        
        Args:
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 模板配置
        """
        # 首先尝试从自定义模板中加载
        if template_name in self.custom_templates:
            return self.custom_templates[template_name]
        
        # 尝试从文件加载
        if self.template_path:
            template_file = Path(self.template_path) / f"{template_name}.json"
            if template_file.exists():
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception as e:
                    self.logger.warning(f"加载模板文件失败 {template_file}: {str(e)}")
        
        # 使用内置默认模板
        return self._get_builtin_template(template_name)
    
    def _get_template_config_sync(self, template_name: str) -> Dict[str, Any]:
        """
        同步方式获取模板配置（用于验证）
        
        Args:
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 模板配置
        """
        if template_name in self.custom_templates:
            return self.custom_templates[template_name]
        return self._get_builtin_template(template_name)
    
    async def _resolve_template_inheritance(
        self,
        template_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        解析模板继承
        
        Args:
            template_config: 原始模板配置
            
        Returns:
            Dict[str, Any]: 解析后的模板配置
        """
        # 如果没有继承，直接返回
        if 'inherits' not in template_config:
            return template_config
        
        # 加载父模板
        parent_name = template_config['inherits']
        parent_config = await self._load_template_config(parent_name)
        
        # 递归解析父模板的继承
        resolved_parent = await self._resolve_template_inheritance(parent_config)
        
        # 合并配置
        merged_config = self._merge_template_configs(resolved_parent, template_config)
        
        return merged_config
    
    def _merge_template_configs(
        self,
        parent_config: Dict[str, Any],
        child_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        合并模板配置
        
        Args:
            parent_config: 父模板配置
            child_config: 子模板配置
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        merged = parent_config.copy()
        
        # 合并基本属性
        for key, value in child_config.items():
            if key == 'inherits':
                continue  # 跳过继承声明
            elif key == 'sections':
                # 特殊处理章节合并
                merged['sections'] = self._merge_sections(
                    parent_config.get('sections', []),
                    value
                )
            elif isinstance(value, dict) and key in merged:
                # 递归合并字典
                merged[key] = {**merged[key], **value}
            elif isinstance(value, list) and key in merged:
                # 合并列表（去重）
                merged[key] = list(set(merged[key] + value))
            else:
                # 子配置覆盖父配置
                merged[key] = value
        
        return merged
    
    def _merge_sections(
        self,
        parent_sections: List[Dict[str, Any]],
        child_sections: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        合并章节配置
        
        Args:
            parent_sections: 父模板章节
            child_sections: 子模板章节
            
        Returns:
            List[Dict[str, Any]]: 合并后的章节
        """
        # 创建父章节的字典映射
        parent_dict = {section['id']: section for section in parent_sections}
        
        # 处理子章节
        merged_sections = []
        processed_ids = set()
        
        for child_section in child_sections:
            section_id = child_section['id']
            
            if section_id in parent_dict:
                # 合并已存在的章节
                merged_section = {**parent_dict[section_id], **child_section}
                merged_sections.append(merged_section)
            else:
                # 添加新章节
                merged_sections.append(child_section)
            
            processed_ids.add(section_id)
        
        # 添加未处理的父章节
        for parent_section in parent_sections:
            if parent_section['id'] not in processed_ids:
                merged_sections.append(parent_section)
        
        return merged_sections
    
    async def _create_template_sections(
        self,
        template_config: Dict[str, Any],
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> List[DocumentSection]:
        """
        创建基于模板的章节
        
        Args:
            template_config: 模板配置
            project_context: 项目上下文
            requirements: 用户要求
            
        Returns:
            List[DocumentSection]: 章节列表
        """
        sections = []
        template_sections = template_config.get('sections', [])
        
        for section_def in template_sections:
            # 检查章节条件
            if not self._check_section_conditions(section_def, project_context, requirements):
                continue
            
            # 创建章节
            section = DocumentSection(
                section_id=section_def['id'],
                title=section_def['title'],
                order=section_def.get('order', 999),
                is_required=section_def.get('required', False),
                template_name=section_def.get('template', f"section_{section_def['id']}.md"),
                description=section_def.get('description', ''),
                dependencies=section_def.get('dependencies'),
                metadata=section_def.get('metadata', {})
            )
            
            sections.append(section)
        
        return sections
    
    def _check_section_conditions(
        self,
        section_def: Dict[str, Any],
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> bool:
        """
        检查章节条件
        
        Args:
            section_def: 章节定义
            project_context: 项目上下文
            requirements: 用户要求
            
        Returns:
            bool: 是否满足条件
        """
        conditions = section_def.get('conditions', {})
        
        # 检查项目类型条件
        if 'project_types' in conditions:
            if project_context.project_type not in conditions['project_types']:
                return False
        
        # 检查技术栈条件
        if 'tech_stack' in conditions:
            tech_stack = str(project_context.tech_stack).lower() if project_context.tech_stack else ""
            required_techs = conditions['tech_stack']
            if not any(tech.lower() in tech_stack for tech in required_techs):
                return False
        
        # 检查用户要求条件
        if 'requirements' in conditions:
            required_flags = conditions['requirements']
            for flag in required_flags:
                if flag.startswith('!'):
                    # 否定条件
                    if requirements.get(flag[1:], False):
                        return False
                else:
                    # 肯定条件
                    if not requirements.get(flag, False):
                        return False
        
        return True
    
    async def _apply_customizations(
        self,
        sections: List[DocumentSection],
        project_context: ProjectContext,
        requirements: Dict[str, Any]
    ) -> List[DocumentSection]:
        """
        应用用户自定义
        
        Args:
            sections: 原始章节列表
            project_context: 项目上下文
            requirements: 用户要求
            
        Returns:
            List[DocumentSection]: 自定义后的章节列表
        """
        if 'custom_sections' not in requirements:
            return sections
        
        custom_sections = requirements['custom_sections']
        section_dict = {section.section_id: section for section in sections}
        
        # 处理自定义章节
        for custom_def in custom_sections:
            action = custom_def.get('action', 'add')
            section_id = custom_def['id']
            
            if action == 'add':
                # 添加新章节
                section = DocumentSection(
                    section_id=section_id,
                    title=custom_def['title'],
                    order=custom_def.get('order', 999),
                    is_required=custom_def.get('required', False),
                    template_name=custom_def.get('template', f"section_{section_id}.md"),
                    description=custom_def.get('description', ''),
                    metadata=custom_def.get('metadata', {})
                )
                section_dict[section_id] = section
                
            elif action == 'modify' and section_id in section_dict:
                # 修改现有章节
                section = section_dict[section_id]
                if 'title' in custom_def:
                    section.title = custom_def['title']
                if 'order' in custom_def:
                    section.order = custom_def['order']
                if 'template' in custom_def:
                    section.template_name = custom_def['template']
                if 'description' in custom_def:
                    section.description = custom_def['description']
                if 'metadata' in custom_def:
                    section.metadata.update(custom_def['metadata'])
                    
            elif action == 'remove' and section_id in section_dict:
                # 删除章节
                del section_dict[section_id]
        
        return list(section_dict.values())
    
    def _load_custom_templates(self):
        """加载自定义模板"""
        custom_template_path = self.config.get('custom_template_path') if self.config else None
        if not custom_template_path:
            return
        
        template_dir = Path(custom_template_path)
        if not template_dir.exists():
            return
        
        for template_file in template_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_config = json.load(f)
                    template_name = template_file.stem
                    self.custom_templates[template_name] = template_config
                    self.logger.info(f"已加载自定义模板: {template_name}")
            except Exception as e:
                self.logger.warning(f"加载自定义模板失败 {template_file}: {str(e)}")
    
    def _get_builtin_template(self, template_name: str) -> Dict[str, Any]:
        """
        获取内置模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 模板配置
        """
        builtin_templates = {
            'generic_standard': {
                'name': '通用标准模板',
                'version': '1.0.0',
                'description': '适用于大多数项目的标准文档模板',
                'required_sections': ['introduction', 'installation', 'usage'],
                'sections': [
                    {'id': 'introduction', 'title': '项目介绍', 'order': 1, 'required': True},
                    {'id': 'installation', 'title': '安装指南', 'order': 2, 'required': True},
                    {'id': 'usage', 'title': '使用说明', 'order': 3, 'required': True},
                    {'id': 'configuration', 'title': '配置说明', 'order': 4, 'required': False},
                    {'id': 'contributing', 'title': '贡献指南', 'order': 5, 'required': False},
                    {'id': 'license', 'title': '许可证', 'order': 6, 'required': False}
                ]
            },
            'library_standard': {
                'name': '库标准模板',
                'version': '1.0.0',
                'description': '软件库的标准文档模板',
                'required_sections': ['introduction', 'installation', 'api_reference'],
                'sections': [
                    {'id': 'introduction', 'title': '库介绍', 'order': 1, 'required': True},
                    {'id': 'installation', 'title': '安装指南', 'order': 2, 'required': True},
                    {'id': 'quick_start', 'title': '快速开始', 'order': 3, 'required': False},
                    {'id': 'api_reference', 'title': 'API参考', 'order': 4, 'required': True},
                    {'id': 'examples', 'title': '使用示例', 'order': 5, 'required': False},
                    {'id': 'contributing', 'title': '贡献指南', 'order': 6, 'required': False}
                ]
            },
            'api_comprehensive': {
                'name': 'API综合模板',
                'version': '1.0.0', 
                'description': 'API服务的综合文档模板',
                'required_sections': ['introduction', 'installation', 'api_reference', 'authentication'],
                'sections': [
                    {'id': 'introduction', 'title': '服务介绍', 'order': 1, 'required': True},
                    {'id': 'installation', 'title': '部署指南', 'order': 2, 'required': True},
                    {'id': 'authentication', 'title': '身份验证', 'order': 3, 'required': True},
                    {'id': 'api_reference', 'title': 'API参考', 'order': 4, 'required': True},
                    {'id': 'rate_limiting', 'title': '速率限制', 'order': 5, 'required': False},
                    {'id': 'error_handling', 'title': '错误处理', 'order': 6, 'required': False}
                ]
            },
            'cli_user_friendly': {
                'name': 'CLI用户友好模板',
                'version': '1.0.0',
                'description': '命令行工具的用户友好文档模板',
                'required_sections': ['introduction', 'installation', 'usage'],
                'sections': [
                    {'id': 'introduction', 'title': '工具介绍', 'order': 1, 'required': True},
                    {'id': 'installation', 'title': '安装指南', 'order': 2, 'required': True},
                    {'id': 'usage', 'title': '使用说明', 'order': 3, 'required': True},
                    {'id': 'examples', 'title': '使用示例', 'order': 4, 'required': False},
                    {'id': 'troubleshooting', 'title': '故障排除', 'order': 5, 'required': False}
                ]
            }
        }
        
        return builtin_templates.get(template_name, builtin_templates['generic_standard'])
