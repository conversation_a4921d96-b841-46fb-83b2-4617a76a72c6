"""
评估阶段AI提示词模板

提供文档质量评估和审查阶段使用的各种AI提示词模板
"""

from typing import Dict, Any
from enum import Enum


class EvaluationTemplateType(str, Enum):
    """评估模板类型"""
    QUALITY_ASSESSMENT = "quality_assessment"
    CONTENT_REVIEW = "content_review"
    TECHNICAL_ACCURACY = "technical_accuracy"
    USER_EXPERIENCE = "user_experience"
    COMPLETENESS_CHECK = "completeness_check"


class EvaluationTemplates:
    """评估阶段提示词模板集合"""
    
    @staticmethod
    def get_quality_assessment_template() -> str:
        """获取质量评估模板"""
        return """
        # 文档质量评估专家

        你是一个专业的文档质量评估专家，擅长从多个维度全面评估文档质量。

        ## 评估任务
        文档类型: {{ document_type }}
        评估标准: {{ evaluation_standards }}
        目标受众: {{ target_audience }}
        质量要求: {{ quality_requirements }}

        ## 待评估内容
        ```
        {{ content_to_evaluate }}
        ```

        ## 评估维度
        请从以下维度进行评估（0-10分）：

        ### 1. 内容质量 (40%)
        - **准确性**: 信息是否准确无误
        - **完整性**: 是否涵盖所有必要信息
        - **相关性**: 内容是否与主题相关
        - **深度**: 信息深度是否适当
        - **时效性**: 信息是否最新有效

        ### 2. 结构组织 (25%)
        - **逻辑性**: 内容组织是否逻辑清晰
        - **层次性**: 标题层级是否合理
        - **流畅性**: 内容过渡是否自然
        - **导航性**: 是否易于查找信息
        - **一致性**: 结构风格是否统一

        ### 3. 语言表达 (20%)
        - **清晰度**: 表达是否清晰易懂
        - **准确性**: 术语使用是否准确
        - **风格**: 语言风格是否适合受众
        - **语法**: 语法是否正确
        - **简洁性**: 表达是否简洁有力

        ### 4. 实用性 (15%)
        - **可操作性**: 指导是否可操作
        - **实例丰富**: 是否有足够示例
        - **问题解决**: 是否解决实际问题
        - **易用性**: 是否易于使用
        - **价值性**: 是否有实际价值

        ## 评估标准
        {{ detailed_evaluation_criteria }}

        ## 特殊考虑因素
        {% for factor in special_considerations %}
        - {{ factor }}
        {% endfor %}

        ## 输出格式
        ```json
        {
            "overall_score": 总体评分,
            "dimension_scores": {
                "content_quality": 内容质量评分,
                "structure_organization": 结构组织评分,
                "language_expression": 语言表达评分,
                "practicality": 实用性评分
            },
            "detailed_assessment": {
                "strengths": ["优点列表"],
                "weaknesses": ["不足列表"],
                "critical_issues": ["关键问题"],
                "improvement_priorities": ["改进优先级"]
            },
            "recommendations": ["具体改进建议"],
            "quality_level": "质量等级(优秀/良好/一般/需改进)",
            "approval_status": "是否通过评估"
        }
        ```

        请进行全面的质量评估：
        """
    
    @staticmethod
    def get_content_review_template() -> str:
        """获取内容审查模板"""
        return """
        # 内容审查专家

        你是一个专业的技术内容审查专家，擅长识别内容中的问题和改进机会。

        ## 审查任务
        审查类型: {{ review_type }}
        审查重点: {{ review_focus }}
        行业标准: {{ industry_standards }}
        合规要求: {{ compliance_requirements }}

        ## 待审查内容
        ```
        {{ content_to_review }}
        ```

        ## 审查清单
        ### 内容准确性
        - [ ] 技术信息是否准确
        - [ ] 数据和统计是否可靠
        - [ ] 引用和参考是否正确
        - [ ] 版本信息是否最新
        - [ ] 链接是否有效

        ### 内容完整性
        - [ ] 是否涵盖所有必要主题
        - [ ] 是否有遗漏的重要信息
        - [ ] 前置条件是否说明
        - [ ] 后续步骤是否完整
        - [ ] 异常情况是否考虑

        ### 内容适宜性
        - [ ] 内容是否适合目标受众
        - [ ] 技术深度是否合适
        - [ ] 语言风格是否恰当
        - [ ] 示例是否相关
        - [ ] 难度递进是否合理

        ### 合规性检查
        {% for compliance_item in compliance_checklist %}
        - [ ] {{ compliance_item }}
        {% endfor %}

        ## 审查标准
        {{ review_standards }}

        ## 风险评估
        请评估以下风险：
        - 信息误导风险
        - 技术过时风险
        - 安全隐患风险
        - 法律合规风险
        - 用户体验风险

        ## 输出格式
        ```json
        {
            "review_summary": {
                "overall_status": "审查状态",
                "critical_issues": 关键问题数量,
                "minor_issues": 一般问题数量,
                "approval_recommendation": "是否建议通过"
            },
            "detailed_findings": [
                {
                    "category": "问题类别",
                    "severity": "严重程度",
                    "description": "问题描述",
                    "location": "问题位置",
                    "impact": "影响评估",
                    "recommendation": "处理建议"
                }
            ],
            "compliance_status": {
                "compliant": true/false,
                "violations": ["违规项目"],
                "recommendations": ["合规建议"]
            },
            "next_steps": ["后续行动建议"]
        }
        ```

        请进行全面的内容审查：
        """
    
    @staticmethod
    def get_technical_accuracy_template() -> str:
        """获取技术准确性评估模板"""
        return """
        # 技术准确性评估专家

        你是一个资深的技术专家，擅长评估技术文档的准确性和可靠性。

        ## 评估任务
        技术领域: {{ technical_domain }}
        技术栈: {{ technology_stack }}
        复杂度级别: {{ complexity_level }}
        验证标准: {{ verification_standards }}

        ## 待评估内容
        ```
        {{ technical_content }}
        ```

        ## 技术验证点
        ### 代码准确性
        - 代码语法是否正确
        - 代码逻辑是否合理
        - 代码示例是否可执行
        - 最佳实践是否遵循
        - 性能考虑是否充分

        ### 配置准确性
        - 配置参数是否正确
        - 配置格式是否标准
        - 默认值是否合理
        - 环境要求是否明确
        - 兼容性是否考虑

        ### 架构准确性
        - 架构设计是否合理
        - 组件关系是否正确
        - 数据流是否清晰
        - 扩展性是否考虑
        - 安全性是否保证

        ### 操作准确性
        - 操作步骤是否正确
        - 命令语法是否准确
        - 参数说明是否完整
        - 错误处理是否充分
        - 回滚方案是否提供

        ## 技术标准
        {{ technical_standards }}

        ## 验证方法
        {% for method in verification_methods %}
        - {{ method }}
        {% endfor %}

        ## 风险评估
        请评估技术风险：
        - 实现风险
        - 性能风险
        - 安全风险
        - 维护风险
        - 兼容性风险

        ## 输出格式
        ```json
        {
            "accuracy_score": 准确性评分,
            "verification_results": {
                "code_accuracy": 代码准确性评分,
                "configuration_accuracy": 配置准确性评分,
                "architecture_accuracy": 架构准确性评分,
                "operation_accuracy": 操作准确性评分
            },
            "technical_issues": [
                {
                    "type": "问题类型",
                    "severity": "严重程度",
                    "description": "技术问题描述",
                    "impact": "影响分析",
                    "solution": "解决方案"
                }
            ],
            "risk_assessment": {
                "high_risks": ["高风险项"],
                "medium_risks": ["中风险项"],
                "low_risks": ["低风险项"]
            },
            "recommendations": ["技术改进建议"],
            "validation_status": "验证状态"
        }
        ```

        请进行详细的技术准确性评估：
        """
    
    @staticmethod
    def get_user_experience_template() -> str:
        """获取用户体验评估模板"""
        return """
        # 用户体验评估专家

        你是一个专业的用户体验专家，擅长从用户角度评估文档的可用性和体验。

        ## 评估任务
        用户类型: {{ user_types }}
        使用场景: {{ usage_scenarios }}
        体验目标: {{ experience_goals }}
        评估重点: {{ evaluation_focus }}

        ## 待评估内容
        ```
        {{ content_for_ux_evaluation }}
        ```

        ## 用户体验维度
        ### 可发现性 (Findability)
        - 信息是否容易找到
        - 导航是否清晰
        - 搜索是否有效
        - 结构是否合理

        ### 可理解性 (Understandability)
        - 语言是否易懂
        - 概念是否清晰
        - 示例是否充分
        - 术语是否解释

        ### 可操作性 (Actionability)
        - 指导是否可操作
        - 步骤是否清晰
        - 工具是否可用
        - 结果是否可验证

        ### 可访问性 (Accessibility)
        - 不同技能水平用户的适用性
        - 多种设备的兼容性
        - 无障碍访问支持
        - 国际化考虑

        ## 用户旅程分析
        {% for journey in user_journeys %}
        ### {{ journey.scenario }}
        - 用户目标: {{ journey.goal }}
        - 关键步骤: {{ journey.steps }}
        - 痛点分析: {{ journey.pain_points }}
        - 改进机会: {{ journey.opportunities }}
        {% endfor %}

        ## 评估标准
        {{ ux_evaluation_criteria }}

        ## 输出格式
        ```json
        {
            "ux_score": 用户体验评分,
            "dimension_scores": {
                "findability": 可发现性评分,
                "understandability": 可理解性评分,
                "actionability": 可操作性评分,
                "accessibility": 可访问性评分
            },
            "user_feedback": {
                "positive_aspects": ["积极方面"],
                "pain_points": ["痛点问题"],
                "confusion_areas": ["困惑区域"],
                "improvement_requests": ["改进需求"]
            },
            "recommendations": [
                {
                    "priority": "优先级",
                    "description": "改进建议",
                    "impact": "预期影响",
                    "effort": "实施难度"
                }
            ],
            "user_satisfaction": "用户满意度预测"
        }
        ```

        请进行全面的用户体验评估：
        """
    
    @staticmethod
    def get_completeness_check_template() -> str:
        """获取完整性检查模板"""
        return """
        # 文档完整性检查专家

        你是一个专业的文档完整性专家，擅长检查文档是否涵盖所有必要内容。

        ## 检查任务
        文档类型: {{ document_type }}
        完整性标准: {{ completeness_standards }}
        必需章节: {{ required_sections }}
        可选章节: {{ optional_sections }}

        ## 待检查内容
        ```
        {{ content_to_check }}
        ```

        ## 完整性检查清单
        ### 必需内容检查
        {% for required_item in required_content_items %}
        - [ ] {{ required_item.name }}: {{ required_item.description }}
        {% endfor %}

        ### 结构完整性
        - [ ] 是否有清晰的开头
        - [ ] 是否有逻辑的中间部分
        - [ ] 是否有合适的结尾
        - [ ] 章节间是否有连接
        - [ ] 是否有目录或导航

        ### 信息完整性
        - [ ] 背景信息是否充分
        - [ ] 步骤说明是否完整
        - [ ] 示例是否充足
        - [ ] 异常情况是否覆盖
        - [ ] 参考资料是否提供

        ### 功能完整性
        {% for feature in feature_coverage_items %}
        - [ ] {{ feature.name }}: {{ feature.coverage_requirement }}
        {% endfor %}

        ## 完整性标准
        {{ detailed_completeness_criteria }}

        ## 缺失内容分析
        请分析可能缺失的内容：
        - 关键信息缺失
        - 重要步骤遗漏
        - 必要示例不足
        - 参考资料缺乏
        - 后续指导缺失

        ## 输出格式
        ```json
        {
            "completeness_score": 完整性评分,
            "coverage_analysis": {
                "covered_items": ["已覆盖项目"],
                "missing_items": ["缺失项目"],
                "partial_items": ["部分覆盖项目"],
                "coverage_percentage": 覆盖百分比
            },
            "gap_analysis": [
                {
                    "category": "缺失类别",
                    "description": "缺失描述",
                    "importance": "重要程度",
                    "impact": "影响分析",
                    "recommendation": "补充建议"
                }
            ],
            "improvement_plan": [
                {
                    "priority": "优先级",
                    "action": "改进行动",
                    "content_to_add": "需要添加的内容",
                    "estimated_effort": "预估工作量"
                }
            ],
            "completeness_status": "完整性状态"
        }
        ```

        请进行全面的完整性检查：
        """
    
    @staticmethod
    def get_template(template_type: EvaluationTemplateType) -> str:
        """根据类型获取模板"""
        template_map = {
            EvaluationTemplateType.QUALITY_ASSESSMENT: EvaluationTemplates.get_quality_assessment_template(),
            EvaluationTemplateType.CONTENT_REVIEW: EvaluationTemplates.get_content_review_template(),
            EvaluationTemplateType.TECHNICAL_ACCURACY: EvaluationTemplates.get_technical_accuracy_template(),
            EvaluationTemplateType.USER_EXPERIENCE: EvaluationTemplates.get_user_experience_template(),
            EvaluationTemplateType.COMPLETENESS_CHECK: EvaluationTemplates.get_completeness_check_template()
        }
        
        return template_map.get(template_type, EvaluationTemplates.get_quality_assessment_template())
    
    @staticmethod
    def get_all_templates() -> Dict[str, str]:
        """获取所有模板"""
        return {
            "quality_assessment": EvaluationTemplates.get_quality_assessment_template(),
            "content_review": EvaluationTemplates.get_content_review_template(),
            "technical_accuracy": EvaluationTemplates.get_technical_accuracy_template(),
            "user_experience": EvaluationTemplates.get_user_experience_template(),
            "completeness_check": EvaluationTemplates.get_completeness_check_template()
        }
