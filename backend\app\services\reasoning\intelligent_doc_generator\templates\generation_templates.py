"""
生成阶段AI提示词模板

提供内容生成阶段使用的各种AI提示词模板
"""

from typing import Dict, Any
from enum import Enum


class GenerationTemplateType(str, Enum):
    """生成模板类型"""
    STRUCTURED = "structured"
    NARRATIVE = "narrative"
    TECHNICAL = "technical"
    CREATIVE = "creative"
    SECTION_SPECIFIC = "section_specific"


class GenerationTemplates:
    """生成阶段提示词模板集合"""
    
    @staticmethod
    def get_structured_generation_template() -> str:
        """获取结构化生成模板"""
        return """
        # 结构化内容生成专家

        你是一个专业的技术文档撰写专家，擅长创建结构清晰、逻辑严密的技术文档。

        ## 生成任务
        章节类型: {{ section_type }}
        章节标题: {{ section_title }}
        内容策略: {{ content_strategy }}
        目标长度: {{ target_length }} 字

        ## 项目信息
        项目名称: {{ project_name }}
        项目类型: {{ project_type }}
        主要语言: {{ primary_language }}
        技术栈: {{ tech_stack }}

        ## 结构要求
        {{ structure_requirements }}

        ## 上下文数据
        {% for key, value in context_data.items() %}
        {{ key }}: {{ value }}
        {% endfor %}

        ## 生成指导
        1. **结构清晰**: 使用标题、列表、表格等组织内容
        2. **逻辑严密**: 内容间有清晰的逻辑关系
        3. **信息准确**: 基于提供的数据，避免臆测
        4. **格式规范**: 使用标准的Markdown格式
        5. **完整性**: 覆盖章节的所有要点

        ## 质量标准
        - 信息准确性: 基于实际数据，避免虚构
        - 结构合理性: 层次分明，易于阅读
        - 内容完整性: 涵盖所有必要信息
        - 格式一致性: 遵循统一的格式规范

        ## 特殊要求
        {% for requirement in special_requirements %}
        - {{ requirement }}
        {% endfor %}

        请生成高质量的结构化内容：
        """
    
    @staticmethod
    def get_narrative_generation_template() -> str:
        """获取叙述性生成模板"""
        return """
        # 叙述性内容生成专家

        你是一个专业的技术写作专家，擅长创建流畅、连贯、引人入胜的技术文档。

        ## 生成任务
        章节类型: {{ section_type }}
        章节标题: {{ section_title }}
        叙述风格: {{ narrative_style }}
        语调风格: {{ tone_style }}

        ## 项目背景
        项目名称: {{ project_name }}
        项目愿景: {{ project_vision }}
        核心价值: {{ core_value }}
        目标受众: {{ target_audience }}

        ## 叙述素材
        {% for key, value in narrative_materials.items() %}
        {{ key }}: {{ value }}
        {% endfor %}

        ## 叙述要求
        1. **流畅性**: 语言自然流畅，逻辑连贯
        2. **可读性**: 易于理解，避免过于复杂的术语
        3. **吸引力**: 开头引人入胜，保持读者兴趣
        4. **情感连接**: 与读者建立情感共鸣
        5. **完整性**: 内容完整，覆盖关键信息

        ## 风格指导
        {{ style_guidance }}

        ## 写作技巧
        - 使用生动的描述和比喻
        - 采用适当的过渡词和连接句
        - 保持段落长度适中
        - 使用主动语态
        - 避免重复和冗余

        ## 目标效果
        {{ target_effect }}

        请创作引人入胜的叙述性内容：
        """
    
    @staticmethod
    def get_technical_generation_template() -> str:
        """获取技术文档生成模板"""
        return """
        # 技术文档生成专家

        你是一个资深的技术架构师和文档专家，擅长创建准确、详细、实用的技术文档。

        ## 技术任务
        文档类型: {{ document_type }}
        技术领域: {{ technical_domain }}
        复杂度级别: {{ complexity_level }}
        技术深度: {{ technical_depth }}

        ## 技术环境
        主要技术: {{ primary_technology }}
        技术栈: {{ technology_stack }}
        架构模式: {{ architecture_pattern }}
        部署环境: {{ deployment_environment }}

        ## 技术数据
        {% for key, value in technical_data.items() %}
        {{ key }}: {{ value }}
        {% endfor %}

        ## 技术要求
        1. **准确性**: 技术信息必须准确无误
        2. **完整性**: 覆盖所有关键技术点
        3. **实用性**: 提供可操作的技术指导
        4. **专业性**: 使用准确的技术术语
        5. **可维护性**: 考虑长期维护和更新

        ## 技术标准
        - 代码示例必须可执行
        - 配置信息必须准确
        - 版本信息必须明确
        - 依赖关系必须完整
        - 错误处理必须考虑

        ## 安全考虑
        {% for security_point in security_considerations %}
        - {{ security_point }}
        {% endfor %}

        ## 性能考虑
        {% for performance_point in performance_considerations %}
        - {{ performance_point }}
        {% endfor %}

        请生成高质量的技术文档：
        """
    
    @staticmethod
    def get_creative_generation_template() -> str:
        """获取创意内容生成模板"""
        return """
        # 创意内容生成专家

        你是一个富有创意的技术营销专家和内容创作者，擅长将技术特点转化为引人入胜的内容。

        ## 创意任务
        内容主题: {{ content_theme }}
        创意风格: {{ creative_style }}
        目标情感: {{ target_emotion }}
        传播渠道: {{ distribution_channel }}

        ## 创意素材
        项目亮点: {{ project_highlights }}
        独特价值: {{ unique_value }}
        用户痛点: {{ user_pain_points }}
        解决方案: {{ solutions }}

        ## 创意要求
        1. **吸引力**: 开头要抓住读者注意力
        2. **独特性**: 突出项目的独特价值和创新点
        3. **情感连接**: 与目标受众建立情感共鸣
        4. **可信度**: 在创意的同时保持专业可信
        5. **行动导向**: 激发读者的兴趣和行动意愿

        ## 创意技巧
        - 使用生动的比喻和类比
        - 讲述项目背后的故事
        - 突出解决的实际问题
        - 展示未来的可能性
        - 使用有力的动词和形容词

        ## 品牌调性
        {{ brand_tone }}

        ## 传播策略
        {{ communication_strategy }}

        请创作富有创意且专业的内容：
        """
    
    @staticmethod
    def get_section_specific_template(section_type: str) -> str:
        """获取特定章节类型的模板"""
        section_templates = {
            "introduction": """
            # 项目介绍生成专家
            
            请为{{ project_name }}项目生成专业的介绍内容，包括：
            1. 项目概述和核心价值
            2. 主要功能和特性
            3. 适用场景和目标用户
            4. 技术亮点和优势
            
            项目信息：{{ project_info }}
            """,
            
            "installation": """
            # 安装指南生成专家
            
            请为{{ project_name }}生成详细的安装指南，包括：
            1. 系统要求和前置条件
            2. 安装步骤（多种方式）
            3. 配置说明
            4. 验证安装成功的方法
            5. 常见问题和解决方案
            
            技术信息：{{ technical_info }}
            """,
            
            "usage": """
            # 使用说明生成专家
            
            请为{{ project_name }}生成实用的使用说明，包括：
            1. 快速开始指南
            2. 基本使用方法
            3. 常用功能示例
            4. 高级用法
            5. 最佳实践建议
            
            功能信息：{{ feature_info }}
            """,
            
            "api_reference": """
            # API参考文档生成专家
            
            请为{{ project_name }}生成完整的API参考文档，包括：
            1. API概述和认证
            2. 端点列表和分组
            3. 请求/响应格式
            4. 参数说明和示例
            5. 错误码和处理
            
            API信息：{{ api_info }}
            """
        }
        
        return section_templates.get(section_type, GenerationTemplates.get_structured_generation_template())
    
    @staticmethod
    def get_template(template_type: GenerationTemplateType) -> str:
        """根据类型获取模板"""
        template_map = {
            GenerationTemplateType.STRUCTURED: GenerationTemplates.get_structured_generation_template(),
            GenerationTemplateType.NARRATIVE: GenerationTemplates.get_narrative_generation_template(),
            GenerationTemplateType.TECHNICAL: GenerationTemplates.get_technical_generation_template(),
            GenerationTemplateType.CREATIVE: GenerationTemplates.get_creative_generation_template()
        }
        
        return template_map.get(template_type, GenerationTemplates.get_structured_generation_template())
    
    @staticmethod
    def get_all_templates() -> Dict[str, str]:
        """获取所有模板"""
        return {
            "structured": GenerationTemplates.get_structured_generation_template(),
            "narrative": GenerationTemplates.get_narrative_generation_template(),
            "technical": GenerationTemplates.get_technical_generation_template(),
            "creative": GenerationTemplates.get_creative_generation_template()
        }
