"""
规划阶段AI提示词模板

提供文档规划阶段使用的各种AI提示词模板
"""

from typing import Dict, Any
from enum import Enum


class PlanningTemplateType(str, Enum):
    """规划模板类型"""
    ADAPTIVE = "adaptive"
    DOMAIN_SPECIFIC = "domain_specific"
    TEMPLATE_BASED = "template_based"
    EVALUATION = "evaluation"


class PlanningTemplates:
    """规划阶段提示词模板集合"""
    
    @staticmethod
    def get_adaptive_planning_template() -> str:
        """获取自适应规划模板"""
        return """
        # 智能文档规划专家

        你是一个专业的技术文档规划专家，擅长根据项目特征智能规划文档结构。

        ## 规划任务
        项目名称: {{ project_name }}
        项目类型: {{ project_type }}
        主要语言: {{ primary_language }}
        目标受众: {{ target_audience }}

        ## 项目分析数据
        {% if structure_analysis %}
        ### 结构分析
        {% for key, value in structure_analysis.items() %}
        - {{ key }}: {{ value }}
        {% endfor %}
        {% endif %}

        {% if dependency_analysis %}
        ### 依赖分析
        {% for key, value in dependency_analysis.items() %}
        - {{ key }}: {{ value }}
        {% endfor %}
        {% endif %}

        {% if module_analysis %}
        ### 模块分析
        {% for key, value in module_analysis.items() %}
        - {{ key }}: {{ value }}
        {% endfor %}
        {% endif %}

        ## 规划要求
        1. **适应性**: 根据项目特征调整章节结构
        2. **完整性**: 覆盖项目的所有重要方面
        3. **逻辑性**: 章节间有清晰的逻辑关系
        4. **实用性**: 对目标受众有实际价值
        5. **可行性**: 考虑生成的复杂度和时间成本

        ## 规划约束
        - 最大章节数: {{ max_sections }}
        - 预期总长度: {{ preferred_length }} 字
        {% if time_constraints %}
        - 时间限制: {{ time_constraints }} 分钟
        {% endif %}

        ## 输出格式
        请以JSON格式输出规划结果，包含以下字段：
        ```json
        {
            "sections": [
                {
                    "section_id": "章节ID",
                    "title": "章节标题",
                    "section_type": "章节类型",
                    "priority": "优先级(high/medium/low)",
                    "estimated_length": 预估字数,
                    "complexity_level": "复杂度(low/medium/high)",
                    "dependencies": ["依赖的章节ID"],
                    "description": "章节描述",
                    "key_points": ["关键要点"],
                    "required_data_sources": ["需要的数据源"]
                }
            ],
            "reasoning": "规划理由和说明"
        }
        ```

        请根据项目特征智能规划文档结构：
        """
    
    @staticmethod
    def get_domain_specific_planning_template() -> str:
        """获取领域特定规划模板"""
        return """
        # 领域特定文档规划专家

        你是一个专业的{{ domain }}领域文档规划专家，深度了解该领域的文档规范和最佳实践。

        ## 领域信息
        领域类型: {{ domain }}
        项目子类型: {{ project_subtype }}
        技术栈: {{ tech_stack }}
        行业标准: {{ industry_standards }}

        ## 项目信息
        项目名称: {{ project_name }}
        项目规模: {{ project_scale }}
        复杂度: {{ complexity }}
        团队规模: {{ team_size }}

        ## 领域特定要求
        {% for requirement in domain_requirements %}
        - {{ requirement }}
        {% endfor %}

        ## 行业最佳实践
        {% for practice in best_practices %}
        - {{ practice }}
        {% endfor %}

        ## 规划指导原则
        1. **领域规范**: 遵循{{ domain }}领域的文档标准
        2. **专业性**: 使用准确的领域术语和概念
        3. **实践导向**: 结合实际应用场景
        4. **标准兼容**: 符合行业标准和规范
        5. **可扩展性**: 考虑未来的扩展需求

        ## 特殊考虑因素
        {% for factor in special_considerations %}
        - {{ factor }}
        {% endfor %}

        请基于{{ domain }}领域的专业知识规划文档结构，确保符合行业标准和最佳实践。

        输出格式同自适应规划模板。
        """
    
    @staticmethod
    def get_template_based_planning_template() -> str:
        """获取基于模板的规划模板"""
        return """
        # 模板化文档规划专家

        你是一个专业的文档模板专家，擅长基于现有模板快速规划文档结构。

        ## 基础模板
        模板名称: {{ template_name }}
        模板类型: {{ template_type }}
        适用场景: {{ template_scope }}

        ## 模板结构
        {% for section in template_sections %}
        ### {{ section.title }}
        - 类型: {{ section.type }}
        - 优先级: {{ section.priority }}
        - 描述: {{ section.description }}
        {% if section.subsections %}
        - 子章节:
        {% for subsection in section.subsections %}
          - {{ subsection }}
        {% endfor %}
        {% endif %}
        {% endfor %}

        ## 项目适配信息
        项目名称: {{ project_name }}
        项目特征: {{ project_features }}
        定制需求: {{ customization_needs }}

        ## 适配要求
        1. **模板遵循**: 基于选定模板的基础结构
        2. **项目适配**: 根据项目特征调整内容
        3. **个性化**: 添加项目特有的章节
        4. **优化**: 移除不适用的章节
        5. **增强**: 强化重要的章节内容

        ## 定制指导
        {% for guideline in customization_guidelines %}
        - {{ guideline }}
        {% endfor %}

        请基于模板结构，结合项目特征，生成适配的文档规划。

        输出格式同自适应规划模板。
        """
    
    @staticmethod
    def get_planning_evaluation_template() -> str:
        """获取规划评估模板"""
        return """
        # 文档规划评估专家

        你是一个专业的文档质量评估专家，擅长评估文档规划方案的质量和可行性。

        ## 待评估规划
        规划ID: {{ plan_id }}
        规划策略: {{ planning_strategy }}
        章节数量: {{ section_count }}

        ## 规划详情
        {% for section in sections %}
        ### {{ section.title }}
        - ID: {{ section.section_id }}
        - 类型: {{ section.section_type }}
        - 优先级: {{ section.priority }}
        - 预估长度: {{ section.estimated_length }}
        - 复杂度: {{ section.complexity_level }}
        - 依赖: {{ section.dependencies }}
        {% endfor %}

        ## 项目上下文
        项目类型: {{ project_type }}
        目标受众: {{ target_audience }}
        时间约束: {{ time_constraints }}
        质量要求: {{ quality_requirements }}

        ## 评估维度
        请从以下维度评估规划质量（0-1分）：

        ### 1. 完整性评估
        - 是否覆盖项目的所有重要方面
        - 是否包含必要的基础章节
        - 是否遗漏关键信息

        ### 2. 相关性评估
        - 章节内容是否与项目类型匹配
        - 是否符合目标受众需求
        - 是否包含不必要的章节

        ### 3. 可行性评估
        - 预估工作量是否合理
        - 依赖关系是否可行
        - 时间安排是否现实

        ### 4. 连贯性评估
        - 章节间逻辑关系是否清晰
        - 内容组织是否合理
        - 是否存在重复或冲突

        ## 输出格式
        ```json
        {
            "overall_score": 总体评分,
            "completeness_score": 完整性评分,
            "relevance_score": 相关性评分,
            "feasibility_score": 可行性评分,
            "coherence_score": 连贯性评分,
            "strengths": ["优点列表"],
            "weaknesses": ["不足列表"],
            "improvement_suggestions": ["改进建议"],
            "missing_sections": ["缺失章节"],
            "redundant_sections": ["冗余章节"],
            "dependency_issues": ["依赖问题"],
            "estimated_time": 预估完成时间,
            "risk_factors": ["风险因素"],
            "approval_recommendation": "是否建议批准"
        }
        ```

        请进行全面的规划质量评估：
        """
    
    @staticmethod
    def get_template(template_type: PlanningTemplateType) -> str:
        """根据类型获取模板"""
        template_map = {
            PlanningTemplateType.ADAPTIVE: PlanningTemplates.get_adaptive_planning_template(),
            PlanningTemplateType.DOMAIN_SPECIFIC: PlanningTemplates.get_domain_specific_planning_template(),
            PlanningTemplateType.TEMPLATE_BASED: PlanningTemplates.get_template_based_planning_template(),
            PlanningTemplateType.EVALUATION: PlanningTemplates.get_planning_evaluation_template()
        }
        
        return template_map.get(template_type, PlanningTemplates.get_adaptive_planning_template())
    
    @staticmethod
    def get_all_templates() -> Dict[str, str]:
        """获取所有模板"""
        return {
            "adaptive": PlanningTemplates.get_adaptive_planning_template(),
            "domain_specific": PlanningTemplates.get_domain_specific_planning_template(),
            "template_based": PlanningTemplates.get_template_based_planning_template(),
            "evaluation": PlanningTemplates.get_planning_evaluation_template()
        }
