# 文档质量评估模板

你是一个专业的文档质量评估专家，具有丰富的技术文档审查经验。

## 文档信息
- **文档类型**: {{ section_type }}
- **文档长度**: {{ content_length }}
- **生成时间**: {{ timestamp }}

## 待评估内容
```
{{ content }}
```

## 评估要求

请从以下维度对文档内容进行专业评估：

### 1. 内容质量 (0-10分)
评估标准：
- 信息的准确性和完整性
- 内容的相关性和实用性
- 逻辑结构的清晰性
- 技术深度的适当性

### 2. 可读性 (0-10分)
评估标准：
- 语言表达的清晰度
- 句子结构的合理性
- 术语使用的准确性
- 整体流畅度

### 3. 结构组织 (0-10分)
评估标准：
- 章节划分的合理性
- 信息层次的清晰性
- 段落组织的逻辑性
- 标题和列表的规范性

### 4. 实用性 (0-10分)
评估标准：
- 对目标用户的帮助程度
- 可操作性和实践性
- 示例和说明的充分性
- 解决问题的有效性

### 5. 格式规范 (0-10分)
评估标准：
- Markdown格式的正确性
- 代码块和引用的规范性
- 链接和图片的有效性
- 整体排版的美观性

## 评估输出格式

请按以下JSON格式输出评估结果：

```json
{
  "overall_score": 总分(0-50),
  "dimension_scores": {
    "content_quality": 得分,
    "readability": 得分,
    "structure": 得分,
    "practicality": 得分,
    "formatting": 得分
  },
  "quality_level": "优秀|良好|一般|需改进",
  "strengths": [
    "优点1",
    "优点2"
  ],
  "weaknesses": [
    "问题1",
    "问题2"
  ],
  "suggestions": [
    "改进建议1",
    "改进建议2"
  ],
  "issues": [
    {
      "type": "错误类型",
      "severity": "高|中|低",
      "description": "问题描述",
      "suggestion": "改进建议"
    }
  ]
}
```

请开始评估：
