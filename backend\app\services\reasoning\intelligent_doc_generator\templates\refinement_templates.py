"""
优化阶段AI提示词模板

提供内容优化和精炼阶段使用的各种AI提示词模板
"""

from typing import Dict, Any
from enum import Enum


class RefinementTemplateType(str, Enum):
    """优化模板类型"""
    QUALITY_IMPROVEMENT = "quality_improvement"
    STYLE_REFINEMENT = "style_refinement"
    STRUCTURE_OPTIMIZATION = "structure_optimization"
    CONTENT_ENHANCEMENT = "content_enhancement"
    CONSISTENCY_CHECK = "consistency_check"


class RefinementTemplates:
    """优化阶段提示词模板集合"""
    
    @staticmethod
    def get_quality_improvement_template() -> str:
        """获取质量改进模板"""
        return """
        # 文档质量改进专家

        你是一个专业的文档质量专家，擅长识别和改进文档中的质量问题。

        ## 改进任务
        文档类型: {{ document_type }}
        当前质量分数: {{ current_quality_score }}
        目标质量分数: {{ target_quality_score }}
        主要问题: {{ main_issues }}

        ## 原始内容
        ```
        {{ original_content }}
        ```

        ## 质量问题分析
        {% for issue in quality_issues %}
        ### {{ issue.category }}
        - 问题描述: {{ issue.description }}
        - 严重程度: {{ issue.severity }}
        - 影响范围: {{ issue.impact }}
        - 建议解决方案: {{ issue.solution }}
        {% endfor %}

        ## 改进要求
        1. **准确性改进**: 修正事实错误和不准确信息
        2. **完整性改进**: 补充缺失的重要信息
        3. **清晰度改进**: 简化复杂表述，提高可读性
        4. **一致性改进**: 统一术语、格式和风格
        5. **实用性改进**: 增强内容的实际应用价值

        ## 质量标准
        - 信息准确率: 100%
        - 内容完整度: ≥90%
        - 可读性指数: ≥80%
        - 格式一致性: 100%
        - 实用性评分: ≥85%

        ## 改进指导
        {{ improvement_guidelines }}

        ## 特殊注意事项
        {% for note in special_notes %}
        - {{ note }}
        {% endfor %}

        请对内容进行全面的质量改进，确保达到目标质量标准：
        """
    
    @staticmethod
    def get_style_refinement_template() -> str:
        """获取风格精炼模板"""
        return """
        # 文档风格精炼专家

        你是一个专业的技术写作风格专家，擅长优化文档的语言风格和表达方式。

        ## 精炼任务
        目标风格: {{ target_style }}
        当前风格问题: {{ style_issues }}
        目标受众: {{ target_audience }}
        语言偏好: {{ language_preference }}

        ## 原始内容
        ```
        {{ original_content }}
        ```

        ## 风格要求
        {{ style_requirements }}

        ## 精炼目标
        1. **语言风格**: 调整为{{ target_style }}风格
        2. **语调统一**: 保持一致的语调和态度
        3. **表达优化**: 使用更准确、生动的表达
        4. **句式多样**: 避免单调的句式结构
        5. **词汇精准**: 选择最合适的词汇和术语

        ## 风格指导原则
        {% for principle in style_principles %}
        - {{ principle }}
        {% endfor %}

        ## 语言技巧
        - 使用主动语态而非被动语态
        - 选择具体词汇而非抽象概念
        - 保持句子长度适中
        - 使用过渡词增强连贯性
        - 避免冗余和重复表达

        ## 受众考虑
        {{ audience_considerations }}

        ## 品牌语调
        {{ brand_voice }}

        请对内容进行风格精炼，确保符合目标风格要求：
        """
    
    @staticmethod
    def get_structure_optimization_template() -> str:
        """获取结构优化模板"""
        return """
        # 文档结构优化专家

        你是一个专业的文档结构专家，擅长优化文档的组织结构和信息架构。

        ## 优化任务
        文档类型: {{ document_type }}
        当前结构问题: {{ structure_issues }}
        优化目标: {{ optimization_goals }}
        读者行为模式: {{ reader_behavior }}

        ## 原始内容
        ```
        {{ original_content }}
        ```

        ## 结构分析
        {% for section in current_structure %}
        ### {{ section.title }}
        - 位置: {{ section.position }}
        - 长度: {{ section.length }}
        - 重要性: {{ section.importance }}
        - 问题: {{ section.issues }}
        {% endfor %}

        ## 优化要求
        1. **逻辑优化**: 调整内容顺序，增强逻辑性
        2. **层次优化**: 优化标题层级和内容分组
        3. **流程优化**: 改善信息流和阅读体验
        4. **重点突出**: 突出关键信息和要点
        5. **导航优化**: 增强文档的可导航性

        ## 结构原则
        - 重要信息前置
        - 逻辑关系清晰
        - 层次结构合理
        - 内容分组恰当
        - 过渡自然流畅

        ## 优化策略
        {{ optimization_strategies }}

        ## 用户体验考虑
        {{ user_experience_factors }}

        ## 可访问性要求
        {% for requirement in accessibility_requirements %}
        - {{ requirement }}
        {% endfor %}

        请对文档结构进行全面优化，提升阅读体验和信息传达效果：
        """
    
    @staticmethod
    def get_content_enhancement_template() -> str:
        """获取内容增强模板"""
        return """
        # 内容增强专家

        你是一个专业的内容增强专家，擅长丰富和深化文档内容。

        ## 增强任务
        增强类型: {{ enhancement_type }}
        增强目标: {{ enhancement_goals }}
        内容深度: {{ content_depth }}
        增强范围: {{ enhancement_scope }}

        ## 原始内容
        ```
        {{ original_content }}
        ```

        ## 可用资源
        {% for resource in available_resources %}
        ### {{ resource.type }}
        {{ resource.content }}
        {% endfor %}

        ## 增强要求
        1. **深度增强**: 增加技术细节和深度分析
        2. **广度增强**: 扩展相关主题和应用场景
        3. **实例增强**: 添加具体示例和案例研究
        4. **交互增强**: 增加图表、代码示例等元素
        5. **价值增强**: 提升内容的实用价值

        ## 增强策略
        {{ enhancement_strategies }}

        ## 内容类型指导
        {% for content_type, guidance in content_type_guidance.items() %}
        ### {{ content_type }}
        {{ guidance }}
        {% endfor %}

        ## 质量标准
        - 信息密度适中
        - 实用性强
        - 可操作性高
        - 示例丰富
        - 逻辑清晰

        ## 增强限制
        {% for limitation in enhancement_limitations %}
        - {{ limitation }}
        {% endfor %}

        请对内容进行全面增强，提升其价值和实用性：
        """
    
    @staticmethod
    def get_consistency_check_template() -> str:
        """获取一致性检查模板"""
        return """
        # 文档一致性检查专家

        你是一个专业的文档一致性专家，擅长识别和修正文档中的不一致问题。

        ## 检查任务
        检查范围: {{ check_scope }}
        一致性标准: {{ consistency_standards }}
        已知问题: {{ known_issues }}

        ## 待检查内容
        ```
        {{ content_to_check }}
        ```

        ## 参考标准
        {% for standard in reference_standards %}
        ### {{ standard.category }}
        {{ standard.rules }}
        {% endfor %}

        ## 检查维度
        1. **术语一致性**: 检查专业术语的使用是否统一
        2. **格式一致性**: 检查标题、列表、代码块等格式
        3. **风格一致性**: 检查语言风格和表达方式
        4. **结构一致性**: 检查章节结构和组织方式
        5. **引用一致性**: 检查链接、引用的格式和准确性

        ## 一致性规则
        {{ consistency_rules }}

        ## 检查重点
        {% for focus_area in check_focus_areas %}
        - {{ focus_area }}
        {% endfor %}

        ## 修正指导
        - 优先修正影响理解的不一致
        - 保持修正的最小化原则
        - 确保修正后的一致性
        - 记录重要的修正决策

        ## 输出格式
        请按以下格式输出检查结果：
        ```json
        {
            "consistency_score": 一致性评分,
            "issues_found": [
                {
                    "type": "问题类型",
                    "location": "位置",
                    "description": "问题描述",
                    "severity": "严重程度",
                    "suggestion": "修正建议"
                }
            ],
            "corrected_content": "修正后的内容",
            "summary": "检查总结"
        }
        ```

        请进行全面的一致性检查和修正：
        """
    
    @staticmethod
    def get_template(template_type: RefinementTemplateType) -> str:
        """根据类型获取模板"""
        template_map = {
            RefinementTemplateType.QUALITY_IMPROVEMENT: RefinementTemplates.get_quality_improvement_template(),
            RefinementTemplateType.STYLE_REFINEMENT: RefinementTemplates.get_style_refinement_template(),
            RefinementTemplateType.STRUCTURE_OPTIMIZATION: RefinementTemplates.get_structure_optimization_template(),
            RefinementTemplateType.CONTENT_ENHANCEMENT: RefinementTemplates.get_content_enhancement_template(),
            RefinementTemplateType.CONSISTENCY_CHECK: RefinementTemplates.get_consistency_check_template()
        }
        
        return template_map.get(template_type, RefinementTemplates.get_quality_improvement_template())
    
    @staticmethod
    def get_all_templates() -> Dict[str, str]:
        """获取所有模板"""
        return {
            "quality_improvement": RefinementTemplates.get_quality_improvement_template(),
            "style_refinement": RefinementTemplates.get_style_refinement_template(),
            "structure_optimization": RefinementTemplates.get_structure_optimization_template(),
            "content_enhancement": RefinementTemplates.get_content_enhancement_template(),
            "consistency_check": RefinementTemplates.get_consistency_check_template()
        }
