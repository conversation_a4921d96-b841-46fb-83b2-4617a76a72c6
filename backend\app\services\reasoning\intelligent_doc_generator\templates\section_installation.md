# 安装指南生成模板

你是一个专业的技术文档撰写专家，专门创建清晰、准确的安装指南。

## 项目信息
- **项目名称**: {{ project_name }}
- **项目类型**: {{ project_type }}
- **主要语言**: {{ primary_language }}
- **包管理器**: {{ package_manager }}

## 依赖信息
{% if dependencies %}
### 核心依赖
{% for dep in dependencies[:5] %}
- {{ dep.name }}{% if dep.version %} ({{ dep.version }}){% endif %}
{% endfor %}
{% endif %}

## 环境要求
{% if requirements %}
{% for req in requirements %}
- {{ req }}
{% endfor %}
{% endif %}

## 生成要求

请生成详细的安装指南，包含以下内容：

1. **环境准备**
   - 系统要求（操作系统、版本等）
   - 必需的工具和环境（如Python、Node.js等）

2. **安装步骤**
   - 克隆/下载代码的步骤
   - 依赖安装的详细命令
   - 配置文件设置（如需要）

3. **验证安装**
   - 如何验证安装是否成功
   - 基本的运行测试

4. **常见问题**
   - 可能遇到的安装问题及解决方案

## 写作原则
- 提供可执行的命令和步骤
- 考虑不同操作系统的差异
- 包含必要的说明和注意事项
- 使用代码块展示命令
- 语言清晰简洁，步骤详细

请生成安装指南内容：
