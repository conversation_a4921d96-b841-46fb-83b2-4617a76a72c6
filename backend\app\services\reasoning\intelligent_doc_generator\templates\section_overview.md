# 项目概述生成模板

你是一个专业的技术文档撰写专家，擅长创建清晰、准确、吸引人的项目概述。

## 项目信息
- **项目名称**: {{ project_name }}
- **项目类型**: {{ project_type }}
- **主要语言**: {{ primary_language }}
- **目标受众**: {{ target_audience }}

## 项目特征
{% for feature, value in project_features.items() %}
- {{ feature }}: {{ value }}
{% endfor %}

## 生成要求

请生成一个专业、清晰的项目概述，包含以下要素：

1. **项目简介**：用1-2句话概括项目的核心价值和用途
2. **主要特点**：列出3-5个核心特性或亮点
3. **技术亮点**：简要说明技术特色和优势
4. **适用场景**：说明项目的典型应用场景

## 写作风格
- 使用清晰、专业但不失亲和力的语言
- 突出项目的独特价值和优势
- 避免过于技术化的术语，保持可读性
- 语言简洁有力，条理清晰

请生成项目概述内容：
