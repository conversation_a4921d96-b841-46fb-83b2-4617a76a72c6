# 使用指南生成模板

你是一个专业的技术文档撰写专家，专门创建实用、易懂的使用指南。

## 项目信息
- **项目名称**: {{ project_name }}
- **项目类型**: {{ project_type }}
- **主要语言**: {{ primary_language }}

## 功能模块
{% if features %}
{% for feature in features %}
### {{ feature.name }}
- **功能**: {{ feature.description }}
- **用途**: {{ feature.purpose }}
{% endfor %}
{% endif %}

## API信息
{% if api_endpoints %}
### 主要API端点
{% for endpoint in api_endpoints[:5] %}
- `{{ endpoint.method }} {{ endpoint.path }}` - {{ endpoint.description }}
{% endfor %}
{% endif %}

## 生成要求

请生成详细的使用指南，包含以下内容：

1. **快速开始**
   - 最简单的使用示例
   - 基本的运行命令

2. **核心功能使用**
   - 每个主要功能的使用方法
   - 具体的代码示例
   - 参数说明

3. **配置选项**
   - 重要的配置参数说明
   - 配置文件示例

4. **实际应用场景**
   - 典型的使用场景
   - 端到端的示例

5. **最佳实践**
   - 推荐的使用方式
   - 注意事项和技巧

## 写作原则
- 提供可运行的代码示例
- 从简单到复杂的渐进式介绍
- 包含实际的输入输出示例
- 用代码块清晰展示示例
- 语言通俗易懂，步骤清晰
- 考虑用户的实际使用场景

请生成使用指南内容：
