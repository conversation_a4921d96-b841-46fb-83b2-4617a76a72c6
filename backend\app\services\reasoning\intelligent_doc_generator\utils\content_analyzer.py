"""
内容分析工具

提供文档内容分析功能，包括：
- 内容质量分析
- 结构分析
- 语义分析
- 相似度计算
"""

import re
import logging
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass
from collections import Counter
import math

logger = logging.getLogger(__name__)


@dataclass
class ContentQuality:
    """内容质量评估结果"""
    overall_score: float
    readability_score: float
    completeness_score: float
    coherence_score: float
    accuracy_score: float
    issues: List[str]
    strengths: List[str]
    recommendations: List[str]


@dataclass
class StructureAnalysis:
    """结构分析结果"""
    has_title: bool
    has_introduction: bool
    has_conclusion: bool
    section_count: int
    subsection_count: int
    hierarchy_depth: int
    structure_score: float
    structure_issues: List[str]


@dataclass
class SemanticAnalysis:
    """语义分析结果"""
    main_topics: List[str]
    key_concepts: List[str]
    sentiment_score: float
    complexity_level: str
    domain_classification: str
    semantic_coherence: float


class ContentAnalyzer:
    """内容分析工具类"""
    
    def __init__(self):
        """初始化内容分析器"""
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did'
        }
        
        self.technical_indicators = {
            'api', 'function', 'method', 'class', 'variable', 'parameter', 'return', 'import', 'export',
            'database', 'server', 'client', 'request', 'response', 'http', 'https', 'json', 'xml',
            'algorithm', 'data', 'structure', 'array', 'object', 'string', 'integer', 'boolean'
        }
        
        self.quality_indicators = {
            'positive': ['详细', '清晰', '完整', '准确', '有用', '实用', '简洁', '明确'],
            'negative': ['模糊', '不清楚', '错误', '缺失', '重复', '冗余', '混乱']
        }
    
    def analyze_content_quality(self, content: str, context: Optional[Dict[str, Any]] = None) -> ContentQuality:
        """分析内容质量"""
        if not content:
            return ContentQuality(0.0, 0.0, 0.0, 0.0, 0.0, ["内容为空"], [], [])
        
        context = context or {}
        
        # 可读性分析
        readability_score = self._analyze_readability(content)
        
        # 完整性分析
        completeness_score = self._analyze_completeness(content, context)
        
        # 连贯性分析
        coherence_score = self._analyze_coherence(content)
        
        # 准确性分析
        accuracy_score = self._analyze_accuracy(content, context)
        
        # 识别问题和优点
        issues = self._identify_quality_issues(content)
        strengths = self._identify_strengths(content)
        
        # 生成建议
        recommendations = self._generate_quality_recommendations(
            readability_score, completeness_score, coherence_score, accuracy_score, issues
        )
        
        # 计算总分
        overall_score = (
            readability_score * 0.25 +
            completeness_score * 0.3 +
            coherence_score * 0.25 +
            accuracy_score * 0.2
        )
        
        return ContentQuality(
            overall_score=overall_score,
            readability_score=readability_score,
            completeness_score=completeness_score,
            coherence_score=coherence_score,
            accuracy_score=accuracy_score,
            issues=issues,
            strengths=strengths,
            recommendations=recommendations
        )
    
    def analyze_structure(self, content: str) -> StructureAnalysis:
        """分析文档结构"""
        if not content:
            return StructureAnalysis(False, False, False, 0, 0, 0, 0.0, ["内容为空"])
        
        lines = content.split('\n')
        
        # 检测标题
        has_title = self._has_document_title(lines)
        
        # 检测介绍和结论
        has_introduction = self._has_introduction(content)
        has_conclusion = self._has_conclusion(content)
        
        # 计算章节数量
        section_count, subsection_count, hierarchy_depth = self._count_sections(lines)
        
        # 评估结构质量
        structure_score = self._calculate_structure_score(
            has_title, has_introduction, has_conclusion, section_count, hierarchy_depth
        )
        
        # 识别结构问题
        structure_issues = self._identify_structure_issues(
            has_title, has_introduction, has_conclusion, section_count, hierarchy_depth
        )
        
        return StructureAnalysis(
            has_title=has_title,
            has_introduction=has_introduction,
            has_conclusion=has_conclusion,
            section_count=section_count,
            subsection_count=subsection_count,
            hierarchy_depth=hierarchy_depth,
            structure_score=structure_score,
            structure_issues=structure_issues
        )
    
    def analyze_semantics(self, content: str) -> SemanticAnalysis:
        """分析语义内容"""
        if not content:
            return SemanticAnalysis([], [], 0.0, "unknown", "unknown", 0.0)
        
        # 提取主要话题
        main_topics = self._extract_main_topics(content)
        
        # 提取关键概念
        key_concepts = self._extract_key_concepts(content)
        
        # 情感分析
        sentiment_score = self._analyze_sentiment(content)
        
        # 复杂度分析
        complexity_level = self._analyze_complexity(content)
        
        # 领域分类
        domain_classification = self._classify_domain(content)
        
        # 语义连贯性
        semantic_coherence = self._analyze_semantic_coherence(content)
        
        return SemanticAnalysis(
            main_topics=main_topics,
            key_concepts=key_concepts,
            sentiment_score=sentiment_score,
            complexity_level=complexity_level,
            domain_classification=domain_classification,
            semantic_coherence=semantic_coherence
        )
    
    def calculate_similarity(self, content1: str, content2: str) -> float:
        """计算两个内容的相似度"""
        if not content1 or not content2:
            return 0.0
        
        # 提取词汇
        words1 = self._extract_words(content1)
        words2 = self._extract_words(content2)
        
        # 计算词汇相似度
        lexical_similarity = self._calculate_lexical_similarity(words1, words2)
        
        # 计算结构相似度
        structure_similarity = self._calculate_structure_similarity(content1, content2)
        
        # 计算语义相似度（简化版）
        semantic_similarity = self._calculate_semantic_similarity(content1, content2)
        
        # 综合相似度
        overall_similarity = (
            lexical_similarity * 0.4 +
            structure_similarity * 0.3 +
            semantic_similarity * 0.3
        )
        
        return overall_similarity
    
    def _analyze_readability(self, content: str) -> float:
        """分析可读性"""
        # 计算句子长度
        sentences = re.split(r'[.!?。！？]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if not sentences:
            return 0.0
        
        # 平均句长
        total_words = sum(len(self._extract_words(sentence)) for sentence in sentences)
        avg_sentence_length = total_words / len(sentences)
        
        # 词汇复杂度
        words = self._extract_words(content)
        unique_words = set(words)
        vocabulary_diversity = len(unique_words) / len(words) if words else 0
        
        # 技术术语密度
        technical_words = sum(1 for word in words if word.lower() in self.technical_indicators)
        technical_density = technical_words / len(words) if words else 0
        
        # 可读性评分（简化版）
        readability_score = 1.0
        
        # 句长惩罚
        if avg_sentence_length > 20:
            readability_score -= 0.2
        elif avg_sentence_length > 15:
            readability_score -= 0.1
        
        # 词汇多样性奖励
        if vocabulary_diversity > 0.7:
            readability_score += 0.1
        elif vocabulary_diversity < 0.3:
            readability_score -= 0.1
        
        # 技术密度调整
        if technical_density > 0.3:
            readability_score -= 0.1
        
        return max(0.0, min(1.0, readability_score))
    
    def _analyze_completeness(self, content: str, context: Dict[str, Any]) -> float:
        """分析完整性"""
        completeness_score = 0.0
        
        # 基础长度检查
        word_count = len(self._extract_words(content))
        if word_count >= 100:
            completeness_score += 0.3
        elif word_count >= 50:
            completeness_score += 0.2
        elif word_count >= 20:
            completeness_score += 0.1
        
        # 结构完整性
        structure = self.analyze_structure(content)
        if structure.has_title:
            completeness_score += 0.1
        if structure.has_introduction:
            completeness_score += 0.1
        if structure.section_count > 0:
            completeness_score += 0.2
        
        # 内容要素检查
        required_elements = context.get('required_elements', [])
        if required_elements:
            covered_elements = sum(1 for element in required_elements if element.lower() in content.lower())
            element_coverage = covered_elements / len(required_elements)
            completeness_score += element_coverage * 0.3
        else:
            completeness_score += 0.3  # 默认完整性
        
        return min(1.0, completeness_score)
    
    def _analyze_coherence(self, content: str) -> float:
        """分析连贯性"""
        paragraphs = content.split('\n\n')
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        if len(paragraphs) < 2:
            return 0.8  # 单段落默认连贯
        
        coherence_score = 0.0
        
        # 段落间词汇重叠
        total_overlap = 0
        for i in range(len(paragraphs) - 1):
            words1 = set(self._extract_words(paragraphs[i]))
            words2 = set(self._extract_words(paragraphs[i + 1]))
            overlap = len(words1 & words2) / len(words1 | words2) if words1 | words2 else 0
            total_overlap += overlap
        
        avg_overlap = total_overlap / (len(paragraphs) - 1)
        coherence_score += avg_overlap * 0.5
        
        # 逻辑连接词检查
        transition_words = ['因此', '所以', '然而', '但是', '另外', '此外', '首先', '其次', '最后', 'therefore', 'however', 'moreover', 'furthermore', 'finally']
        transition_count = sum(1 for word in transition_words if word in content)
        transition_density = transition_count / len(paragraphs)
        coherence_score += min(0.3, transition_density * 0.1)
        
        # 主题一致性
        main_topics = self._extract_main_topics(content)
        if len(main_topics) <= 3:  # 主题集中
            coherence_score += 0.2
        
        return min(1.0, coherence_score)
    
    def _analyze_accuracy(self, content: str, context: Dict[str, Any]) -> float:
        """分析准确性"""
        # 简化的准确性分析
        accuracy_score = 0.8  # 默认准确性
        
        # 检查明显的错误指标
        error_indicators = ['错误', '不对', '有误', 'error', 'wrong', 'incorrect']
        error_count = sum(1 for indicator in error_indicators if indicator in content.lower())
        
        if error_count > 0:
            accuracy_score -= error_count * 0.1
        
        # 检查数据一致性
        numbers = re.findall(r'\d+', content)
        if len(numbers) > 5:  # 有足够的数字进行检查
            # 简单的数字一致性检查
            accuracy_score += 0.1
        
        # 检查引用和链接
        links = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', content)
        if links:
            accuracy_score += 0.1
        
        return max(0.0, min(1.0, accuracy_score))
    
    def _identify_quality_issues(self, content: str) -> List[str]:
        """识别质量问题"""
        issues = []
        
        # 长度问题
        word_count = len(self._extract_words(content))
        if word_count < 50:
            issues.append("内容过短")
        elif word_count > 2000:
            issues.append("内容过长，可能影响可读性")
        
        # 结构问题
        if not re.search(r'^#{1,6}\s+', content, re.MULTILINE):
            issues.append("缺少标题结构")
        
        # 重复内容
        sentences = re.split(r'[.!?。！？]+', content)
        sentence_counts = Counter(s.strip().lower() for s in sentences if s.strip())
        duplicates = [s for s, count in sentence_counts.items() if count > 1 and len(s) > 10]
        if duplicates:
            issues.append("存在重复内容")
        
        # 格式问题
        if re.search(r'\s{3,}', content):
            issues.append("存在多余的空格")
        
        return issues
    
    def _identify_strengths(self, content: str) -> List[str]:
        """识别内容优点"""
        strengths = []
        
        # 结构优点
        if re.search(r'^#{1,6}\s+', content, re.MULTILINE):
            strengths.append("具有清晰的标题结构")
        
        # 内容丰富度
        word_count = len(self._extract_words(content))
        if 200 <= word_count <= 1000:
            strengths.append("内容长度适中")
        
        # 代码示例
        if re.search(r'```[\s\S]*?```', content):
            strengths.append("包含代码示例")
        
        # 列表结构
        if re.search(r'^[-*+]\s+', content, re.MULTILINE):
            strengths.append("使用了列表结构")
        
        return strengths
    
    def _generate_quality_recommendations(
        self,
        readability: float,
        completeness: float,
        coherence: float,
        accuracy: float,
        issues: List[str]
    ) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        if readability < 0.7:
            recommendations.append("建议简化句子结构，提高可读性")
        
        if completeness < 0.7:
            recommendations.append("建议补充更多详细信息")
        
        if coherence < 0.7:
            recommendations.append("建议增加段落间的逻辑连接")
        
        if accuracy < 0.8:
            recommendations.append("建议核实内容准确性")
        
        if "内容过短" in issues:
            recommendations.append("建议扩展内容，增加更多细节")
        
        if "缺少标题结构" in issues:
            recommendations.append("建议添加标题和子标题")
        
        return recommendations

    def _has_document_title(self, lines: List[str]) -> bool:
        """检查是否有文档标题"""
        for line in lines[:5]:  # 检查前5行
            if line.strip() and (line.startswith('#') or self._looks_like_title(line.strip())):
                return True
        return False

    def _has_introduction(self, content: str) -> bool:
        """检查是否有介绍部分"""
        intro_keywords = ['介绍', '概述', '简介', '背景', 'introduction', 'overview', 'background']
        return any(keyword in content.lower() for keyword in intro_keywords)

    def _has_conclusion(self, content: str) -> bool:
        """检查是否有结论部分"""
        conclusion_keywords = ['结论', '总结', '小结', '结语', 'conclusion', 'summary', 'summary']
        return any(keyword in content.lower() for keyword in conclusion_keywords)

    def _count_sections(self, lines: List[str]) -> Tuple[int, int, int]:
        """计算章节数量"""
        section_count = 0
        subsection_count = 0
        max_depth = 0

        for line in lines:
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                max_depth = max(max_depth, level)

                if level <= 2:
                    section_count += 1
                else:
                    subsection_count += 1

        return section_count, subsection_count, max_depth

    def _calculate_structure_score(
        self,
        has_title: bool,
        has_introduction: bool,
        has_conclusion: bool,
        section_count: int,
        hierarchy_depth: int
    ) -> float:
        """计算结构分数"""
        score = 0.0

        if has_title:
            score += 0.2
        if has_introduction:
            score += 0.2
        if has_conclusion:
            score += 0.2

        if section_count > 0:
            score += 0.3

        if 1 <= hierarchy_depth <= 4:
            score += 0.1

        return min(1.0, score)

    def _identify_structure_issues(
        self,
        has_title: bool,
        has_introduction: bool,
        has_conclusion: bool,
        section_count: int,
        hierarchy_depth: int
    ) -> List[str]:
        """识别结构问题"""
        issues = []

        if not has_title:
            issues.append("缺少文档标题")
        if not has_introduction:
            issues.append("缺少介绍部分")
        if not has_conclusion:
            issues.append("缺少结论部分")
        if section_count == 0:
            issues.append("缺少章节结构")
        if hierarchy_depth > 5:
            issues.append("标题层级过深")

        return issues

    def _extract_main_topics(self, content: str) -> List[str]:
        """提取主要话题"""
        # 基于标题提取话题
        titles = re.findall(r'^#{1,6}\s+(.+)$', content, re.MULTILINE)

        # 基于关键词提取话题
        words = self._extract_words(content)
        word_freq = Counter(word.lower() for word in words if len(word) > 3 and word.lower() not in self.stop_words)

        # 合并标题和高频词
        topics = titles + [word for word, _ in word_freq.most_common(5)]

        return list(set(topics))[:10]  # 返回前10个唯一话题

    def _extract_key_concepts(self, content: str) -> List[str]:
        """提取关键概念"""
        words = self._extract_words(content)

        # 技术概念
        technical_concepts = [word for word in words if word.lower() in self.technical_indicators]

        # 高频概念
        word_freq = Counter(word.lower() for word in words if len(word) > 4 and word.lower() not in self.stop_words)
        frequent_concepts = [word for word, freq in word_freq.most_common(10) if freq > 1]

        # 合并并去重
        concepts = list(set(technical_concepts + frequent_concepts))

        return concepts[:15]  # 返回前15个概念

    def _analyze_sentiment(self, content: str) -> float:
        """分析情感倾向"""
        positive_words = self.quality_indicators['positive']
        negative_words = self.quality_indicators['negative']

        words = self._extract_words(content)
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)

        total_sentiment_words = positive_count + negative_count
        if total_sentiment_words == 0:
            return 0.5  # 中性

        sentiment_score = positive_count / total_sentiment_words
        return sentiment_score

    def _analyze_complexity(self, content: str) -> str:
        """分析复杂度级别"""
        words = self._extract_words(content)

        # 技术词汇密度
        technical_density = sum(1 for word in words if word.lower() in self.technical_indicators) / len(words) if words else 0

        # 平均词长
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0

        # 句子复杂度
        sentences = re.split(r'[.!?。！？]+', content)
        avg_sentence_length = sum(len(self._extract_words(s)) for s in sentences) / len(sentences) if sentences else 0

        # 综合判断
        if technical_density > 0.2 or avg_word_length > 6 or avg_sentence_length > 20:
            return "high"
        elif technical_density > 0.1 or avg_word_length > 5 or avg_sentence_length > 15:
            return "medium"
        else:
            return "low"

    def _classify_domain(self, content: str) -> str:
        """分类领域"""
        words = set(word.lower() for word in self._extract_words(content))

        # 技术领域指标
        tech_indicators = words & self.technical_indicators
        if len(tech_indicators) > 5:
            return "technical"

        # 其他领域可以扩展
        return "general"

    def _analyze_semantic_coherence(self, content: str) -> float:
        """分析语义连贯性"""
        paragraphs = content.split('\n\n')
        if len(paragraphs) < 2:
            return 0.8

        # 简化的语义连贯性分析
        coherence_score = 0.0

        # 词汇重叠分析
        total_overlap = 0
        for i in range(len(paragraphs) - 1):
            words1 = set(self._extract_words(paragraphs[i]))
            words2 = set(self._extract_words(paragraphs[i + 1]))
            if words1 and words2:
                overlap = len(words1 & words2) / len(words1 | words2)
                total_overlap += overlap

        coherence_score = total_overlap / (len(paragraphs) - 1) if len(paragraphs) > 1 else 0

        return min(1.0, coherence_score)

    def _extract_words(self, text: str) -> List[str]:
        """提取单词"""
        # 提取中英文单词
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]+\b', text)
        return [word for word in words if len(word) > 1]

    def _calculate_lexical_similarity(self, words1: List[str], words2: List[str]) -> float:
        """计算词汇相似度"""
        if not words1 or not words2:
            return 0.0

        set1 = set(word.lower() for word in words1)
        set2 = set(word.lower() for word in words2)

        intersection = len(set1 & set2)
        union = len(set1 | set2)

        return intersection / union if union > 0 else 0.0

    def _calculate_structure_similarity(self, content1: str, content2: str) -> float:
        """计算结构相似度"""
        struct1 = self.analyze_structure(content1)
        struct2 = self.analyze_structure(content2)

        # 比较结构特征
        similarities = []

        # 章节数量相似度
        if struct1.section_count > 0 and struct2.section_count > 0:
            section_sim = 1 - abs(struct1.section_count - struct2.section_count) / max(struct1.section_count, struct2.section_count)
            similarities.append(section_sim)

        # 层级深度相似度
        if struct1.hierarchy_depth > 0 and struct2.hierarchy_depth > 0:
            depth_sim = 1 - abs(struct1.hierarchy_depth - struct2.hierarchy_depth) / max(struct1.hierarchy_depth, struct2.hierarchy_depth)
            similarities.append(depth_sim)

        # 结构元素相似度
        struct_features1 = [struct1.has_title, struct1.has_introduction, struct1.has_conclusion]
        struct_features2 = [struct2.has_title, struct2.has_introduction, struct2.has_conclusion]
        feature_sim = sum(1 for f1, f2 in zip(struct_features1, struct_features2) if f1 == f2) / len(struct_features1)
        similarities.append(feature_sim)

        return sum(similarities) / len(similarities) if similarities else 0.0

    def _calculate_semantic_similarity(self, content1: str, content2: str) -> float:
        """计算语义相似度（简化版）"""
        # 基于主题重叠的简化语义相似度
        topics1 = set(self._extract_main_topics(content1))
        topics2 = set(self._extract_main_topics(content2))

        if not topics1 or not topics2:
            return 0.0

        intersection = len(topics1 & topics2)
        union = len(topics1 | topics2)

        return intersection / union if union > 0 else 0.0

    def _looks_like_title(self, text: str) -> bool:
        """判断文本是否看起来像标题"""
        return (
            len(text) < 100 and
            not text.endswith('.') and
            not text.endswith('。') and
            not text.startswith('-') and
            not text.startswith('*') and
            (text[0].isupper() or any('\u4e00' <= char <= '\u9fff' for char in text[:3]))
        )
