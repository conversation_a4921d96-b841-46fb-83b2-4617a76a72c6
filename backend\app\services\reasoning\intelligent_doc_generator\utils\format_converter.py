"""
格式转换工具

提供各种文档格式转换功能，包括：
- Markdown转换
- HTML转换
- 文本格式转换
- 结构化数据转换
"""

import re
import json
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class OutputFormat(str, Enum):
    """输出格式枚举"""
    MARKDOWN = "markdown"
    HTML = "html"
    PLAIN_TEXT = "plain_text"
    JSON = "json"
    XML = "xml"


@dataclass
class ConversionOptions:
    """转换选项"""
    include_toc: bool = False
    include_metadata: bool = False
    preserve_formatting: bool = True
    add_line_numbers: bool = False
    custom_css: Optional[str] = None
    custom_template: Optional[str] = None


class FormatConverter:
    """格式转换工具类"""
    
    def __init__(self):
        """初始化格式转换器"""
        self.markdown_patterns = {
            'header': r'^(#{1,6})\s+(.+)$',
            'bold': r'\*\*(.*?)\*\*',
            'italic': r'\*(.*?)\*',
            'code_inline': r'`([^`]+)`',
            'code_block': r'```(\w+)?\n(.*?)\n```',
            'link': r'\[([^\]]+)\]\(([^)]+)\)',
            'image': r'!\[([^\]]*)\]\(([^)]+)\)',
            'list_unordered': r'^[\s]*[-*+]\s+(.+)$',
            'list_ordered': r'^[\s]*\d+\.\s+(.+)$',
            'blockquote': r'^>\s+(.+)$',
            'horizontal_rule': r'^[-*_]{3,}$'
        }
    
    def convert_to_format(
        self,
        content: str,
        source_format: str,
        target_format: OutputFormat,
        options: Optional[ConversionOptions] = None
    ) -> str:
        """转换内容格式"""
        if not content:
            return ""
        
        options = options or ConversionOptions()
        
        # 根据源格式和目标格式选择转换方法
        if source_format == "markdown":
            if target_format == OutputFormat.HTML:
                return self.markdown_to_html(content, options)
            elif target_format == OutputFormat.PLAIN_TEXT:
                return self.markdown_to_text(content, options)
            elif target_format == OutputFormat.JSON:
                return self.markdown_to_json(content, options)
        elif source_format == "html":
            if target_format == OutputFormat.MARKDOWN:
                return self.html_to_markdown(content, options)
            elif target_format == OutputFormat.PLAIN_TEXT:
                return self.html_to_text(content, options)
        elif source_format == "plain_text":
            if target_format == OutputFormat.MARKDOWN:
                return self.text_to_markdown(content, options)
            elif target_format == OutputFormat.HTML:
                return self.text_to_html(content, options)
        
        # 如果没有匹配的转换方法，返回原内容
        logger.warning(f"不支持从 {source_format} 转换到 {target_format.value}")
        return content
    
    def markdown_to_html(self, markdown_content: str, options: ConversionOptions) -> str:
        """Markdown转HTML"""
        html_content = markdown_content
        
        # 转换标题
        html_content = re.sub(
            self.markdown_patterns['header'],
            lambda m: f'<h{len(m.group(1))}>{m.group(2)}</h{len(m.group(1))}>',
            html_content,
            flags=re.MULTILINE
        )
        
        # 转换粗体
        html_content = re.sub(
            self.markdown_patterns['bold'],
            r'<strong>\1</strong>',
            html_content
        )
        
        # 转换斜体
        html_content = re.sub(
            self.markdown_patterns['italic'],
            r'<em>\1</em>',
            html_content
        )
        
        # 转换行内代码
        html_content = re.sub(
            self.markdown_patterns['code_inline'],
            r'<code>\1</code>',
            html_content
        )
        
        # 转换代码块
        html_content = re.sub(
            self.markdown_patterns['code_block'],
            lambda m: f'<pre><code class="language-{m.group(1) or ""}">{m.group(2)}</code></pre>',
            html_content,
            flags=re.DOTALL
        )
        
        # 转换链接
        html_content = re.sub(
            self.markdown_patterns['link'],
            r'<a href="\2">\1</a>',
            html_content
        )
        
        # 转换图片
        html_content = re.sub(
            self.markdown_patterns['image'],
            r'<img src="\2" alt="\1">',
            html_content
        )
        
        # 转换无序列表
        html_content = self._convert_lists(html_content)
        
        # 转换引用
        html_content = re.sub(
            self.markdown_patterns['blockquote'],
            r'<blockquote>\1</blockquote>',
            html_content,
            flags=re.MULTILINE
        )
        
        # 转换水平线
        html_content = re.sub(
            self.markdown_patterns['horizontal_rule'],
            '<hr>',
            html_content,
            flags=re.MULTILINE
        )
        
        # 转换段落
        html_content = self._convert_paragraphs(html_content)
        
        # 包装在HTML文档中
        if options.include_metadata:
            html_content = self._wrap_in_html_document(html_content, options)
        
        return html_content
    
    def markdown_to_text(self, markdown_content: str, options: ConversionOptions) -> str:
        """Markdown转纯文本"""
        text_content = markdown_content
        
        # 移除Markdown语法
        # 移除标题标记
        text_content = re.sub(r'^#{1,6}\s+', '', text_content, flags=re.MULTILINE)
        
        # 移除粗体和斜体标记
        text_content = re.sub(r'\*\*(.*?)\*\*', r'\1', text_content)
        text_content = re.sub(r'\*(.*?)\*', r'\1', text_content)
        
        # 移除行内代码标记
        text_content = re.sub(r'`([^`]+)`', r'\1', text_content)
        
        # 移除代码块标记
        text_content = re.sub(r'```\w*\n(.*?)\n```', r'\1', text_content, flags=re.DOTALL)
        
        # 转换链接
        text_content = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text_content)
        
        # 移除图片
        text_content = re.sub(r'!\[([^\]]*)\]\([^)]+\)', r'[图片: \1]', text_content)
        
        # 移除列表标记
        text_content = re.sub(r'^[\s]*[-*+]\s+', '', text_content, flags=re.MULTILINE)
        text_content = re.sub(r'^[\s]*\d+\.\s+', '', text_content, flags=re.MULTILINE)
        
        # 移除引用标记
        text_content = re.sub(r'^>\s+', '', text_content, flags=re.MULTILINE)
        
        # 移除水平线
        text_content = re.sub(r'^[-*_]{3,}$', '', text_content, flags=re.MULTILINE)
        
        return text_content.strip()
    
    def markdown_to_json(self, markdown_content: str, options: ConversionOptions) -> str:
        """Markdown转JSON结构"""
        sections = self._parse_markdown_structure(markdown_content)
        
        document_structure = {
            "type": "document",
            "format": "markdown",
            "sections": sections,
            "metadata": {
                "converted_at": "2024-01-01T00:00:00Z",
                "total_sections": len(sections)
            } if options.include_metadata else {}
        }
        
        return json.dumps(document_structure, ensure_ascii=False, indent=2)
    
    def html_to_markdown(self, html_content: str, options: ConversionOptions) -> str:
        """HTML转Markdown"""
        markdown_content = html_content
        
        # 转换标题
        for i in range(1, 7):
            markdown_content = re.sub(
                f'<h{i}[^>]*>(.*?)</h{i}>',
                lambda m: f'{"#" * i} {m.group(1)}',
                markdown_content,
                flags=re.IGNORECASE | re.DOTALL
            )
        
        # 转换粗体
        markdown_content = re.sub(
            r'<(strong|b)[^>]*>(.*?)</\1>',
            r'**\2**',
            markdown_content,
            flags=re.IGNORECASE | re.DOTALL
        )
        
        # 转换斜体
        markdown_content = re.sub(
            r'<(em|i)[^>]*>(.*?)</\1>',
            r'*\2*',
            markdown_content,
            flags=re.IGNORECASE | re.DOTALL
        )
        
        # 转换代码
        markdown_content = re.sub(
            r'<code[^>]*>(.*?)</code>',
            r'`\1`',
            markdown_content,
            flags=re.IGNORECASE | re.DOTALL
        )
        
        # 转换代码块
        markdown_content = re.sub(
            r'<pre[^>]*><code[^>]*>(.*?)</code></pre>',
            r'```\n\1\n```',
            markdown_content,
            flags=re.IGNORECASE | re.DOTALL
        )
        
        # 转换链接
        markdown_content = re.sub(
            r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>',
            r'[\2](\1)',
            markdown_content,
            flags=re.IGNORECASE | re.DOTALL
        )
        
        # 转换图片
        markdown_content = re.sub(
            r'<img[^>]*src=["\']([^"\']*)["\'][^>]*alt=["\']([^"\']*)["\'][^>]*>',
            r'![\2](\1)',
            markdown_content,
            flags=re.IGNORECASE
        )
        
        # 移除HTML标签
        markdown_content = re.sub(r'<[^>]+>', '', markdown_content)
        
        # 清理多余的空行
        markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)
        
        return markdown_content.strip()
    
    def html_to_text(self, html_content: str, options: ConversionOptions) -> str:
        """HTML转纯文本"""
        # 先转换为Markdown，再转换为文本
        markdown_content = self.html_to_markdown(html_content, options)
        return self.markdown_to_text(markdown_content, options)
    
    def text_to_markdown(self, text_content: str, options: ConversionOptions) -> str:
        """纯文本转Markdown"""
        markdown_content = text_content
        
        # 检测并转换标题（基于行长度和位置）
        lines = markdown_content.split('\n')
        processed_lines = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped and self._looks_like_title(stripped, lines, i):
                # 根据上下文确定标题级别
                level = self._determine_title_level(stripped, lines, i)
                processed_lines.append(f'{"#" * level} {stripped}')
            else:
                processed_lines.append(line)
        
        return '\n'.join(processed_lines)
    
    def text_to_html(self, text_content: str, options: ConversionOptions) -> str:
        """纯文本转HTML"""
        # 先转换为Markdown，再转换为HTML
        markdown_content = self.text_to_markdown(text_content, options)
        return self.markdown_to_html(markdown_content, options)
    
    def _convert_lists(self, content: str) -> str:
        """转换列表"""
        lines = content.split('\n')
        processed_lines = []
        in_list = False
        list_type = None
        
        for line in lines:
            unordered_match = re.match(self.markdown_patterns['list_unordered'], line)
            ordered_match = re.match(self.markdown_patterns['list_ordered'], line)
            
            if unordered_match:
                if not in_list or list_type != 'ul':
                    if in_list:
                        processed_lines.append(f'</{list_type}>')
                    processed_lines.append('<ul>')
                    in_list = True
                    list_type = 'ul'
                processed_lines.append(f'<li>{unordered_match.group(1)}</li>')
            elif ordered_match:
                if not in_list or list_type != 'ol':
                    if in_list:
                        processed_lines.append(f'</{list_type}>')
                    processed_lines.append('<ol>')
                    in_list = True
                    list_type = 'ol'
                processed_lines.append(f'<li>{ordered_match.group(1)}</li>')
            else:
                if in_list:
                    processed_lines.append(f'</{list_type}>')
                    in_list = False
                    list_type = None
                processed_lines.append(line)
        
        if in_list:
            processed_lines.append(f'</{list_type}>')
        
        return '\n'.join(processed_lines)
    
    def _convert_paragraphs(self, content: str) -> str:
        """转换段落"""
        # 简单的段落转换
        paragraphs = content.split('\n\n')
        html_paragraphs = []
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if paragraph and not paragraph.startswith('<'):
                html_paragraphs.append(f'<p>{paragraph}</p>')
            else:
                html_paragraphs.append(paragraph)
        
        return '\n\n'.join(html_paragraphs)
    
    def _wrap_in_html_document(self, content: str, options: ConversionOptions) -> str:
        """包装在HTML文档中"""
        css = options.custom_css or """
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
            h1, h2, h3, h4, h5, h6 { color: #333; }
            code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
            pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
            blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
        </style>
        """
        
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转换文档</title>
    {css}
</head>
<body>
{content}
</body>
</html>"""
    
    def _parse_markdown_structure(self, markdown_content: str) -> List[Dict[str, Any]]:
        """解析Markdown结构"""
        sections = []
        lines = markdown_content.split('\n')
        current_section = None
        
        for line in lines:
            header_match = re.match(self.markdown_patterns['header'], line)
            if header_match:
                if current_section:
                    sections.append(current_section)
                
                current_section = {
                    "type": "section",
                    "level": len(header_match.group(1)),
                    "title": header_match.group(2),
                    "content": []
                }
            elif current_section:
                current_section["content"].append(line)
            else:
                # 文档开头的内容
                if not sections:
                    sections.append({
                        "type": "section",
                        "level": 0,
                        "title": "前言",
                        "content": [line]
                    })
                else:
                    sections[-1]["content"].append(line)
        
        if current_section:
            sections.append(current_section)
        
        # 清理内容
        for section in sections:
            section["content"] = '\n'.join(section["content"]).strip()
        
        return sections
    
    def _looks_like_title(self, text: str, lines: List[str], index: int) -> bool:
        """判断文本是否看起来像标题"""
        # 基本条件
        if len(text) > 100 or text.endswith('.') or text.endswith('。'):
            return False
        
        # 检查是否在文档开头或前面有空行
        if index == 0 or (index > 0 and not lines[index - 1].strip()):
            return True
        
        # 检查是否后面有空行
        if index < len(lines) - 1 and not lines[index + 1].strip():
            return True
        
        return False
    
    def _determine_title_level(self, text: str, lines: List[str], index: int) -> int:
        """确定标题级别"""
        # 简单的标题级别判断
        if index == 0:
            return 1  # 文档标题
        
        # 根据文本长度判断
        if len(text) < 20:
            return 2
        elif len(text) < 40:
            return 3
        else:
            return 4
