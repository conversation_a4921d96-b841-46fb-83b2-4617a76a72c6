"""
文本处理工具

提供各种文本处理功能，包括：
- 文本清理和格式化
- 文本分析和统计
- 文本转换和优化
- 多语言支持
"""

import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TextStatistics:
    """文本统计信息"""
    word_count: int
    character_count: int
    character_count_no_spaces: int
    sentence_count: int
    paragraph_count: int
    line_count: int
    average_words_per_sentence: float
    average_sentences_per_paragraph: float
    readability_score: float


class TextProcessor:
    """文本处理工具类"""
    
    def __init__(self):
        """初始化文本处理器"""
        self.chinese_punctuation = "，。！？；：""''（）【】《》"
        self.english_punctuation = ",.!?;:\"'()[]{}<>"
        
    def clean_text(self, text: str, options: Optional[Dict[str, Any]] = None) -> str:
        """清理文本"""
        if not text:
            return ""
        
        options = options or {}
        cleaned_text = text
        
        # 移除多余的空白字符
        if options.get("remove_extra_whitespace", True):
            cleaned_text = self._remove_extra_whitespace(cleaned_text)
        
        # 标准化换行符
        if options.get("normalize_line_breaks", True):
            cleaned_text = self._normalize_line_breaks(cleaned_text)
        
        # 移除特殊字符
        if options.get("remove_special_chars", False):
            cleaned_text = self._remove_special_characters(cleaned_text)
        
        # 修正标点符号
        if options.get("fix_punctuation", True):
            cleaned_text = self._fix_punctuation(cleaned_text)
        
        # 移除HTML标签
        if options.get("remove_html_tags", True):
            cleaned_text = self._remove_html_tags(cleaned_text)
        
        return cleaned_text.strip()
    
    def _remove_extra_whitespace(self, text: str) -> str:
        """移除多余的空白字符"""
        # 移除行首行尾空白
        lines = text.split('\n')
        cleaned_lines = [line.rstrip() for line in lines]
        
        # 合并多个连续空行为单个空行
        result_lines = []
        prev_empty = False
        
        for line in cleaned_lines:
            if line.strip():
                result_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                result_lines.append('')
                prev_empty = True
        
        return '\n'.join(result_lines)
    
    def _normalize_line_breaks(self, text: str) -> str:
        """标准化换行符"""
        # 统一换行符为\n
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除段落内的单独换行符（保留段落间的空行）
        paragraphs = text.split('\n\n')
        normalized_paragraphs = []
        
        for paragraph in paragraphs:
            # 将段落内的换行符替换为空格
            normalized_paragraph = ' '.join(line.strip() for line in paragraph.split('\n') if line.strip())
            if normalized_paragraph:
                normalized_paragraphs.append(normalized_paragraph)
        
        return '\n\n'.join(normalized_paragraphs)
    
    def _remove_special_characters(self, text: str) -> str:
        """移除特殊字符"""
        # 保留中英文字符、数字、基本标点符号和空白字符
        pattern = r'[^\w\s\u4e00-\u9fff' + re.escape(self.chinese_punctuation + self.english_punctuation) + r']'
        return re.sub(pattern, '', text)
    
    def _fix_punctuation(self, text: str) -> str:
        """修正标点符号"""
        # 修正标点符号前后的空格
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)  # 移除标点前的空格
        text = re.sub(r'([,.!?;:])(?=[^\s])', r'\1 ', text)  # 在标点后添加空格
        
        # 修正中文标点符号
        text = re.sub(r'\s+([，。！？；：])', r'\1', text)  # 移除中文标点前的空格
        text = re.sub(r'([，。！？；：])\s+', r'\1', text)  # 移除中文标点后的空格
        
        return text
    
    def _remove_html_tags(self, text: str) -> str:
        """移除HTML标签"""
        # 简单的HTML标签移除
        clean_text = re.sub(r'<[^>]+>', '', text)
        
        # 解码HTML实体
        html_entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&nbsp;': ' '
        }
        
        for entity, char in html_entities.items():
            clean_text = clean_text.replace(entity, char)
        
        return clean_text
    
    def analyze_text(self, text: str) -> TextStatistics:
        """分析文本统计信息"""
        if not text:
            return TextStatistics(0, 0, 0, 0, 0, 0, 0.0, 0.0, 0.0)
        
        # 基本统计
        word_count = self._count_words(text)
        character_count = len(text)
        character_count_no_spaces = len(text.replace(' ', '').replace('\n', '').replace('\t', ''))
        sentence_count = self._count_sentences(text)
        paragraph_count = self._count_paragraphs(text)
        line_count = len(text.split('\n'))
        
        # 计算平均值
        average_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0
        average_sentences_per_paragraph = sentence_count / paragraph_count if paragraph_count > 0 else 0
        
        # 计算可读性分数（简化版）
        readability_score = self._calculate_readability_score(text, word_count, sentence_count)
        
        return TextStatistics(
            word_count=word_count,
            character_count=character_count,
            character_count_no_spaces=character_count_no_spaces,
            sentence_count=sentence_count,
            paragraph_count=paragraph_count,
            line_count=line_count,
            average_words_per_sentence=average_words_per_sentence,
            average_sentences_per_paragraph=average_sentences_per_paragraph,
            readability_score=readability_score
        )
    
    def _count_words(self, text: str) -> int:
        """计算单词数"""
        # 中英文混合文本的单词计数
        # 英文单词
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
        
        # 中文字符（每个中文字符算作一个词）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        
        return english_words + chinese_chars
    
    def _count_sentences(self, text: str) -> int:
        """计算句子数"""
        # 基于标点符号分割句子
        sentence_endings = r'[.!?。！？]+'
        sentences = re.split(sentence_endings, text)
        
        # 过滤空句子
        non_empty_sentences = [s.strip() for s in sentences if s.strip()]
        
        return len(non_empty_sentences)
    
    def _count_paragraphs(self, text: str) -> int:
        """计算段落数"""
        paragraphs = text.split('\n\n')
        non_empty_paragraphs = [p.strip() for p in paragraphs if p.strip()]
        return len(non_empty_paragraphs)
    
    def _calculate_readability_score(self, text: str, word_count: int, sentence_count: int) -> float:
        """计算可读性分数（简化版Flesch Reading Ease）"""
        if sentence_count == 0 or word_count == 0:
            return 0.0
        
        # 计算平均句长
        average_sentence_length = word_count / sentence_count
        
        # 简化的可读性计算（不考虑音节数）
        # 分数越高表示越容易阅读
        readability_score = 206.835 - (1.015 * average_sentence_length)
        
        # 限制分数范围在0-100之间
        return max(0.0, min(100.0, readability_score))
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        if not text:
            return []
        
        # 简单的关键词提取（基于词频）
        # 移除停用词
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
        }
        
        # 提取单词
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]+\b', text.lower())
        
        # 过滤停用词和短词
        filtered_words = [word for word in words if len(word) > 1 and word not in stop_words]
        
        # 计算词频
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序并返回前N个
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        return [word for word, freq in sorted_words[:max_keywords]]
    
    def split_into_sentences(self, text: str) -> List[str]:
        """将文本分割为句子"""
        if not text:
            return []
        
        # 基于标点符号分割
        sentence_pattern = r'[.!?。！？]+\s*'
        sentences = re.split(sentence_pattern, text)
        
        # 清理和过滤
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def split_into_paragraphs(self, text: str) -> List[str]:
        """将文本分割为段落"""
        if not text:
            return []
        
        paragraphs = text.split('\n\n')
        cleaned_paragraphs = []
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if paragraph:
                cleaned_paragraphs.append(paragraph)
        
        return cleaned_paragraphs
    
    def truncate_text(self, text: str, max_length: int, suffix: str = "...") -> str:
        """截断文本"""
        if not text or len(text) <= max_length:
            return text
        
        # 尝试在单词边界截断
        truncated = text[:max_length - len(suffix)]
        
        # 查找最后一个空格或标点符号
        last_space = max(
            truncated.rfind(' '),
            truncated.rfind('。'),
            truncated.rfind('，'),
            truncated.rfind('.'),
            truncated.rfind(',')
        )
        
        if last_space > max_length * 0.8:  # 如果截断点不会丢失太多内容
            truncated = truncated[:last_space]
        
        return truncated + suffix
    
    def format_text(self, text: str, format_options: Dict[str, Any]) -> str:
        """格式化文本"""
        if not text:
            return ""
        
        formatted_text = text
        
        # 首行缩进
        if format_options.get("indent_first_line", False):
            formatted_text = self._add_first_line_indent(formatted_text)
        
        # 段落间距
        if format_options.get("paragraph_spacing", False):
            formatted_text = self._add_paragraph_spacing(formatted_text)
        
        # 标题格式化
        if format_options.get("format_titles", False):
            formatted_text = self._format_titles(formatted_text)
        
        return formatted_text
    
    def _add_first_line_indent(self, text: str) -> str:
        """添加首行缩进"""
        paragraphs = text.split('\n\n')
        indented_paragraphs = []
        
        for paragraph in paragraphs:
            if paragraph.strip():
                indented_paragraphs.append('    ' + paragraph)
            else:
                indented_paragraphs.append(paragraph)
        
        return '\n\n'.join(indented_paragraphs)
    
    def _add_paragraph_spacing(self, text: str) -> str:
        """添加段落间距"""
        # 确保段落间有双换行
        text = re.sub(r'\n{3,}', '\n\n', text)  # 移除多余的换行
        text = re.sub(r'\n(?!\n)', '\n\n', text)  # 单换行变双换行
        
        return text
    
    def _format_titles(self, text: str) -> str:
        """格式化标题"""
        lines = text.split('\n')
        formatted_lines = []
        
        for line in lines:
            stripped = line.strip()
            if stripped and self._looks_like_title(stripped):
                # 为标题添加Markdown格式
                if not stripped.startswith('#'):
                    formatted_lines.append(f"## {stripped}")
                else:
                    formatted_lines.append(line)
            else:
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def _looks_like_title(self, text: str) -> bool:
        """判断文本是否看起来像标题"""
        return (
            len(text) < 100 and
            not text.endswith('.') and
            not text.endswith('。') and
            not text.startswith('-') and
            not text.startswith('*') and
            text[0].isupper() or any('\u4e00' <= char <= '\u9fff' for char in text[:3])
        )
