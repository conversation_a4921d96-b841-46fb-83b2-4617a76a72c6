"""
验证工具

提供各种验证功能，包括：
- 输入参数验证
- 内容格式验证
- 数据完整性验证
- 业务规则验证
"""

import re
import json
import logging
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ValidationLevel(str, Enum):
    """验证级别"""
    STRICT = "strict"
    NORMAL = "normal"
    LENIENT = "lenient"


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]
    validation_level: ValidationLevel
    details: Dict[str, Any]


@dataclass
class ValidationRule:
    """验证规则"""
    name: str
    description: str
    validator: Callable[[Any], bool]
    error_message: str
    level: ValidationLevel = ValidationLevel.NORMAL


class ValidationUtils:
    """验证工具类"""
    
    def __init__(self):
        """初始化验证工具"""
        self.common_rules = self._initialize_common_rules()
        
    def validate_input_parameters(
        self,
        parameters: Dict[str, Any],
        required_fields: List[str],
        optional_fields: Optional[List[str]] = None,
        validation_level: ValidationLevel = ValidationLevel.NORMAL
    ) -> ValidationResult:
        """验证输入参数"""
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            suggestions=[],
            validation_level=validation_level,
            details={}
        )
        
        optional_fields = optional_fields or []
        
        # 检查必需字段
        for field in required_fields:
            if field not in parameters:
                result.errors.append(f"缺少必需字段: {field}")
                result.is_valid = False
            elif parameters[field] is None:
                result.errors.append(f"必需字段不能为空: {field}")
                result.is_valid = False
            elif isinstance(parameters[field], str) and not parameters[field].strip():
                result.errors.append(f"必需字段不能为空字符串: {field}")
                result.is_valid = False
        
        # 检查未知字段
        all_known_fields = set(required_fields + optional_fields)
        unknown_fields = set(parameters.keys()) - all_known_fields
        
        if unknown_fields:
            if validation_level == ValidationLevel.STRICT:
                result.errors.append(f"包含未知字段: {list(unknown_fields)}")
                result.is_valid = False
            else:
                result.warnings.append(f"包含未知字段: {list(unknown_fields)}")
        
        # 类型验证
        result.details["field_types"] = {k: type(v).__name__ for k, v in parameters.items()}
        
        return result
    
    def validate_content_format(
        self,
        content: str,
        expected_format: str,
        validation_level: ValidationLevel = ValidationLevel.NORMAL
    ) -> ValidationResult:
        """验证内容格式"""
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            suggestions=[],
            validation_level=validation_level,
            details={}
        )
        
        if not content:
            result.errors.append("内容不能为空")
            result.is_valid = False
            return result
        
        # 根据期望格式进行验证
        if expected_format.lower() == "markdown":
            self._validate_markdown_format(content, result)
        elif expected_format.lower() == "json":
            self._validate_json_format(content, result)
        elif expected_format.lower() == "html":
            self._validate_html_format(content, result)
        elif expected_format.lower() == "plain_text":
            self._validate_plain_text_format(content, result)
        else:
            result.warnings.append(f"未知的格式类型: {expected_format}")
        
        return result
    
    def validate_document_structure(
        self,
        content: str,
        required_sections: Optional[List[str]] = None,
        validation_level: ValidationLevel = ValidationLevel.NORMAL
    ) -> ValidationResult:
        """验证文档结构"""
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            suggestions=[],
            validation_level=validation_level,
            details={}
        )
        
        if not content:
            result.errors.append("文档内容不能为空")
            result.is_valid = False
            return result
        
        # 检查基本结构
        structure_analysis = self._analyze_document_structure(content)
        result.details["structure_analysis"] = structure_analysis
        
        # 验证必需章节
        if required_sections:
            missing_sections = self._check_required_sections(content, required_sections)
            if missing_sections:
                if validation_level == ValidationLevel.STRICT:
                    result.errors.append(f"缺少必需章节: {missing_sections}")
                    result.is_valid = False
                else:
                    result.warnings.append(f"建议添加章节: {missing_sections}")
        
        # 结构质量检查
        if structure_analysis["title_count"] == 0:
            result.warnings.append("文档缺少标题结构")
        
        if structure_analysis["paragraph_count"] < 2:
            result.warnings.append("文档段落过少")
        
        if structure_analysis["max_title_level"] > 6:
            result.warnings.append("标题层级过深")
        
        return result
    
    def validate_business_rules(
        self,
        data: Dict[str, Any],
        rules: List[ValidationRule],
        validation_level: ValidationLevel = ValidationLevel.NORMAL
    ) -> ValidationResult:
        """验证业务规则"""
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            suggestions=[],
            validation_level=validation_level,
            details={}
        )
        
        rule_results = {}
        
        for rule in rules:
            try:
                is_rule_valid = rule.validator(data)
                rule_results[rule.name] = is_rule_valid
                
                if not is_rule_valid:
                    if rule.level == ValidationLevel.STRICT or validation_level == ValidationLevel.STRICT:
                        result.errors.append(f"{rule.name}: {rule.error_message}")
                        result.is_valid = False
                    else:
                        result.warnings.append(f"{rule.name}: {rule.error_message}")
                        
            except Exception as e:
                logger.error(f"验证规则 {rule.name} 执行失败: {str(e)}")
                result.warnings.append(f"验证规则 {rule.name} 执行失败")
        
        result.details["rule_results"] = rule_results
        
        return result
    
    def validate_data_completeness(
        self,
        data: Dict[str, Any],
        completeness_requirements: Dict[str, Any],
        validation_level: ValidationLevel = ValidationLevel.NORMAL
    ) -> ValidationResult:
        """验证数据完整性"""
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            suggestions=[],
            validation_level=validation_level,
            details={}
        )
        
        completeness_score = 0.0
        total_requirements = len(completeness_requirements)
        
        for requirement, expected_value in completeness_requirements.items():
            if requirement in data:
                actual_value = data[requirement]
                
                # 检查值的完整性
                if self._is_value_complete(actual_value, expected_value):
                    completeness_score += 1.0
                else:
                    if validation_level == ValidationLevel.STRICT:
                        result.errors.append(f"字段 {requirement} 的值不完整")
                        result.is_valid = False
                    else:
                        result.warnings.append(f"字段 {requirement} 的值可能不完整")
                        completeness_score += 0.5
            else:
                if validation_level == ValidationLevel.STRICT:
                    result.errors.append(f"缺少必需字段: {requirement}")
                    result.is_valid = False
                else:
                    result.warnings.append(f"建议添加字段: {requirement}")
        
        # 计算完整性分数
        final_completeness_score = completeness_score / total_requirements if total_requirements > 0 else 1.0
        result.details["completeness_score"] = final_completeness_score
        
        if final_completeness_score < 0.8:
            result.suggestions.append("建议补充更多信息以提高数据完整性")
        
        return result
    
    def _initialize_common_rules(self) -> Dict[str, ValidationRule]:
        """初始化常用验证规则"""
        rules = {}
        
        # 项目名称规则
        rules["project_name_valid"] = ValidationRule(
            name="project_name_valid",
            description="项目名称应该是有效的字符串",
            validator=lambda data: isinstance(data.get("project_name"), str) and len(data.get("project_name", "").strip()) > 0,
            error_message="项目名称必须是非空字符串"
        )
        
        # 内容长度规则
        rules["content_length_adequate"] = ValidationRule(
            name="content_length_adequate",
            description="内容长度应该足够",
            validator=lambda data: len(data.get("content", "")) >= 50,
            error_message="内容长度过短，应至少包含50个字符"
        )
        
        # 目标受众规则
        rules["target_audience_specified"] = ValidationRule(
            name="target_audience_specified",
            description="应指定目标受众",
            validator=lambda data: "target_audience" in data and data["target_audience"],
            error_message="应指定目标受众",
            level=ValidationLevel.LENIENT
        )
        
        return rules
    
    def _validate_markdown_format(self, content: str, result: ValidationResult):
        """验证Markdown格式"""
        # 检查标题格式
        invalid_titles = re.findall(r'^#{7,}', content, re.MULTILINE)
        if invalid_titles:
            result.warnings.append("发现过深的标题层级（超过6级）")
        
        # 检查代码块格式
        code_blocks = re.findall(r'```[\s\S]*?```', content)
        unclosed_blocks = content.count('```') % 2
        if unclosed_blocks != 0:
            result.errors.append("代码块未正确闭合")
            result.is_valid = False
        
        # 检查链接格式
        invalid_links = re.findall(r'\[([^\]]*)\]\([^)]*\)', content)
        for link_text, in re.findall(r'\[([^\]]*)\]\(([^)]*)\)', content):
            if not link_text.strip():
                result.warnings.append("发现空的链接文本")
    
    def _validate_json_format(self, content: str, result: ValidationResult):
        """验证JSON格式"""
        try:
            json.loads(content)
        except json.JSONDecodeError as e:
            result.errors.append(f"JSON格式错误: {str(e)}")
            result.is_valid = False
    
    def _validate_html_format(self, content: str, result: ValidationResult):
        """验证HTML格式"""
        # 简单的HTML标签检查
        open_tags = re.findall(r'<([a-zA-Z][^>]*)>', content)
        close_tags = re.findall(r'</([a-zA-Z][^>]*)>', content)
        
        # 检查标签是否匹配
        open_tag_names = [tag.split()[0] for tag in open_tags]
        close_tag_names = close_tags
        
        # 自闭合标签
        self_closing = {'img', 'br', 'hr', 'input', 'meta', 'link'}
        open_tag_names = [tag for tag in open_tag_names if tag not in self_closing]
        
        if len(open_tag_names) != len(close_tag_names):
            result.warnings.append("HTML标签可能未正确闭合")
    
    def _validate_plain_text_format(self, content: str, result: ValidationResult):
        """验证纯文本格式"""
        # 检查是否包含HTML标签
        if re.search(r'<[^>]+>', content):
            result.warnings.append("纯文本中包含HTML标签")
        
        # 检查是否包含Markdown语法
        if re.search(r'#{1,6}\s+|```|\*\*.*\*\*|\[.*\]\(.*\)', content):
            result.warnings.append("纯文本中包含Markdown语法")
    
    def _analyze_document_structure(self, content: str) -> Dict[str, Any]:
        """分析文档结构"""
        structure = {
            "title_count": 0,
            "paragraph_count": 0,
            "max_title_level": 0,
            "has_code_blocks": False,
            "has_lists": False,
            "has_links": False
        }
        
        # 统计标题
        titles = re.findall(r'^(#{1,6})\s+', content, re.MULTILINE)
        structure["title_count"] = len(titles)
        if titles:
            structure["max_title_level"] = max(len(title) for title in titles)
        
        # 统计段落
        paragraphs = content.split('\n\n')
        structure["paragraph_count"] = len([p for p in paragraphs if p.strip()])
        
        # 检查其他元素
        structure["has_code_blocks"] = bool(re.search(r'```[\s\S]*?```', content))
        structure["has_lists"] = bool(re.search(r'^[-*+]\s+', content, re.MULTILINE))
        structure["has_links"] = bool(re.search(r'\[.*?\]\(.*?\)', content))
        
        return structure
    
    def _check_required_sections(self, content: str, required_sections: List[str]) -> List[str]:
        """检查必需章节"""
        content_lower = content.lower()
        missing_sections = []
        
        for section in required_sections:
            section_patterns = [
                f"#{1,6}\\s+.*{section.lower()}",  # Markdown标题
                f"^.*{section.lower()}.*$"  # 普通文本行
            ]
            
            found = any(re.search(pattern, content_lower, re.MULTILINE) for pattern in section_patterns)
            if not found:
                missing_sections.append(section)
        
        return missing_sections
    
    def _is_value_complete(self, actual_value: Any, expected_value: Any) -> bool:
        """检查值是否完整"""
        if expected_value is None:
            return actual_value is not None
        
        if isinstance(expected_value, dict):
            if not isinstance(actual_value, dict):
                return False
            
            # 检查字典的完整性
            for key, expected_sub_value in expected_value.items():
                if key not in actual_value:
                    return False
                if not self._is_value_complete(actual_value[key], expected_sub_value):
                    return False
            return True
        
        elif isinstance(expected_value, list):
            if not isinstance(actual_value, list):
                return False
            
            # 检查列表长度
            expected_length = len(expected_value)
            return len(actual_value) >= expected_length
        
        elif isinstance(expected_value, str):
            if not isinstance(actual_value, str):
                return False
            
            # 检查字符串长度
            return len(actual_value.strip()) >= len(expected_value.strip())
        
        else:
            # 其他类型的基本检查
            return actual_value is not None and actual_value != ""
    
    def get_common_rule(self, rule_name: str) -> Optional[ValidationRule]:
        """获取常用验证规则"""
        return self.common_rules.get(rule_name)
    
    def get_all_common_rules(self) -> List[ValidationRule]:
        """获取所有常用验证规则"""
        return list(self.common_rules.values())
    
    def create_custom_rule(
        self,
        name: str,
        description: str,
        validator: Callable[[Any], bool],
        error_message: str,
        level: ValidationLevel = ValidationLevel.NORMAL
    ) -> ValidationRule:
        """创建自定义验证规则"""
        return ValidationRule(
            name=name,
            description=description,
            validator=validator,
            error_message=error_message,
            level=level
        )
