"""
生成工作流

管理文档内容生成阶段的完整流程，包括：
- 生成器选择
- 并发生成
- 内容整合
- 质量检查
- 结果输出
"""

import uuid
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum

from ..models.planning_models import DocumentPlan, SectionPlan
from ..models.generation_models import GenerationRequest, GenerationResult, ContentType, GenerationStrategy
from ..models.workflow_models import WorkflowStep, WorkflowResult, WorkflowStatus
from ..generators import BaseGenerator, StructuredGenerator, NarrativeGenerator, TechnicalGenerator, CreativeGenerator

logger = logging.getLogger(__name__)


class GenerationStage(str, Enum):
    """生成阶段枚举"""
    GENERATOR_SELECTION = "generator_selection"
    CONTENT_GENERATION = "content_generation"
    QUALITY_CHECK = "quality_check"
    CONTENT_INTEGRATION = "content_integration"
    RESULT_FINALIZATION = "result_finalization"


class GenerationWorkflow:
    """生成工作流管理器"""
    
    def __init__(self):
        """初始化生成工作流"""
        self.workflow_id = str(uuid.uuid4())
        self.current_stage = None
        self.workflow_status = WorkflowStatus.NOT_STARTED
        self.steps: List[WorkflowStep] = []
        self.results: Dict[str, Any] = {}
        
        # 初始化生成器
        self.generators = {
            GenerationStrategy.STRUCTURED: StructuredGenerator(),
            GenerationStrategy.NARRATIVE: NarrativeGenerator(),
            GenerationStrategy.TECHNICAL: TechnicalGenerator(),
            GenerationStrategy.CREATIVE: CreativeGenerator()
        }
    
    async def execute_generation_workflow(
        self,
        document_plan: DocumentPlan,
        generation_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行完整的生成工作流"""
        try:
            self.workflow_status = WorkflowStatus.RUNNING
            logger.info(f"开始执行生成工作流: {self.workflow_id}")
            
            # 阶段1: 生成器选择
            generator_assignments = await self._execute_generator_selection(document_plan, generation_config)
            
            # 阶段2: 内容生成
            generation_results = await self._execute_content_generation(document_plan, generator_assignments, generation_config)
            
            # 阶段3: 质量检查
            quality_results = await self._execute_quality_check(generation_results, generation_config)
            
            # 阶段4: 内容整合
            integrated_results = await self._execute_content_integration(quality_results, document_plan)
            
            # 阶段5: 结果最终化
            final_results = await self._execute_result_finalization(integrated_results, document_plan)
            
            self.workflow_status = WorkflowStatus.COMPLETED
            logger.info(f"生成工作流执行完成: {self.workflow_id}")
            
            return final_results
            
        except Exception as e:
            self.workflow_status = WorkflowStatus.FAILED
            logger.error(f"生成工作流执行失败: {str(e)}")
            raise
    
    async def _execute_generator_selection(
        self,
        document_plan: DocumentPlan,
        generation_config: Dict[str, Any]
    ) -> Dict[str, GenerationStrategy]:
        """执行生成器选择阶段"""
        self.current_stage = GenerationStage.GENERATOR_SELECTION
        step = WorkflowStep(
            step_id="generator_selection",
            name="生成器选择",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行生成器选择")
            
            generator_assignments = {}
            
            for section in document_plan.sections:
                # 根据章节类型和特征选择最适合的生成器
                strategy = self._select_generation_strategy(section, generation_config)
                generator_assignments[section.section_id] = strategy
            
            self.results["generator_assignments"] = generator_assignments
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"assignments": generator_assignments}
            
            logger.info(f"生成器选择完成，分配了{len(generator_assignments)}个生成任务")
            return generator_assignments
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"生成器选择失败: {str(e)}")
            raise
    
    async def _execute_content_generation(
        self,
        document_plan: DocumentPlan,
        generator_assignments: Dict[str, GenerationStrategy],
        generation_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行内容生成阶段"""
        self.current_stage = GenerationStage.CONTENT_GENERATION
        step = WorkflowStep(
            step_id="content_generation",
            name="内容生成",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行内容生成")
            
            # 准备生成任务
            generation_tasks = []
            for section in document_plan.sections:
                strategy = generator_assignments[section.section_id]
                task = self._create_generation_task(section, strategy, document_plan, generation_config)
                generation_tasks.append(task)
            
            # 并发执行生成任务
            generation_results = {}
            if generation_config.get("parallel_generation", True):
                results = await asyncio.gather(*generation_tasks, return_exceptions=True)
                for i, result in enumerate(results):
                    section_id = document_plan.sections[i].section_id
                    if isinstance(result, Exception):
                        logger.error(f"章节 {section_id} 生成失败: {str(result)}")
                        continue
                    generation_results[section_id] = result
            else:
                # 串行执行
                for i, task in enumerate(generation_tasks):
                    section_id = document_plan.sections[i].section_id
                    try:
                        result = await task
                        generation_results[section_id] = result
                    except Exception as e:
                        logger.error(f"章节 {section_id} 生成失败: {str(e)}")
                        continue
            
            self.results["generation_results"] = generation_results
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "generated_sections": len(generation_results),
                "total_sections": len(document_plan.sections),
                "success_rate": len(generation_results) / len(document_plan.sections)
            }
            
            logger.info(f"内容生成完成，成功生成{len(generation_results)}个章节")
            return generation_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"内容生成失败: {str(e)}")
            raise
    
    async def _execute_quality_check(
        self,
        generation_results: Dict[str, GenerationResult],
        generation_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行质量检查阶段"""
        self.current_stage = GenerationStage.QUALITY_CHECK
        step = WorkflowStep(
            step_id="quality_check",
            name="质量检查",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行质量检查")
            
            quality_threshold = generation_config.get("quality_threshold", 0.7)
            quality_results = {}
            quality_issues = []
            
            for section_id, result in generation_results.items():
                if result.quality_score >= quality_threshold:
                    quality_results[section_id] = result
                else:
                    quality_issues.append({
                        "section_id": section_id,
                        "quality_score": result.quality_score,
                        "threshold": quality_threshold
                    })
                    
                    # 尝试重新生成质量不达标的内容
                    if generation_config.get("auto_regenerate", True):
                        logger.warning(f"章节 {section_id} 质量不达标，尝试重新生成")
                        # 这里可以实现重新生成逻辑
                        quality_results[section_id] = result  # 暂时保留原结果
                    else:
                        quality_results[section_id] = result
            
            self.results["quality_results"] = quality_results
            self.results["quality_issues"] = quality_issues
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "passed_sections": len(quality_results),
                "quality_issues": len(quality_issues),
                "average_quality": sum(r.quality_score for r in quality_results.values()) / len(quality_results) if quality_results else 0
            }
            
            logger.info(f"质量检查完成，{len(quality_results)}个章节通过检查")
            return quality_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"质量检查失败: {str(e)}")
            raise
    
    async def _execute_content_integration(
        self,
        quality_results: Dict[str, GenerationResult],
        document_plan: DocumentPlan
    ) -> Dict[str, GenerationResult]:
        """执行内容整合阶段"""
        self.current_stage = GenerationStage.CONTENT_INTEGRATION
        step = WorkflowStep(
            step_id="content_integration",
            name="内容整合",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行内容整合")
            
            # 按照文档计划的顺序整合内容
            generation_order = document_plan.get_generation_order()
            integrated_results = {}
            
            for section_id in generation_order:
                if section_id in quality_results:
                    result = quality_results[section_id]
                    # 这里可以添加内容整合逻辑，如添加章节间的连接
                    integrated_results[section_id] = result
            
            self.results["integrated_results"] = integrated_results
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"integrated_sections": len(integrated_results)}
            
            logger.info(f"内容整合完成，整合了{len(integrated_results)}个章节")
            return integrated_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"内容整合失败: {str(e)}")
            raise
    
    async def _execute_result_finalization(
        self,
        integrated_results: Dict[str, GenerationResult],
        document_plan: DocumentPlan
    ) -> Dict[str, GenerationResult]:
        """执行结果最终化阶段"""
        self.current_stage = GenerationStage.RESULT_FINALIZATION
        step = WorkflowStep(
            step_id="result_finalization",
            name="结果最终化",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行结果最终化")
            
            # 最终化处理
            final_results = {}
            for section_id, result in integrated_results.items():
                # 添加最终化标记
                result.is_finalized = True
                result.finalized_at = datetime.now(timezone.utc)
                final_results[section_id] = result
            
            self.results["final_results"] = final_results
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"finalized_sections": len(final_results)}
            
            logger.info(f"结果最终化完成，最终化了{len(final_results)}个章节")
            return final_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"结果最终化失败: {str(e)}")
            raise

    def _select_generation_strategy(
        self,
        section: SectionPlan,
        generation_config: Dict[str, Any]
    ) -> GenerationStrategy:
        """选择生成策略"""
        # 根据章节类型选择最适合的生成策略
        strategy_mapping = {
            ContentType.API_REFERENCE: GenerationStrategy.STRUCTURED,
            ContentType.INSTALLATION: GenerationStrategy.STRUCTURED,
            ContentType.CONFIGURATION: GenerationStrategy.STRUCTURED,
            ContentType.FEATURES: GenerationStrategy.STRUCTURED,
            ContentType.INTRODUCTION: GenerationStrategy.NARRATIVE,
            ContentType.OVERVIEW: GenerationStrategy.NARRATIVE,
            ContentType.TUTORIAL: GenerationStrategy.NARRATIVE,
            ContentType.USAGE: GenerationStrategy.NARRATIVE,
            ContentType.ARCHITECTURE: GenerationStrategy.TECHNICAL,
            ContentType.DEVELOPMENT: GenerationStrategy.TECHNICAL,
            ContentType.DEPLOYMENT: GenerationStrategy.TECHNICAL,
            ContentType.TESTING: GenerationStrategy.TECHNICAL,
            ContentType.MARKETING: GenerationStrategy.CREATIVE,
            ContentType.HIGHLIGHTS: GenerationStrategy.CREATIVE,
            ContentType.INNOVATION: GenerationStrategy.CREATIVE
        }

        # 优先使用配置中指定的策略
        if section.generation_strategy and section.generation_strategy != "default":
            try:
                return GenerationStrategy(section.generation_strategy)
            except ValueError:
                pass

        # 根据章节类型映射
        return strategy_mapping.get(section.section_type, GenerationStrategy.STRUCTURED)

    async def _create_generation_task(
        self,
        section: SectionPlan,
        strategy: GenerationStrategy,
        document_plan: DocumentPlan,
        generation_config: Dict[str, Any]
    ):
        """创建生成任务"""
        # 准备生成请求
        request = GenerationRequest(
            request_id=f"{document_plan.plan_id}_{section.section_id}",
            content_type=section.section_type,
            strategy=strategy,
            template_name=section.template_name,
            context_data={
                "project_name": document_plan.planning_context.project_name,
                "project_type": document_plan.planning_context.project_type,
                "target_audience": document_plan.planning_context.target_audience,
                "section_title": section.title,
                "section_description": section.description,
                "key_points": section.key_points,
                "content_guidelines": section.content_guidelines,
                **generation_config.get("context_data", {})
            },
            generation_params={
                "target_length": section.estimated_length,
                "complexity_level": section.complexity_level,
                **section.generation_params,
                **generation_config.get("generation_params", {})
            },
            quality_requirements=section.quality_requirements,
            constraints=generation_config.get("constraints", {})
        )

        # 获取对应的生成器
        generator = self.generators[strategy]

        # 准备输入数据
        input_data = {
            "parameters": {
                "request_id": request.request_id,
                "content_type": request.content_type.value,
                "strategy": request.strategy.value,
                "template_name": request.template_name,
                "context_data": request.context_data,
                "generation_params": request.generation_params,
                "quality_requirements": request.quality_requirements,
                "constraints": request.constraints
            }
        }

        # 准备输入并处理
        agent_input = await generator.prepare_input(input_data)
        agent_output = await generator.process(agent_input)

        return agent_output.response.result

    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            "workflow_id": self.workflow_id,
            "status": self.workflow_status.value,
            "current_stage": self.current_stage.value if self.current_stage else None,
            "steps": [step.model_dump() for step in self.steps],
            "results_summary": {
                "has_generator_assignments": "generator_assignments" in self.results,
                "has_generation_results": "generation_results" in self.results,
                "has_quality_results": "quality_results" in self.results,
                "has_integrated_results": "integrated_results" in self.results,
                "has_final_results": "final_results" in self.results,
                "total_generated": len(self.results.get("final_results", {}))
            }
        }
