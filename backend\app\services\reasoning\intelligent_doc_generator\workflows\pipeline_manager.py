"""
流水线管理器

协调和管理整个智能文档生成的完整流水线，包括：
- 规划阶段
- 生成阶段  
- 优化阶段
- 流程监控
- 错误处理
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

from .planning_workflow import PlanningWorkflow
from .generation_workflow import GenerationWorkflow
from .refinement_workflow import RefinementWorkflow
from ..models.planning_models import DocumentPlan
from ..models.generation_models import GenerationResult
from ..models.workflow_models import WorkflowStep, WorkflowResult, WorkflowStatus

logger = logging.getLogger(__name__)


class PipelineStage(str, Enum):
    """流水线阶段枚举"""
    INITIALIZATION = "initialization"
    PLANNING = "planning"
    GENERATION = "generation"
    REFINEMENT = "refinement"
    FINALIZATION = "finalization"


class PipelineStatus(str, Enum):
    """流水线状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PipelineManager:
    """智能文档生成流水线管理器"""
    
    def __init__(self):
        """初始化流水线管理器"""
        self.pipeline_id = str(uuid.uuid4())
        self.current_stage = None
        self.pipeline_status = PipelineStatus.NOT_STARTED
        self.steps: List[WorkflowStep] = []
        self.results: Dict[str, Any] = {}
        
        # 初始化工作流
        self.planning_workflow = PlanningWorkflow()
        self.generation_workflow = GenerationWorkflow()
        self.refinement_workflow = RefinementWorkflow()
        
        # 流水线配置
        self.config = {}
        self.start_time = None
        self.end_time = None
        
    async def execute_pipeline(
        self,
        project_path: str,
        project_name: str,
        pipeline_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行完整的文档生成流水线"""
        try:
            self.pipeline_status = PipelineStatus.RUNNING
            self.start_time = datetime.now(timezone.utc)
            self.config = pipeline_config
            
            logger.info(f"开始执行文档生成流水线: {self.pipeline_id}")
            
            # 阶段1: 初始化
            await self._execute_initialization(project_path, project_name, pipeline_config)
            
            # 阶段2: 规划阶段
            document_plan = await self._execute_planning_stage(project_path, project_name, pipeline_config)
            
            # 阶段3: 生成阶段
            generation_results = await self._execute_generation_stage(document_plan, pipeline_config)
            
            # 阶段4: 优化阶段
            refined_results = await self._execute_refinement_stage(generation_results, pipeline_config)
            
            # 阶段5: 最终化
            final_output = await self._execute_finalization(document_plan, refined_results, pipeline_config)
            
            self.pipeline_status = PipelineStatus.COMPLETED
            self.end_time = datetime.now(timezone.utc)
            
            logger.info(f"文档生成流水线执行完成: {self.pipeline_id}")
            
            return final_output
            
        except Exception as e:
            self.pipeline_status = PipelineStatus.FAILED
            self.end_time = datetime.now(timezone.utc)
            logger.error(f"文档生成流水线执行失败: {str(e)}")
            raise
    
    async def _execute_initialization(
        self,
        project_path: str,
        project_name: str,
        pipeline_config: Dict[str, Any]
    ):
        """执行初始化阶段"""
        self.current_stage = PipelineStage.INITIALIZATION
        step = WorkflowStep(
            step_id="initialization",
            name="流水线初始化",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行流水线初始化")
            
            # 验证输入参数
            validation_result = await self._validate_pipeline_inputs(project_path, project_name, pipeline_config)
            if not validation_result["is_valid"]:
                raise ValueError(f"输入参数验证失败: {validation_result['errors']}")
            
            # 准备工作环境
            await self._prepare_pipeline_environment(pipeline_config)
            
            # 初始化监控
            await self._initialize_monitoring()
            
            self.results["initialization"] = {
                "project_path": project_path,
                "project_name": project_name,
                "config": pipeline_config,
                "validation": validation_result
            }
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"initialized": True}
            
            logger.info("流水线初始化完成")
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"流水线初始化失败: {str(e)}")
            raise
    
    async def _execute_planning_stage(
        self,
        project_path: str,
        project_name: str,
        pipeline_config: Dict[str, Any]
    ) -> DocumentPlan:
        """执行规划阶段"""
        self.current_stage = PipelineStage.PLANNING
        step = WorkflowStep(
            step_id="planning_stage",
            name="文档规划阶段",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行文档规划阶段")
            
            # 提取规划配置
            planning_config = pipeline_config.get("planning", {})
            
            # 执行规划工作流
            document_plan = await self.planning_workflow.execute_planning_workflow(
                project_path, project_name, planning_config
            )
            
            self.results["document_plan"] = document_plan
            self.results["planning_workflow_status"] = self.planning_workflow.get_workflow_status()
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "plan_id": document_plan.plan_id,
                "sections_count": len(document_plan.sections),
                "strategy": document_plan.strategy.value
            }
            
            logger.info(f"文档规划阶段完成，生成{len(document_plan.sections)}个章节规划")
            return document_plan
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"文档规划阶段失败: {str(e)}")
            raise
    
    async def _execute_generation_stage(
        self,
        document_plan: DocumentPlan,
        pipeline_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行生成阶段"""
        self.current_stage = PipelineStage.GENERATION
        step = WorkflowStep(
            step_id="generation_stage",
            name="内容生成阶段",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行内容生成阶段")
            
            # 提取生成配置
            generation_config = pipeline_config.get("generation", {})
            
            # 执行生成工作流
            generation_results = await self.generation_workflow.execute_generation_workflow(
                document_plan, generation_config
            )
            
            self.results["generation_results"] = generation_results
            self.results["generation_workflow_status"] = self.generation_workflow.get_workflow_status()
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "generated_sections": len(generation_results),
                "total_sections": len(document_plan.sections),
                "success_rate": len(generation_results) / len(document_plan.sections) if document_plan.sections else 0
            }
            
            logger.info(f"内容生成阶段完成，成功生成{len(generation_results)}个章节")
            return generation_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"内容生成阶段失败: {str(e)}")
            raise
    
    async def _execute_refinement_stage(
        self,
        generation_results: Dict[str, GenerationResult],
        pipeline_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行优化阶段"""
        self.current_stage = PipelineStage.REFINEMENT
        step = WorkflowStep(
            step_id="refinement_stage",
            name="内容优化阶段",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行内容优化阶段")
            
            # 检查是否启用优化
            if not pipeline_config.get("enable_refinement", True):
                logger.info("优化阶段已禁用，跳过")
                step.status = WorkflowStatus.COMPLETED
                step.completed_at = datetime.now(timezone.utc)
                step.result = {"skipped": True, "reason": "disabled"}
                return generation_results
            
            # 提取优化配置
            refinement_config = pipeline_config.get("refinement", {})
            
            # 执行优化工作流
            refined_results = await self.refinement_workflow.execute_refinement_workflow(
                generation_results, refinement_config
            )
            
            self.results["refined_results"] = refined_results
            self.results["refinement_workflow_status"] = self.refinement_workflow.get_workflow_status()
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "refined_sections": len(refined_results),
                "overall_quality": self.refinement_workflow.results.get("overall_quality", 0)
            }
            
            logger.info(f"内容优化阶段完成，优化了{len(refined_results)}个章节")
            return refined_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"内容优化阶段失败: {str(e)}")
            raise
    
    async def _execute_finalization(
        self,
        document_plan: DocumentPlan,
        refined_results: Dict[str, GenerationResult],
        pipeline_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行最终化阶段"""
        self.current_stage = PipelineStage.FINALIZATION
        step = WorkflowStep(
            step_id="finalization",
            name="结果最终化",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行结果最终化")
            
            # 组装最终文档
            final_document = await self._assemble_final_document(document_plan, refined_results)
            
            # 生成元数据
            metadata = await self._generate_metadata(document_plan, refined_results, pipeline_config)
            
            # 生成统计信息
            statistics = await self._generate_statistics()
            
            final_output = {
                "document": final_document,
                "metadata": metadata,
                "statistics": statistics,
                "pipeline_info": {
                    "pipeline_id": self.pipeline_id,
                    "execution_time": (self.end_time or datetime.now(timezone.utc) - self.start_time).total_seconds(),
                    "status": self.pipeline_status.value
                }
            }
            
            self.results["final_output"] = final_output
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"finalized": True, "document_sections": len(final_document)}
            
            logger.info("结果最终化完成")
            return final_output
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"结果最终化失败: {str(e)}")
            raise

    async def _validate_pipeline_inputs(
        self,
        project_path: str,
        project_name: str,
        pipeline_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证流水线输入参数"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }

        # 验证项目路径
        if not project_path or not project_path.strip():
            validation_result["errors"].append("项目路径不能为空")
            validation_result["is_valid"] = False

        # 验证项目名称
        if not project_name or not project_name.strip():
            validation_result["errors"].append("项目名称不能为空")
            validation_result["is_valid"] = False

        # 验证配置
        if not isinstance(pipeline_config, dict):
            validation_result["errors"].append("流水线配置必须是字典类型")
            validation_result["is_valid"] = False

        return validation_result

    async def _prepare_pipeline_environment(self, pipeline_config: Dict[str, Any]):
        """准备流水线环境"""
        # 初始化各个工作流
        await self._initialize_workflows()

        # 设置配置
        self._apply_pipeline_config(pipeline_config)

    async def _initialize_workflows(self):
        """初始化工作流"""
        # 工作流已在__init__中初始化
        pass

    def _apply_pipeline_config(self, pipeline_config: Dict[str, Any]):
        """应用流水线配置"""
        # 应用全局配置
        self.config.update(pipeline_config)

    async def _initialize_monitoring(self):
        """初始化监控"""
        # 简化实现
        pass

    async def _assemble_final_document(
        self,
        document_plan: DocumentPlan,
        refined_results: Dict[str, GenerationResult]
    ) -> Dict[str, str]:
        """组装最终文档"""
        final_document = {}

        # 按照规划顺序组装文档
        generation_order = document_plan.get_generation_order()

        for section_id in generation_order:
            if section_id in refined_results:
                section = document_plan.get_section_by_id(section_id)
                if section:
                    final_document[section_id] = {
                        "title": section.title,
                        "content": refined_results[section_id].content,
                        "section_type": section.section_type.value,
                        "order": section.order
                    }

        return final_document

    async def _generate_metadata(
        self,
        document_plan: DocumentPlan,
        refined_results: Dict[str, GenerationResult],
        pipeline_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成元数据"""
        return {
            "plan_id": document_plan.plan_id,
            "plan_title": document_plan.title,
            "plan_strategy": document_plan.strategy.value,
            "total_sections": len(document_plan.sections),
            "generated_sections": len(refined_results),
            "project_name": document_plan.planning_context.project_name,
            "project_type": document_plan.planning_context.project_type,
            "target_audience": document_plan.planning_context.target_audience,
            "generation_time": datetime.now(timezone.utc).isoformat(),
            "pipeline_config": pipeline_config
        }

    async def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        total_time = 0
        if self.start_time and self.end_time:
            total_time = (self.end_time - self.start_time).total_seconds()
        elif self.start_time:
            total_time = (datetime.now(timezone.utc) - self.start_time).total_seconds()

        return {
            "pipeline_id": self.pipeline_id,
            "total_execution_time": total_time,
            "total_steps": len(self.steps),
            "completed_steps": len([s for s in self.steps if s.status == WorkflowStatus.COMPLETED]),
            "failed_steps": len([s for s in self.steps if s.status == WorkflowStatus.FAILED]),
            "current_stage": self.current_stage.value if self.current_stage else None,
            "pipeline_status": self.pipeline_status.value
        }

    def get_pipeline_status(self) -> Dict[str, Any]:
        """获取流水线状态"""
        return {
            "pipeline_id": self.pipeline_id,
            "status": self.pipeline_status.value,
            "current_stage": self.current_stage.value if self.current_stage else None,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "steps": [step.model_dump() for step in self.steps],
            "workflow_statuses": {
                "planning": self.planning_workflow.get_workflow_status(),
                "generation": self.generation_workflow.get_workflow_status(),
                "refinement": self.refinement_workflow.get_workflow_status()
            }
        }

    async def pause_pipeline(self):
        """暂停流水线"""
        if self.pipeline_status == PipelineStatus.RUNNING:
            self.pipeline_status = PipelineStatus.PAUSED
            logger.info(f"流水线已暂停: {self.pipeline_id}")

    async def resume_pipeline(self):
        """恢复流水线"""
        if self.pipeline_status == PipelineStatus.PAUSED:
            self.pipeline_status = PipelineStatus.RUNNING
            logger.info(f"流水线已恢复: {self.pipeline_id}")

    async def cancel_pipeline(self):
        """取消流水线"""
        self.pipeline_status = PipelineStatus.CANCELLED
        self.end_time = datetime.now(timezone.utc)
        logger.info(f"流水线已取消: {self.pipeline_id}")
