"""
规划工作流

管理文档规划阶段的完整流程，包括：
- 项目分析
- 需求收集
- 结构规划
- 方案评估
- 规划优化
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

from ..models.planning_models import (
    DocumentPlan, SectionPlan, PlanningContext, PlanEvaluation,
    PlanningStrategy, PlanningStatus
)
from ..models.workflow_models import WorkflowStep, WorkflowResult, WorkflowStatus
from ..core.doc_planner import DocumentPlanner

logger = logging.getLogger(__name__)


class PlanningStage(str, Enum):
    """规划阶段枚举"""
    CONTEXT_ANALYSIS = "context_analysis"
    REQUIREMENT_GATHERING = "requirement_gathering"
    STRUCTURE_PLANNING = "structure_planning"
    PLAN_EVALUATION = "plan_evaluation"
    PLAN_OPTIMIZATION = "plan_optimization"
    PLAN_APPROVAL = "plan_approval"


class PlanningWorkflow:
    """规划工作流管理器"""
    
    def __init__(self):
        """初始化规划工作流"""
        self.workflow_id = str(uuid.uuid4())
        self.planner = DocumentPlanner()
        self.current_stage = None
        self.workflow_status = WorkflowStatus.NOT_STARTED
        self.steps: List[WorkflowStep] = []
        self.results: Dict[str, Any] = {}
        
    async def execute_planning_workflow(
        self,
        project_path: str,
        project_name: str,
        planning_config: Dict[str, Any]
    ) -> DocumentPlan:
        """执行完整的规划工作流"""
        try:
            self.workflow_status = WorkflowStatus.RUNNING
            logger.info(f"开始执行规划工作流: {self.workflow_id}")
            
            # 阶段1: 上下文分析
            context = await self._execute_context_analysis(project_path, project_name, planning_config)
            
            # 阶段2: 需求收集
            requirements = await self._execute_requirement_gathering(context, planning_config)
            
            # 阶段3: 结构规划
            initial_plan = await self._execute_structure_planning(context, requirements)
            
            # 阶段4: 方案评估
            evaluation = await self._execute_plan_evaluation(initial_plan, context)
            
            # 阶段5: 规划优化
            optimized_plan = await self._execute_plan_optimization(initial_plan, evaluation, context)
            
            # 阶段6: 方案审批
            final_plan = await self._execute_plan_approval(optimized_plan, evaluation)
            
            self.workflow_status = WorkflowStatus.COMPLETED
            logger.info(f"规划工作流执行完成: {self.workflow_id}")
            
            return final_plan
            
        except Exception as e:
            self.workflow_status = WorkflowStatus.FAILED
            logger.error(f"规划工作流执行失败: {str(e)}")
            raise
    
    async def _execute_context_analysis(
        self,
        project_path: str,
        project_name: str,
        planning_config: Dict[str, Any]
    ) -> PlanningContext:
        """执行上下文分析阶段"""
        self.current_stage = PlanningStage.CONTEXT_ANALYSIS
        step = WorkflowStep(
            step_id="context_analysis",
            name="上下文分析",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行上下文分析")
            
            # 创建规划上下文
            context = PlanningContext(
                project_path=project_path,
                project_name=project_name,
                project_type=planning_config.get("project_type", "unknown"),
                target_audience=planning_config.get("target_audience", "developers"),
                documentation_style=planning_config.get("documentation_style", "technical"),
                language=planning_config.get("language", "zh-CN"),
                max_sections=planning_config.get("max_sections", 20),
                preferred_length=planning_config.get("preferred_length", 5000),
                time_constraints=planning_config.get("time_constraints"),
                custom_sections=planning_config.get("custom_sections", []),
                excluded_sections=planning_config.get("excluded_sections", []),
                required_sections=planning_config.get("required_sections", [])
            )
            
            # 分析项目特征
            project_characteristics = context.get_project_characteristics()
            self.results["project_characteristics"] = project_characteristics
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"context": context.dict(), "characteristics": project_characteristics}
            
            logger.info("上下文分析完成")
            return context
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"上下文分析失败: {str(e)}")
            raise
    
    async def _execute_requirement_gathering(
        self,
        context: PlanningContext,
        planning_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行需求收集阶段"""
        self.current_stage = PlanningStage.REQUIREMENT_GATHERING
        step = WorkflowStep(
            step_id="requirement_gathering",
            name="需求收集",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行需求收集")
            
            # 收集文档需求
            requirements = {
                "functional_requirements": planning_config.get("functional_requirements", []),
                "quality_requirements": planning_config.get("quality_requirements", {}),
                "content_requirements": planning_config.get("content_requirements", {}),
                "format_requirements": planning_config.get("format_requirements", {}),
                "audience_requirements": {
                    "primary_audience": context.target_audience,
                    "technical_level": planning_config.get("technical_level", "intermediate"),
                    "use_cases": planning_config.get("use_cases", [])
                },
                "constraint_requirements": {
                    "time_limit": context.time_constraints,
                    "length_limit": context.preferred_length,
                    "section_limit": context.max_sections
                }
            }
            
            # 验证需求完整性
            validation_result = await self._validate_requirements(requirements)
            requirements["validation"] = validation_result
            
            self.results["requirements"] = requirements
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = requirements
            
            logger.info("需求收集完成")
            return requirements
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"需求收集失败: {str(e)}")
            raise
    
    async def _execute_structure_planning(
        self,
        context: PlanningContext,
        requirements: Dict[str, Any]
    ) -> DocumentPlan:
        """执行结构规划阶段"""
        self.current_stage = PlanningStage.STRUCTURE_PLANNING
        step = WorkflowStep(
            step_id="structure_planning",
            name="结构规划",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行结构规划")
            
            # 确定规划策略
            strategy = self._determine_planning_strategy(context, requirements)
            
            # 创建文档规划
            plan = await self.planner.create_plan(
                document_id=str(uuid.uuid4()),
                context=context,
                strategy=strategy
            )
            
            # 设置规划基本信息
            plan.title = f"{context.project_name} 文档规划"
            plan.description = f"基于{strategy.value}策略的文档规划方案"
            
            self.results["initial_plan"] = plan
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"plan_id": plan.plan_id, "strategy": strategy.value, "sections_count": len(plan.sections)}
            
            logger.info(f"结构规划完成，生成{len(plan.sections)}个章节")
            return plan
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"结构规划失败: {str(e)}")
            raise
    
    async def _execute_plan_evaluation(
        self,
        plan: DocumentPlan,
        context: PlanningContext
    ) -> PlanEvaluation:
        """执行方案评估阶段"""
        self.current_stage = PlanningStage.PLAN_EVALUATION
        step = WorkflowStep(
            step_id="plan_evaluation",
            name="方案评估",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行方案评估")
            
            # 创建评估
            evaluation = plan.create_evaluation("PlanningWorkflow")
            
            # 详细评估分析
            await self._perform_detailed_evaluation(evaluation, plan, context)
            
            self.results["evaluation"] = evaluation
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "overall_score": evaluation.overall_score,
                "is_approved": evaluation.is_approved(),
                "critical_issues": evaluation.get_critical_issues()
            }
            
            logger.info(f"方案评估完成，总分: {evaluation.overall_score:.2f}")
            return evaluation
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"方案评估失败: {str(e)}")
            raise
    
    async def _execute_plan_optimization(
        self,
        plan: DocumentPlan,
        evaluation: PlanEvaluation,
        context: PlanningContext
    ) -> DocumentPlan:
        """执行规划优化阶段"""
        self.current_stage = PlanningStage.PLAN_OPTIMIZATION
        step = WorkflowStep(
            step_id="plan_optimization",
            name="规划优化",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行规划优化")
            
            # 如果评估通过，进行轻度优化
            if evaluation.is_approved():
                optimized_plan = await self.planner.optimize_plan(plan, context)
            else:
                # 如果评估不通过，进行深度优化
                optimized_plan = await self._perform_deep_optimization(plan, evaluation, context)
            
            # 更新版本信息
            version_parts = optimized_plan.version.split('.')
            version_parts[-1] = str(int(version_parts[-1]) + 1)
            optimized_plan.version = '.'.join(version_parts)
            optimized_plan.status = PlanningStatus.MODIFIED
            
            self.results["optimized_plan"] = optimized_plan
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"optimized_plan_id": optimized_plan.plan_id, "version": optimized_plan.version}
            
            logger.info("规划优化完成")
            return optimized_plan
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"规划优化失败: {str(e)}")
            raise
    
    async def _execute_plan_approval(
        self,
        plan: DocumentPlan,
        evaluation: PlanEvaluation
    ) -> DocumentPlan:
        """执行方案审批阶段"""
        self.current_stage = PlanningStage.PLAN_APPROVAL
        step = WorkflowStep(
            step_id="plan_approval",
            name="方案审批",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行方案审批")
            
            # 最终评估
            final_evaluation = plan.create_evaluation("FinalApproval")
            await self._perform_detailed_evaluation(final_evaluation, plan, plan.planning_context)
            
            # 审批决策
            if final_evaluation.is_approved():
                plan.status = PlanningStatus.APPROVED
                approval_result = "approved"
            else:
                plan.status = PlanningStatus.REJECTED
                approval_result = "rejected"
            
            self.results["final_evaluation"] = final_evaluation
            self.results["approval_result"] = approval_result
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {"approval_result": approval_result, "final_score": final_evaluation.overall_score}
            
            logger.info(f"方案审批完成: {approval_result}")
            return plan
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"方案审批失败: {str(e)}")
            raise

    def _determine_planning_strategy(
        self,
        context: PlanningContext,
        requirements: Dict[str, Any]
    ) -> PlanningStrategy:
        """确定规划策略"""
        # 根据项目特征和需求确定最适合的规划策略
        if context.custom_sections or requirements.get("functional_requirements"):
            return PlanningStrategy.USER_DEFINED
        elif context.project_type in ["web", "api", "library"]:
            return PlanningStrategy.DOMAIN_SPECIFIC
        else:
            return PlanningStrategy.ADAPTIVE

    async def _validate_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """验证需求完整性"""
        validation_result = {
            "is_valid": True,
            "missing_items": [],
            "warnings": []
        }

        # 检查必需的需求项
        required_items = ["audience_requirements", "constraint_requirements"]
        for item in required_items:
            if item not in requirements or not requirements[item]:
                validation_result["missing_items"].append(item)
                validation_result["is_valid"] = False

        return validation_result

    async def _perform_detailed_evaluation(
        self,
        evaluation: PlanEvaluation,
        plan: DocumentPlan,
        context: PlanningContext
    ):
        """执行详细评估"""
        # 这里可以集成更复杂的评估逻辑
        # 目前使用基础评估
        pass

    async def _perform_deep_optimization(
        self,
        plan: DocumentPlan,
        evaluation: PlanEvaluation,
        context: PlanningContext
    ) -> DocumentPlan:
        """执行深度优化"""
        # 根据评估结果进行深度优化
        optimized_plan = await self.planner.optimize_plan(plan, context)

        # 处理关键问题
        critical_issues = evaluation.get_critical_issues()
        for issue in critical_issues:
            if "完整性" in issue:
                # 添加缺失的章节
                await self._add_missing_sections(optimized_plan, context)
            elif "依赖" in issue:
                # 修复依赖问题
                await self._fix_dependency_issues(optimized_plan)

        return optimized_plan

    async def _add_missing_sections(self, plan: DocumentPlan, context: PlanningContext):
        """添加缺失的章节"""
        # 简化实现
        pass

    async def _fix_dependency_issues(self, plan: DocumentPlan):
        """修复依赖问题"""
        # 简化实现
        pass

    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            "workflow_id": self.workflow_id,
            "status": self.workflow_status.value,
            "current_stage": self.current_stage.value if self.current_stage else None,
            "steps": [step.dict() for step in self.steps],
            "results_summary": {
                "has_context": "project_characteristics" in self.results,
                "has_requirements": "requirements" in self.results,
                "has_initial_plan": "initial_plan" in self.results,
                "has_evaluation": "evaluation" in self.results,
                "has_optimized_plan": "optimized_plan" in self.results,
                "approval_result": self.results.get("approval_result")
            }
        }
