"""
优化工作流

管理文档内容优化和精炼阶段的完整流程，包括：
- 质量分析
- 内容优化
- 风格统一
- 一致性检查
- 最终审核
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum

from ..models.generation_models import GenerationResult
from ..models.workflow_models import WorkflowStep, WorkflowResult, WorkflowStatus
from ..templates.refinement_templates import RefinementTemplates, RefinementTemplateType

logger = logging.getLogger(__name__)


class RefinementStage(str, Enum):
    """优化阶段枚举"""
    QUALITY_ANALYSIS = "quality_analysis"
    CONTENT_OPTIMIZATION = "content_optimization"
    STYLE_REFINEMENT = "style_refinement"
    CONSISTENCY_CHECK = "consistency_check"
    FINAL_REVIEW = "final_review"


class RefinementWorkflow:
    """优化工作流管理器"""
    
    def __init__(self):
        """初始化优化工作流"""
        self.workflow_id = str(uuid.uuid4())
        self.current_stage = None
        self.workflow_status = WorkflowStatus.NOT_STARTED
        self.steps: List[WorkflowStep] = []
        self.results: Dict[str, Any] = {}
    
    async def execute_refinement_workflow(
        self,
        generation_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行完整的优化工作流"""
        try:
            self.workflow_status = WorkflowStatus.RUNNING
            logger.info(f"开始执行优化工作流: {self.workflow_id}")
            
            # 阶段1: 质量分析
            quality_analysis = await self._execute_quality_analysis(generation_results, refinement_config)
            
            # 阶段2: 内容优化
            optimized_results = await self._execute_content_optimization(generation_results, quality_analysis, refinement_config)
            
            # 阶段3: 风格精炼
            refined_results = await self._execute_style_refinement(optimized_results, refinement_config)
            
            # 阶段4: 一致性检查
            consistent_results = await self._execute_consistency_check(refined_results, refinement_config)
            
            # 阶段5: 最终审核
            final_results = await self._execute_final_review(consistent_results, refinement_config)
            
            self.workflow_status = WorkflowStatus.COMPLETED
            logger.info(f"优化工作流执行完成: {self.workflow_id}")
            
            return final_results
            
        except Exception as e:
            self.workflow_status = WorkflowStatus.FAILED
            logger.error(f"优化工作流执行失败: {str(e)}")
            raise
    
    async def _execute_quality_analysis(
        self,
        generation_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行质量分析阶段"""
        self.current_stage = RefinementStage.QUALITY_ANALYSIS
        step = WorkflowStep(
            step_id="quality_analysis",
            name="质量分析",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行质量分析")
            
            quality_analysis = {}
            overall_issues = []
            
            for section_id, result in generation_results.items():
                # 分析单个章节的质量问题
                section_analysis = await self._analyze_section_quality(result, refinement_config)
                quality_analysis[section_id] = section_analysis
                
                # 收集需要优化的问题
                if section_analysis["needs_improvement"]:
                    overall_issues.extend(section_analysis["issues"])
            
            # 全局质量分析
            global_analysis = await self._analyze_global_quality(generation_results, refinement_config)
            quality_analysis["global"] = global_analysis
            overall_issues.extend(global_analysis.get("issues", []))
            
            self.results["quality_analysis"] = quality_analysis
            self.results["overall_issues"] = overall_issues
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "analyzed_sections": len(quality_analysis) - 1,  # 减去global
                "total_issues": len(overall_issues),
                "needs_improvement": len([a for a in quality_analysis.values() if isinstance(a, dict) and a.get("needs_improvement", False)])
            }
            
            logger.info(f"质量分析完成，发现{len(overall_issues)}个问题")
            return quality_analysis
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"质量分析失败: {str(e)}")
            raise
    
    async def _execute_content_optimization(
        self,
        generation_results: Dict[str, GenerationResult],
        quality_analysis: Dict[str, Any],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行内容优化阶段"""
        self.current_stage = RefinementStage.CONTENT_OPTIMIZATION
        step = WorkflowStep(
            step_id="content_optimization",
            name="内容优化",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行内容优化")
            
            optimized_results = {}
            optimization_count = 0
            
            for section_id, result in generation_results.items():
                section_analysis = quality_analysis.get(section_id, {})
                
                if section_analysis.get("needs_improvement", False):
                    # 需要优化的章节
                    optimized_result = await self._optimize_section_content(result, section_analysis, refinement_config)
                    optimized_results[section_id] = optimized_result
                    optimization_count += 1
                else:
                    # 不需要优化的章节直接保留
                    optimized_results[section_id] = result
            
            self.results["optimized_results"] = optimized_results
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "total_sections": len(optimized_results),
                "optimized_sections": optimization_count,
                "optimization_rate": optimization_count / len(optimized_results) if optimized_results else 0
            }
            
            logger.info(f"内容优化完成，优化了{optimization_count}个章节")
            return optimized_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"内容优化失败: {str(e)}")
            raise
    
    async def _execute_style_refinement(
        self,
        optimized_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行风格精炼阶段"""
        self.current_stage = RefinementStage.STYLE_REFINEMENT
        step = WorkflowStep(
            step_id="style_refinement",
            name="风格精炼",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行风格精炼")
            
            target_style = refinement_config.get("target_style", "professional")
            refined_results = {}
            refinement_count = 0
            
            for section_id, result in optimized_results.items():
                # 检查是否需要风格调整
                if await self._needs_style_refinement(result, target_style):
                    refined_result = await self._refine_section_style(result, target_style, refinement_config)
                    refined_results[section_id] = refined_result
                    refinement_count += 1
                else:
                    refined_results[section_id] = result
            
            self.results["refined_results"] = refined_results
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "total_sections": len(refined_results),
                "refined_sections": refinement_count,
                "target_style": target_style
            }
            
            logger.info(f"风格精炼完成，精炼了{refinement_count}个章节")
            return refined_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"风格精炼失败: {str(e)}")
            raise
    
    async def _execute_consistency_check(
        self,
        refined_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行一致性检查阶段"""
        self.current_stage = RefinementStage.CONSISTENCY_CHECK
        step = WorkflowStep(
            step_id="consistency_check",
            name="一致性检查",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行一致性检查")
            
            # 检查全局一致性
            consistency_issues = await self._check_global_consistency(refined_results, refinement_config)
            
            # 修正一致性问题
            consistent_results = {}
            correction_count = 0
            
            if consistency_issues:
                for section_id, result in refined_results.items():
                    corrected_result = await self._correct_consistency_issues(result, consistency_issues, refinement_config)
                    if corrected_result != result:
                        correction_count += 1
                    consistent_results[section_id] = corrected_result
            else:
                consistent_results = refined_results
            
            self.results["consistent_results"] = consistent_results
            self.results["consistency_issues"] = consistency_issues
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "total_sections": len(consistent_results),
                "consistency_issues": len(consistency_issues),
                "corrected_sections": correction_count
            }
            
            logger.info(f"一致性检查完成，发现{len(consistency_issues)}个问题，修正了{correction_count}个章节")
            return consistent_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"一致性检查失败: {str(e)}")
            raise
    
    async def _execute_final_review(
        self,
        consistent_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, GenerationResult]:
        """执行最终审核阶段"""
        self.current_stage = RefinementStage.FINAL_REVIEW
        step = WorkflowStep(
            step_id="final_review",
            name="最终审核",
            status=WorkflowStatus.RUNNING,
            started_at=datetime.now(timezone.utc)
        )
        self.steps.append(step)
        
        try:
            logger.info("执行最终审核")
            
            # 最终质量评估
            final_quality_scores = {}
            overall_quality = 0
            
            for section_id, result in consistent_results.items():
                quality_score = await self._evaluate_final_quality(result, refinement_config)
                final_quality_scores[section_id] = quality_score
                overall_quality += quality_score
            
            overall_quality = overall_quality / len(consistent_results) if consistent_results else 0
            
            # 标记为最终版本
            final_results = {}
            for section_id, result in consistent_results.items():
                result.is_final = True
                result.final_quality_score = final_quality_scores[section_id]
                result.reviewed_at = datetime.now(timezone.utc)
                final_results[section_id] = result
            
            self.results["final_results"] = final_results
            self.results["final_quality_scores"] = final_quality_scores
            self.results["overall_quality"] = overall_quality
            
            step.status = WorkflowStatus.COMPLETED
            step.completed_at = datetime.now(timezone.utc)
            step.result = {
                "total_sections": len(final_results),
                "overall_quality": overall_quality,
                "quality_threshold_met": overall_quality >= refinement_config.get("quality_threshold", 0.8)
            }
            
            logger.info(f"最终审核完成，整体质量分数: {overall_quality:.2f}")
            return final_results
            
        except Exception as e:
            step.status = WorkflowStatus.FAILED
            step.error_message = str(e)
            logger.error(f"最终审核失败: {str(e)}")
            raise

    async def _analyze_section_quality(
        self,
        result: GenerationResult,
        refinement_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析单个章节的质量"""
        quality_threshold = refinement_config.get("quality_threshold", 0.8)

        analysis = {
            "quality_score": result.quality_score,
            "needs_improvement": result.quality_score < quality_threshold,
            "issues": [],
            "strengths": []
        }

        # 基础质量检查
        if len(result.content.strip()) < 100:
            analysis["issues"].append("内容过短")

        if result.quality_score < 0.6:
            analysis["issues"].append("整体质量偏低")

        if result.quality_score >= 0.8:
            analysis["strengths"].append("质量良好")

        return analysis

    async def _analyze_global_quality(
        self,
        generation_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析全局质量"""
        total_quality = sum(r.quality_score for r in generation_results.values())
        average_quality = total_quality / len(generation_results) if generation_results else 0

        global_analysis = {
            "average_quality": average_quality,
            "total_sections": len(generation_results),
            "issues": []
        }

        if average_quality < 0.7:
            global_analysis["issues"].append("整体质量需要提升")

        return global_analysis

    async def _optimize_section_content(
        self,
        result: GenerationResult,
        section_analysis: Dict[str, Any],
        refinement_config: Dict[str, Any]
    ) -> GenerationResult:
        """优化章节内容"""
        # 简化实现 - 实际可以调用AI进行内容优化
        optimized_content = result.content

        # 基础优化
        if "内容过短" in section_analysis.get("issues", []):
            optimized_content += "\n\n补充内容以满足长度要求。"

        # 创建优化后的结果
        optimized_result = GenerationResult(
            generation_id=result.generation_id + "_optimized",
            request=result.request,
            content=optimized_content,
            content_type=result.content_type,
            strategy=result.strategy,
            generator_name=result.generator_name,
            quality_score=min(result.quality_score + 0.1, 1.0),  # 略微提升质量分数
            generation_time=result.generation_time,
            created_at=result.created_at,
            updated_at=datetime.now(timezone.utc),
            optimization_applied=True
        )

        return optimized_result

    async def _needs_style_refinement(self, result: GenerationResult, target_style: str) -> bool:
        """检查是否需要风格调整"""
        # 简化实现
        return True  # 假设都需要风格调整

    async def _refine_section_style(
        self,
        result: GenerationResult,
        target_style: str,
        refinement_config: Dict[str, Any]
    ) -> GenerationResult:
        """精炼章节风格"""
        # 简化实现
        refined_result = result
        refined_result.updated_at = datetime.now(timezone.utc)
        return refined_result

    async def _check_global_consistency(
        self,
        refined_results: Dict[str, GenerationResult],
        refinement_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """检查全局一致性"""
        # 简化实现
        return []  # 假设没有一致性问题

    async def _correct_consistency_issues(
        self,
        result: GenerationResult,
        consistency_issues: List[Dict[str, Any]],
        refinement_config: Dict[str, Any]
    ) -> GenerationResult:
        """修正一致性问题"""
        # 简化实现
        return result

    async def _evaluate_final_quality(
        self,
        result: GenerationResult,
        refinement_config: Dict[str, Any]
    ) -> float:
        """评估最终质量"""
        # 简化实现
        return min(result.quality_score + 0.05, 1.0)

    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            "workflow_id": self.workflow_id,
            "status": self.workflow_status.value,
            "current_stage": self.current_stage.value if self.current_stage else None,
            "steps": [step.model_dump() for step in self.steps],
            "results_summary": {
                "has_quality_analysis": "quality_analysis" in self.results,
                "has_optimized_results": "optimized_results" in self.results,
                "has_refined_results": "refined_results" in self.results,
                "has_consistent_results": "consistent_results" in self.results,
                "has_final_results": "final_results" in self.results,
                "overall_quality": self.results.get("overall_quality", 0)
            }
        }
